import logging
import time
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import List

from config import settings
from packages.langfuse.client import StatefulTraceClient
from services.aws import textract

s3BucketName = settings.AWS_S3_BUCKET


def start_job(client, s3_bucket_name, object_name):
    response = client.start_document_text_detection(
        DocumentLocation={
            'S3Object': {
                'Bucket': s3_bucket_name,
                'Name': object_name
            }
        }
    )

    return response["JobId"]


def is_job_complete(client, job_id):
    time_elapsed = 0
    response = client.get_document_text_detection(JobId=job_id)
    status = response["JobStatus"]
    logging.debug(f"Job status: {status} - {job_id}")

    while status == "IN_PROGRESS" and time_elapsed < 15 * 60:
        time.sleep(1)
        time_elapsed += 1
        response = client.get_document_text_detection(JobId=job_id)
        status = response["JobStatus"]
        logging.debug(f"Job status: {status} - {job_id}")

    return status


def get_job_results(client, job_id):
    pages = []
    time.sleep(1)
    response = client.get_document_text_detection(JobId=job_id)
    pages.append(response)
    logging.debug(f"Result page received: {len(pages)} - {job_id}")
    next_token = None
    if 'NextToken' in response:
        next_token = response['NextToken']

    while next_token:
        time.sleep(1)
        response = client. \
            get_document_text_detection(JobId=job_id, NextToken=next_token)
        pages.append(response)
        logging.debug(f"Result page received: {len(pages)} - {job_id}")
        next_token = None
        if 'NextToken' in response:
            next_token = response['NextToken']

    return pages


def run_ocr_bulk(trace: StatefulTraceClient, document_files: List[str], feature_types: List[str] = None, attempts=0):
    retries = attempts or 0
    start = time.time()
    span = trace.span(
        name="Run OCR Bulk",
        input={
            "document_files": document_files,
            "feature_types": feature_types,
            "attempts": retries
        }
    )

    try:
        def call_aws(ix, page_bytes):
            logging.info(f"Bulk OCR processing page {ix + 1}")
            doc = textract.analyze_document(
                Document={'Bytes': page_bytes},
                FeatureTypes=feature_types if feature_types is not None else ["FORMS"]
            )
            return doc['Blocks']

        pages_data = []
        for document_file in document_files:
            with open(document_file, "rb") as file:
                doc_bytes = file.read()
                pages_data.append(doc_bytes)

        logging.info(f"Bulk OCR processing {len(pages_data)} pages...")
        pages = []
        with ThreadPoolExecutor(max_workers=5) as executor:
            results = executor.map(call_aws, [ix for ix in range(len(pages_data))],
                                   [page for page in pages_data])
            for res in results:
                pages.append(res)

        logging.info(f"Bulk OCR processing completed :: {len(pages)} :: {time.time() - start} seconds")

        span.end(output={
            "pages": pages
        })
        return pages
    except Exception as e:
        while retries < 3:
            retries += 1
            logging.error(f"Error processing ocr bulk: {e}")
            logging.error(f"Retrying {retries} of 3")
            return run_ocr_bulk(trace, document_files, feature_types, attempts=retries)
        raise e


def run_ocr(trace: StatefulTraceClient, bucket: str, document_ref: str, attempts=0):
    retries = attempts or 0
    start = time.time()
    span = trace.span(
        id=trace.id,
        name="Run OCR",
        input={
            "bucket": bucket,
            "document_ref": document_ref,
            "attempts": retries
        }
    )
    try:
        pages = []
        job_id = start_job(textract, bucket or s3BucketName, document_ref)
        logging.info(f"OCR started job with id: {job_id}")
        raw_pages = []
        if is_job_complete(textract, job_id):
            raw_pages = get_job_results(textract, job_id)

        for raw_page in raw_pages:
            pages.append(raw_page["Blocks"])

        logging.info(f"OCR processing completed :: {len(pages)} :: {time.time() - start} seconds")
        span.end(output={"pages": pages})
        return pages
    except Exception as e:
        while retries < 3:
            retries += 1
            logging.error(f"Error processing ocr: {e}")
            logging.error(f"Retrying {retries} of 3")
            return run_ocr(trace, bucket, document_ref, attempts=retries)
        raise e

# if __name__ == "__main__":
#     print(run_ocr("referrals/202353521.pdf"))
