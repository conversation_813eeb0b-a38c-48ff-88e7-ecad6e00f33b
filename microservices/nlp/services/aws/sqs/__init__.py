import json
from datetime import datetime

# NOTE: this should remain even if not directly used
# noinspection PyUnresolvedReferences
import sqs_extended_client

from loguru import logger

from comprehend import process_transcript, process_referral, process_qa, process_care_plan, process_compliance, process_correction
from comprehend.utils import synchronize_async_helper
from config import settings
from .consumer import Consumer
from .error import SQSException
from .message import MessageAttributeValue, Message
from .. import sqs
from ...models import ProcessRecording, ProcessReferral, QATask, ProcessCarePlan, ProcessCompliance, ProcessCorrectionRecording

sqs_processing = []


class SQSConsumer(Consumer):

    def __init__(self, queue_url: str = settings.IN_QUEUE):
        super().__init__(
            queue_url=queue_url,
            polling_wait_time_ms=100,
            sqs_client=sqs,
            auto_delete=False,
            message_attribute_names=['All']
        )

    def handle_message(self, message: Message):
        logger.info('Consumer Received')
        body = {"send": True}
        body.update(json.loads(message.Body))
        sqs_processing.append({"body": message.Body})
        index = len(sqs_processing) - 1

        send_result = {}
        try:
            #  Delete the message then re-add if the process fails
            self._delete_message(message)
            pipeline = body.get("pipeline", "transcript")
            ctx = body.get("ctx", {})
            logger.info(f'Processing consumed :: {pipeline} - {ctx}')
            if pipeline == "transcript":
                recording_body = ProcessRecording(**body)
                answers, meta = synchronize_async_helper(process_transcript(recording_body))
                send_result = sqs.send_message(
                    QueueUrl=settings.OUT_QUEUE,
                    MessageBody=json.dumps({
                        "ctx": ctx,
                        "meta": meta,
                        "pipeline": pipeline,
                        "answers": answers,
                        "visit_id": recording_body.visit_id
                    })
                )
            elif pipeline == "referral":
                referral_body = ProcessReferral(**body)
                answers = synchronize_async_helper(process_referral(referral_body))
                send_result = sqs.send_message(
                    QueueUrl=settings.OUT_QUEUE,
                    MessageBody=json.dumps({
                        "ctx": ctx,
                        "id": referral_body.id,
                        "pipeline": pipeline,
                        "answers": answers,
                        "meta": body.get("meta", {})
                    })
                )
            elif pipeline == "qa":
                qa_body = QATask(**body)
                answers = synchronize_async_helper(process_qa(qa_body))
                send_result = sqs.send_message(
                    QueueUrl=settings.OUT_QUEUE,
                    MessageBody=json.dumps({
                        "ctx": ctx,
                        "visit_id": qa_body.visit_id,
                        "pipeline": pipeline,
                        "answers": answers,
                        "meta": body.get("meta", {})
                    })
                )
            elif pipeline == "care_plan":
                care_plan_body = ProcessCarePlan(**body)
                goals = synchronize_async_helper(process_care_plan(care_plan_body))
                send_result = sqs.send_message(
                    QueueUrl=settings.OUT_QUEUE,
                    MessageBody=json.dumps({
                        "ctx": ctx,
                        "episode_id": care_plan_body.episode_id,
                        "pipeline": pipeline,
                        "goals": goals,
                        "meta": body.get("meta", {})
                    })
                )
            elif pipeline == "compliance":
                compliance_body = ProcessCompliance(**body)
                answers = synchronize_async_helper(process_compliance(compliance_body))
                send_result = sqs.send_message(
                    QueueUrl=settings.OUT_QUEUE,
                    MessageBody=json.dumps({
                        "ctx": ctx,
                        "id": compliance_body.id,
                        "pipeline": pipeline,
                        "answers": answers,
                        "meta": body.get("meta", {})
                    })
                )
            elif pipeline == "correction":
                recording_body = ProcessCorrectionRecording(**body)
                answers, meta = synchronize_async_helper(process_correction(recording_body))
                send_result = sqs.send_message(
                    QueueUrl=settings.OUT_QUEUE,
                    MessageBody=json.dumps({
                        "ctx": ctx,
                        "meta": meta,
                        "pipeline": pipeline,
                        "answers": answers,
                        "visit_id": recording_body.visit_id
                    })
                )
            else:
                raise SQSException(f'Invalid pipeline: {pipeline}')

            logger.debug(f'SQS message sent :: {send_result.get("MessageId", "__")}')
        except Exception as e:
            if settings.PATRIUM_ENV == 'local':
                raise e
            retries = body.get("retries", 0)
            logger.error(f'Consumer Error :: {retries} :: {e}')
            if retries <= 3:
                sqs.send_message(
                    QueueUrl=settings.IN_QUEUE,
                    MessageBody=json.dumps({
                        **body,
                        "retries": retries + 1
                    })
                )

            logger.debug(f'Re-added job back into queue:: {retries} :: {body.get("visit_id")}')
        finally:
            # remove from processing queue
            sqs_processing.pop(index)
            logger.debug(f'Consumer Complete')


def start_consumer(csm: SQSConsumer):
    logger.info('Consumer Started')
    csm.start()


def stop_consumer(csm: SQSConsumer):
    logger.info(f'Consumer Stopping, cleaning active processes - {len(sqs_processing)}')
    for message in sqs_processing:
        sqs.send_message(
            QueueUrl=settings.OUT_QUEUE,
            MessageBody=message.get("body")
        )
    csm.stop()
    logger.info(f'Consumer Stopped - {datetime.now()} ')
