import boto3

# NOTE: this should remain even if not directly used
import sqs_extended_client

from config import settings

aws_session = boto3.Session(
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
    region_name=settings.AWS_REGION
)

s3 = aws_session.client('s3')

sqs_extended_client = aws_session.client('sqs')
sqs_extended_client.large_payload_support = settings.AWS_S3_BUCKET
sqs_extended_client.use_legacy_attribute = False
sqs_extended_client.always_through_s3 = False
sqs = sqs_extended_client

textract = aws_session.client('textract')

