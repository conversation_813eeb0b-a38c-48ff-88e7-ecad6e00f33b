from loguru import logger

from config import settings
from services.aws import s3

s3BucketName = settings.AWS_S3_BUCKET


def save_to_s3(file_name: str, data: str, bucket: str = s3BucketName):
    response = s3.put_object(Bucket=bucket or s3BucketName, Key=file_name, Body=data)
    return response


def get_s3_url(path: str, bucket: str = s3BucketName):
    # Generate a pre-signed URL that allows read-only access to the object
    logger.info(f"Generating presigned url for :: {bucket or s3BucketName} / {path}")
    url = s3.generate_presigned_url(
        'get_object',
        Params={'Bucket': bucket or s3BucketName, 'Key': path},
        ExpiresIn=3600  # URL will expire in 1 hour
    )
    return url
