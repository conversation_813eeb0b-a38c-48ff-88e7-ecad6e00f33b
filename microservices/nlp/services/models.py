from typing import List, Union, Literal

from pydantic import BaseModel, Field


class ProcessTask(BaseModel):
    transcript: str
    context: list = Field(default_factory=list)
    override: list = Field(default_factory=list)


class MedicationTask(BaseModel):
    urls: List[str]
    bucket: str


class LinkTask(BaseModel):
    ctx: dict = Field(default_factory=dict)
    id: str
    code: str = None
    visit_id: str = None
    episode_id: str = None
    visit_type: str
    snippets: list
    rationale: str = None


class QATask(BaseModel):
    ctx: dict = Field(default_factory=dict)
    visit_id: str
    visit_type: str = None
    live: bool = False
    answers: list = Field(default_factory=list)


class ProcessReferral(BaseModel):
    ctx: dict = Field(default_factory=dict)
    id: str
    service_line: str
    documents: List[str]
    source: str = 'aws'
    bucket: str = 'patrium-health-production'


class ProcessCompliance(BaseModel):
    ctx: dict = Field(default_factory=dict)
    id: str
    service_line: str
    documents: List[str]
    data: List[dict]
    questions: List[str]
    source: str = 'aws'
    bucket: str = 'patrium-health-production'


class ProcessCarePlan(BaseModel):
    ctx: dict = Field(default_factory=dict)
    episode_id: str
    goals: List[dict]
    responses: dict
    conditions: List[dict]


class ProcessRecording(BaseModel):
    ctx: dict = Field(default_factory=dict)
    recording: str
    code: str = None
    context: list = Field(default_factory=list)
    send: bool = False
    secondary: list = Field(default_factory=list)
    answered: list = Field(default_factory=list)
    visit_id: str = None
    visit_type: str = None
    agency_id: str = None
    episode_id: str = None
    patient_id: str = None
    override: list = Field(default_factory=list)
    # mimetype: str = None


class ProcessSandbox(BaseModel):
    ctx: dict = Field(default_factory=dict)
    id: str
    field: str | dict
    visit_type: str
    snippets: list


class ProcessGenerate(BaseModel):
    ctx: dict = Field(default_factory=dict)
    id: str
    message: str
    data: dict
    context: dict


class LoadQuestions(BaseModel):
    sync: bool = True
    type: Union[Literal['SocChecklist'], Literal['Checklist'], Literal['Referral']] = 'SocChecklist'
    questions: list = Field(default_factory=list)


class ProcessCorrectionRecording(BaseModel):
    ctx: dict = Field(default_factory=dict)
    recording: str
    code: str = None
    correction_id: str = None
    visit_id: str = None
    visit_type: str = None
    agency_id: str = None
    episode_id: str = None
    patient_id: str = None

ProcessTask.model_rebuild()
ProcessRecording.model_rebuild()
ProcessCorrectionRecording.model_rebuild()
