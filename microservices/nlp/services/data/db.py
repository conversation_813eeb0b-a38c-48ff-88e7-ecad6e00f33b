import json
from typing import List

from loguru import logger
from sqlalchemy import text
from sqlalchemy.orm import Session

from packages.langfuse.client import StatefulSpanClient
from services.data.session import get_db

database = "patrium"

tables = {
    "referral": {
        "table": json.dumps("Referrals"),
        "filter": "AND deleted_at IS NULL"
    },
    "referral_responses": {
        "table": json.dumps("Referral_responses"),
        "filter": "AND deleted_at IS NULL"
    },
    "visits": {
        "table": json.dumps("Visits"),
        "filter": "AND deleted_at IS NULL"
    },
    "patients": {
        "table": json.dumps("Patients"),
        "filter": "AND deleted_at IS NULL"
    },
    "responses": {
        "table": json.dumps("Visit_assessment_responses"),
        "filter": "AND deleted_at IS NULL",
    },
    "empty_qa_responses": {
        "table": json.dumps("Visit_assessment_responses"),
        "filter": "AND deleted_at IS NULL AND qa IS NULL",
    }
}


def format_value(value):
    if isinstance(value, dict):
        # Handle operator dict format: {"op": ">=", "value": 10}
        if "op" in value and "value" in value:
            op = value["op"]
            val = value["value"]
            if op == "IN" and isinstance(val, (list, tuple)):
                # Format IN operator: (value1, value2, value3)
                formatted_vals = [format_value(v) for v in val]
                return f"({', '.join(formatted_vals)})"
            elif op == "BETWEEN" and isinstance(val, (list, tuple)) and len(val) == 2:
                # Format BETWEEN operator: 'start_value' AND 'end_value'
                formatted_vals = [format_value(v) for v in val]
                return f"{formatted_vals[0]} AND {formatted_vals[1]}"
            return format_value(val)
        return value.get("data", "")

    # if is list or tuple process each value
    if isinstance(value, str):
        clean_value = value.replace("'", r"\'")
        return f"'{clean_value}'"
    if isinstance(value, int):
        return str(value)
    # if isinstance(value, dict):
    #     if value["$sub"]:
    #         return ','.join([f"{format_value(k)}" for k in value['$q']])
    #     return value
    if isinstance(value, (list, tuple)):
        return [format_value(v) for v in value]
    return value


def db_request(
        span: StatefulSpanClient,
        ref: str,
        operation: str = 'get',
        id: str = None,
        query: dict = None,
        data: dict = None,
        select: List[str] = None,
        limit: int = 1000,
        joins: List[dict] = None,
        order: str = None):
    table_ref = tables[ref]

    span = span.span(
        trace_id=span.trace_id,
        name="DB Request",
        input={
            "id": id,
            "data": data,
            "query": query,
            "select": select,
            "table_ref": table_ref,
            "operation": operation,
            "joins": joins,
            "order": order,
        }
    )

    # Connect to the database
    conn: Session
    with get_db() as conn:
        logger.debug(50 * '-')
        logger.debug(f'Processing DB operation :: {table_ref["table"]} - {operation} - {id} - {data} - {query}')

        col_names = select
        if col_names is None:
            # Get the column names
            col_q = text(
                f"""SELECT column_name FROM information_schema.columns WHERE table_name = '{table_ref['table'].replace('"', '')}'""")
            col_result = conn.execute(col_q)
            col_names = [row[0] for row in col_result.fetchall()]
            logger.debug(f'Column Names :: {col_names}')

        if operation == 'get':
            q = text(
                f"SELECT * FROM {table_ref['table']} WHERE id = '{id}' {table_ref['filter']} {table_ref.get('order', '')}")
            logger.debug(f'DB :: Get :: {q}')
            results = conn.execute(q).columns(*[col for col in col_names])
            rows = results.fetchall()
        elif operation == 'indexed_get':
            q = text(
                f"SELECT * FROM {table_ref['table']} WHERE {' AND '.join([f'{k} = {format_value(v)}' for k, v in data.items()])} {table_ref['filter']} {table_ref.get('order', '')}")
            logger.debug(f'DB :: Indexed Get :: {q}')
            results = conn.execute(q).columns(*[col for col in col_names])
            rows = results.fetchall()
        elif operation == 'list':
            # Build JOIN clause if joins are specified
            join_clause = ""
            if joins:
                for join in joins:
                    join_type = join.get('type', 'INNER')
                    join_table = json.dumps(join['table'])
                    join_condition = join['on']
                    join_clause += f" {join_type} JOIN {join_table} ON {join_condition}"
            
            list_filter = f"""WHERE {' AND '.join([f"{k} {v.get('op', '=') if isinstance(v, dict) else '='} {format_value(v)}" for k, v in query.items()])}"""
            order_clause = f" {order}" if order else f" {table_ref.get('order', '')}"
            q = text(f"SELECT * FROM {table_ref['table']}{join_clause} {list_filter}{order_clause} LIMIT {limit}")
            logger.debug(f'DB :: List :: {q}')
            results = conn.execute(q).columns(*[col for col in col_names])
            rows = results.fetchall()
        elif operation == 'create':
            formatted_values = format_value(list(data.values()))
            q = text(
                f"INSERT INTO {table_ref['table']} ({', '.join(data.keys())}) VALUES ({', '.join(formatted_values)})")
            logger.debug(f'DB :: Insert :: {q}')
            conn.execute(q)
            return []
        elif operation == 'update':
            filter = f"WHERE id = '{id}'"

            if id is None:
                filter = f"WHERE {'AND '.join([f'{k} = {format_value(v)}' for k, v in query.items()])}"

            q = text(
                f"UPDATE {table_ref['table']} SET {', '.join([f'{k} = {format_value(v)}' for k, v in data.items()])} {filter}")
            logger.debug(f'DB :: Update :: {q}')
            conn.execute(q)
            return []
        elif operation == 'delete':
            q = text(f"DELETE FROM {table_ref['table']} WHERE id = '{id}'")
            logger.debug(f'DB :: Delete :: {q}')
            conn.execute(q)
            return []

        logger.debug(f'Retrieved Rows :: {operation} :: {len(rows)}')

    json_output = []
    for row in rows:
        json_output.append(dict(zip(col_names, row)))

    is_get = operation in ['get', 'indexed_get']
    if len(json_output) > 0:
        result = json_output[0] if is_get else json_output
    else:
        result = None if is_get else json_output

    span.end(output={
        "sql": str(q),
        "result": result
    })

    return result

# if __name__ == "__main__":
#     retrieve('responses', 'get', id='10')
#     db_request('visits', 'list', limit=4)
