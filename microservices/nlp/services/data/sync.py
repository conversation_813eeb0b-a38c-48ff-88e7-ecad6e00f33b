import datetime
import json
import os
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from typing import Literal

from loguru import logger

from comprehend.utils import process_json_text, clone_object
from comprehend.utils.constants import FIELD_TYPE_MAP
from config import settings
from services.data.cms import CMS

api = CMS(endpoint=settings.DIRECTUS_ENDPOINT, access_token=settings.DIRECTUS_TOKEN)

_in_memory_cache = {}
_background_executor = ThreadPoolExecutor(max_workers=4)
_background_updates = {}

PROD_TTL_TIME = 30
# PROD_TTL_TIME = 480
DEV_TTL_TIME = 30


def get_cache_key(type: str, scope: Literal['ALL', 'QA', 'BASE'] = 'BASE', exclude_referral: bool = False):
    return f"{type}-{scope}-{'NO_REF' if exclude_referral else 'REF'}"


def is_question_valid(item, negate=False):
    check = ((item.get("code", "") == "") or (item.get("question", "") == "")
             or (item.get("criteria", "") == "") or (len(item.get("examples", "")) < 1))
    if negate:
        return not check
    return check


def construct_field(collection, field, source):
    # print(f"Constructing field {field['id']} for source {source['id']}\n")

    types = []
    deps = field.get("dependencies", []) or []
    sections = []
    if collection == 'Steps':
        types = ['CARE_PLAN']
        desc = process_json_text(source.get("description", ""))
        sections = ['step']
    # elif collection == 'Interventions':
    #     types = ['CARE_PLAN']
    #     desc = process_json_text(source.get("description", ""))
    #     sections = ['intervention']
    elif collection == 'Referral_Item':
        types = ['REFERRAL']
        desc = source.get("name", "")
        sections = ['referral']
    else:
        desc = process_json_text(source.get("description", ""))
        sects = source.get("sub_sections", [])
        for sect in sects:
            if sect is not None:
                sect_data = sect.get("sub_section_id")
                if sect_data is not None and sect_data.get('status') == 'published':
                    sections.append(sect_data.get("id"))
                    types.append(sect_data.get("type"))

    types = list(set(types))

    return {
        'code': field.get("code", ""),
        'name': field.get("name", ""),
        'id': field.get("id", ""),
        'types': types,
        'sections': sections,
        'source': field.get("source"),
        'source_id': field.get("source_id") or field.get("source_key"),
        'schema': field.get("schema"),
        'mandatory': field.get("mandatory", False),
        'notes': field.get("notes", ""),
        'enabled': field.get("enabled", False),
        'question': field.get("question", ""),
        'inputType': field.get("type") or field.get("inputType"),
        'criteria': field.get("criteria", ""),
        'examples': field.get("examples", []),
        'options': field.get("options", []),
        'description': desc,
        'dependencies': deps,
        'conditionals': field.get("conditionals", {"conditions": [], "order": ""})
    }


def get_field_details(type, field):
    source_id = field.get("source_id") or field.get("source_key")
    source_ref = field.get("source")

    if source_ref == 'referral':
        source_collection = 'Referral_Item'
        source_fields = [
            'id',
            'name',
            'status'
        ]
    elif source_ref == 'steps':
        source_collection = 'Steps'
        source_fields = [
            'id',
            'type',
            'status',
            'description'
        ]
    elif source_ref == 'interventions':
        return None
    #     source_collection = 'Interventions'
    #     source_fields = [
    #         'id',
    #         'type',
    #         'status',
    #         'description'
    #     ]
    else:
        source_collection = 'Tasks'
        source_fields = [
            'id',
            'type',
            'status',
            'description',
            'sub_sections.sub_section_id.id',
            'sub_sections.sub_section_id.status',
            'sub_sections.sub_section_id.type'
        ]

    # ignore care plan if not requesting for it
    if type != 'CARE_PLAN' and (source_ref == 'steps' or source_ref == 'interventions'):
        return None

    source = api.get_items(
        source_collection,
        limit=2,
        filter_dict={'id': {'_eq': source_id}},
        fields=source_fields)

    if len(source) == 0:
        logger.debug(f"Source {source_id} not found")
        source = {}
    else:
        source = next((x for x in source if x.get('status') == 'published'), {})

    return construct_field(source_collection, field, source)


# def get_sub_section_fields(type: str):
#     sub_sections = api.get_items('Sub_sections', limit=10000, filter_dict={
#         'type': 'SOC_CHECKLIST',
#         'status': 'published'
#     }, fields=['id', 'tasks.task_id.id'])
#
#     tasks = list(set([x for y in sub_sections for x in y.get('tasks', [])]))
#
#     fields = api.get_items('Fields', limit=10000, filter_dict={
#         'code': {'_starts_with': 'QA_'},
#         'source': 'tasks',
#         'status': 'published',
#         'enabled': True,
#         'source_key': {'_in': tasks}
#     }, fields=['source', 'source_key', 'code', 'question', 'schema', 'criteria', 'description', 'type'])
#
#     return fields


def get_fields(type: str, scope: Literal['ALL', 'QA', 'BASE'] = 'BASE', exclude_referral: bool = False):
    type = FIELD_TYPE_MAP.get(type, type)
    logger.info(f"Retrieving fields for type :: {type}")

    limit = 10000

    fields = api.get_items('Fields', limit=limit, filter_dict={
        'status': 'published',
        **(
            {
                '_or': [
                    {'code': {'_starts_with' if scope == 'QA' else '_nstarts_with': 'QA_'}},
                    {'source': {'_eq': 'referral'}}
                ]
            } if scope != 'ALL' else {}
        )
    })

    field_models = []
    with ThreadPoolExecutor(max_workers=50) as executor:
        results = executor.map(get_field_details, [type for _ in fields], fields)
        for result in results:
            if result is not None:
                field_models.append(result)

    # fill in the dependencies with more details
    for field in field_models:
        deps = field.get("dependencies", [])
        for dep in deps:
            found = next((x for x in field_models if x["code"] == dep.get("code")), None)
            if found is not None:
                dep["name"] = found.get("name")
                dep["question"] = found.get("question")
                dep["description"] = found.get("description")

    items = list(
        filter(
            lambda item: (item["enabled"] == True)
                         and ((type in item.get("types", [])) or (
                    not exclude_referral and 'REFERRAL' in item.get("types", []))),
            field_models)
    )

    if scope != 'ALL':
        items = list(
            filter(lambda item: scope == 'QA' if (item.get("code").startswith("QA_")) else not scope == 'QA', items))

    return items


def is_cache_valid(
        type: str,
        scope: Literal['ALL', 'QA', 'BASE'] = 'BASE',
        exclude_referral: bool = False,
        cache_ttl_in_mins=PROD_TTL_TIME if settings.PATRIUM_ENV == 'prod' else DEV_TTL_TIME):
    cache_key = get_cache_key(type, scope, exclude_referral)
    """ Checks if the cache is valid based on the cache TTL """

    cache_data = {}
    if cache_key in _in_memory_cache:
        cache_data = _in_memory_cache.get(cache_key, {})
    else:
        # check file cache
        cached_type_dir = os.path.join('_cache', cache_key)
        if os.path.exists(cached_type_dir):
            files = [f for f in os.listdir(cached_type_dir) if f.endswith('.json')]

            if files:
                latest_file = max(files)
                cache_data = { "time": float(latest_file.split('.')[0]) }

    if cache_data.get("time") is None:
        return False

    timestamp = datetime.datetime.now().timestamp()
    cache_age = timestamp - cache_data.get("time", 0)
    return cache_age < cache_ttl_in_mins * 60 * 60


def cached_question_retriever(
        type: str,
        scope: Literal['ALL', 'QA', 'BASE'] = 'BASE',
        exclude_referral: bool = False,
        cache_ttl_in_mins=PROD_TTL_TIME if settings.PATRIUM_ENV == 'prod' else DEV_TTL_TIME,
        cache_directory='_cache'
):
    """ Loads questions into memory from the cms while being backed up by the file system """
    global _in_memory_cache, _background_updates

    ttl = cache_ttl_in_mins * 60 * 60
    timestamp = datetime.datetime.now().timestamp()
    cache_key = get_cache_key(type, scope, exclude_referral)

    def update_cache():
        try:
            new_data = get_fields(type=type, scope=scope, exclude_referral=exclude_referral)
            cached_type_dir = os.path.join(cache_directory, cache_key)

            if not os.path.exists(cached_type_dir):
                os.makedirs(cached_type_dir)

            # Save to file cache
            with open(os.path.join(cached_type_dir, f'{timestamp}.json'), 'w') as f:
                json.dump(new_data, f, indent=2)

            # Update memory cache
            _in_memory_cache[cache_key] = {
                "time": timestamp,
                "data": new_data
            }

            # Cleanup old cache files
            files = [f for f in os.listdir(cached_type_dir) if f.endswith('.json')]
            if len(files) > 2:
                to_remove = sorted(files)[:-2]  # Keep newest 2 files
                for file in to_remove:
                    os.remove(os.path.join(cached_type_dir, file))

            logger.info(f"Background cache update completed for {cache_key} :: {len(new_data)} items")
        except Exception as e:
            logger.error(f"Background cache update failed: {e}")
        finally:
            _background_updates.pop(cache_key, None)

    # First, try to get from memory cache
    if cache_key in _in_memory_cache:
        cache_data = _in_memory_cache[cache_key]
        cache_age = timestamp - cache_data["time"]

        # If cache is expired and no background update is running, start one
        if cache_age > ttl and cache_key not in _background_updates:
            logger.info(f"Starting background cache update for {cache_key}")
            _background_updates[cache_key] = _background_executor.submit(update_cache)

        return clone_object(cache_data["data"])

    # If not in memory, check file cache
    cached_type_dir = os.path.join(cache_directory, cache_key)
    if os.path.exists(cached_type_dir):
        files = [f for f in os.listdir(cached_type_dir) if f.endswith('.json')]

        if files:
            latest_file = max(files)
            with open(os.path.join(cached_type_dir, latest_file), 'r') as f:
                try:
                    data = json.load(f)
                except json.JSONDecodeError:
                    logger.error(f"Failed to load cache file: {latest_file}")
                    data = None

            if data is None:
                logger.info(f"Cache file is empty, performing blocking update for {cache_key}")
                update_cache()
                return clone_object(_in_memory_cache[cache_key]["data"])

            # Store in memory cache
            _in_memory_cache[cache_key] = {
                "time": float(latest_file.split('.')[0]),
                "data": data
            }

            # Start background update if cache is expired
            cache_age = timestamp - float(latest_file.split('.')[0])
            if cache_age > ttl and cache_key not in _background_updates:
                logger.info(f"Starting background cache update for {cache_key}")
                _background_updates[cache_key] = _background_executor.submit(update_cache)

            return clone_object(data)

    # If no cache exists, do a blocking update
    logger.info(f"No cache exists, performing blocking update for {cache_key}")
    update_cache()
    return clone_object(_in_memory_cache[cache_key]["data"])
