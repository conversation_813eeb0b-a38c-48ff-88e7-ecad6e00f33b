from contextlib import contextmanager
from functools import lru_cache
from typing import Context<PERSON>anager, Generator

from sqlalchemy import create_engine
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session, scoped_session, sessionmaker

from config import settings


@lru_cache
def get_db_engine() -> Engine:
    return create_engine(
        str(settings.DATABASE_URL),
        echo=False,
        pool_pre_ping=True,
        pool_size=500,
        max_overflow=250
    )


def get_session(engine: "Engine") -> ContextManager[Session]:
    session_factory = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine,
        future=True,
    )
    Session_ = scoped_session(session_factory)
    return Session_()


@contextmanager
def get_db() -> Generator[Session, None, None]:
    engine = get_db_engine()
    session = get_session(engine)
    yield session
