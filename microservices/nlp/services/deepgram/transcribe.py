import json
import os
import time
from typing import Text

from deepgram import Deepgram
from deepgram._types import PrerecordedOptions
from loguru import logger

from config import settings
from services.deepgram.utils import get_ext_from_url

UPPER_LIMIT = 80
LOWER_LIMIT = 50

dir_path = os.path.dirname(os.path.realpath(__file__))
with open(os.path.join(dir_path, 'variations.json'), 'rb') as f:
    code_variations = json.load(f)
from comprehend.lib.base_llm import call_llm
from comprehend.utils import extract_json, seconds_to_time_string


def identify_clinician(paragraphs: list[dict]):
    """Snippet the conversation and identify the speakers in the transcript with an LLM"""

    conversation = ""
    for pg in paragraphs[:10]:
        conversation += f"{pg['speaker']}: {' '.join(pg['sentences'])}\n"

    prompt = f"""You are a transcript annotator. You will be provided with a transcript of a conversation
between a clinician and a patient / caregiver. Your task is to identify the clinician in the transcript.
Respond with a json object with a rationale and answer field. The rationale should give a step by step
deduction of the given information to get the right answer. If you cannot determine teh speaker, respond with -1.

Example Response:
{json.dumps({
        "rationale": "Speaker 0 highlights information about the care process for patients, hence is the clinician",
        "answer": 0
    }, indent=2)}


Transcript:

{conversation}

Answer:
"""

    result = call_llm(prompt=prompt)
    result = extract_json(result)
    return int(result.get("answer"))


def group_content(transcript: dict, max_sentences: int = 6):
    """Groups the transcript snippets from the same speaker into paragraphs with a maximum number of sentences"""
    paragraphs = transcript.get("paragraphs", {}).get("paragraphs", [])
    grouped = []

    if len(paragraphs) == 0:
        return grouped

    current_group = {
        "speaker": paragraphs[0]["speaker"],
        "sentences": [],
        "start": paragraphs[0]["start"],
        "num_words": 0,
        "end": paragraphs[0]["end"],
        "timestamp": seconds_to_time_string(paragraphs[0]["start"])
    }

    sentence_count = 0
    for pg in paragraphs:
        # Get sentences from current paragraph
        new_sentences = [s["text"] for s in pg["sentences"]]

        # If same speaker and under max sentences, add to current group
        if pg["speaker"] == current_group["speaker"] and (sentence_count + len(new_sentences)) <= max_sentences:
            current_group["sentences"].extend(new_sentences)
            current_group["num_words"] += pg["num_words"]
            current_group["end"] = pg["end"]
            sentence_count += len(new_sentences)
        else:
            # Different speaker or max sentences reached, add current group and start new one
            if len(current_group["sentences"]) > 0:
                # Only append if there are sentences
                grouped.append(current_group)

            current_group = {
                "speaker": pg["speaker"],
                "sentences": new_sentences,
                "start": pg["start"],
                "num_words": pg["num_words"],
                "end": pg["end"],
                "timestamp": seconds_to_time_string(pg["start"])
            }
            sentence_count = len(new_sentences)

    # Add the last group
    # Only append if there are sentences
    if current_group["sentences"]:
        grouped.append(current_group)

    clinician = identify_clinician(grouped)
    for pg in grouped:
        pg["sentences"] = replace_code_likes(' '.join(pg["sentences"]))
    for pg in grouped:
        if clinician != -1:
            if pg["speaker"] == clinician:
                pg["speaker"] = "CLINICIAN"
            else:
                pg["speaker"] = "PATIENT/CAREGIVER"
        else:  # Couldn't identify clinician, use default Speaker X format
            pg["speaker"] = f"Speaker {pg['speaker']}"

    return grouped


def replace_code_likes(transcript: str):
    coded_transcript = transcript.lower()
    for code_variation in code_variations:
        code = code_variation['code']
        variations = code_variation['variations']

        for variation in variations:
            coded_transcript = coded_transcript.replace(variation.lower(), code)

    return coded_transcript


def process_response(response: dict | None):
    if response is None:
        return []

    channel = response.get('results').get('channels')[0].get('alternatives')[0]
    words = channel.get("words", [])
    transcript = group_content(channel)

    # replace speaker identifiers accordingly

    if len(transcript) > 0:
        speaker_0 = transcript[0].get('speaker')
        speaker_X = 'CLINICIAN' if speaker_0 == 'CLINICIAN' else 'PATIENT/CAREGIVER'

        def check(w):
            if w.get('speaker') == 0:
                return speaker_0
            return speaker_X

        words = [dict(w, speaker=check(w)) for w in words]

    return transcript, words


async def call_dg(source, file_type: str):
    dg_client = Deepgram(settings.DEEPGRAM_API_KEY)

    logger.debug(f'Transcribing file of type {file_type} ...')
    start = time.time()
    response = await dg_client.transcription.prerecorded(source, PrerecordedOptions(
        punctuate=True,
        smart_format=True,
        paragraphs=True,
        diarize="true",
        numbers=True,
        numerals=True,
        model='nova-3-medical',
        keywords=[]
    ))
    time_taken = round(time.time() - start, 2)
    logger.debug(f'Transcription complete in {time_taken} seconds ...')

    return response


async def transcribe(recording_file: Text = None,
                     recording_url: Text = None,
                     mimetype: Text = None):
    response = None
    if recording_file is not None:
        fileName, fileExtension = os.path.splitext(recording_file)
        fileExtension = fileExtension[1:]
        mType = mimetype or f"audio/{fileExtension}"
        with open(recording_file, 'rb') as audio:
            source = {'buffer': audio, 'mimetype': mType}
            response = await call_dg(source, mType)
    elif recording_url is not None:
        ext = get_ext_from_url(recording_url)
        mType = mimetype or f"audio/{ext}"
        source = {'url': recording_url}
        response = await call_dg(source, mType)

    transcript, words = process_response(response)

    return transcript, words

# async def run_test():
#     # a = await transcribe(recording_file='../../transcripts/4198/recording.mp4')
#     # b = await transcribe(recording_file='../../transcripts/hv/jennifer.m4a')
#     # c = await transcribe(recording_file='../../transcripts/hv/juanita.m4a')
#     # d = await transcribe(recording_file='../../transcripts/3979/recording.mp4')
#     #
#     with open(os.path.join('../../transcripts/live/240406/transcript__.txt'), 'r') as f:
#         a = f.read()
#
#     a = replace_code_likes(a)
#
#     with open(os.path.join('../../transcripts/live/240406/transcript__.txt'), 'w') as f:
#         f.write(a)
#     # with open(os.path.join('../../transcripts/hv/jennifer.txt'), 'w') as f:
#     #     f.write(b)
#     # with open(os.path.join('../../transcripts/hv/juanita.txt'), 'w') as f:
#     #     f.write(c)
#     # with open(os.path.join('../../transcripts/3979/transcript.txt'), 'w') as f:
#     #     f.write(d)
#
#
# if __name__ == '__main__':
#     import asyncio
#     asyncio.run(run_test())
