[{"code": "M0060", "variations": ["in 0 0 6o", "and 0 0 6o", "i'm 0 0 6o", "in o 0 60", "and o 0 60", "i'm o 0 60", "in o o 6o", "and o o 6o", "i'm o o 6o", "in 0 0 60", "and 0 0 60", "i'm 0 0 60", "in 006o", "and 006o", "i'm 006o", "in o060", "and o060", "i'm o060", "in 0 0 6 o", "and 0 0 6 o", "i'm 0 0 6 o", "in o 0 6 0", "and o 0 6 0", "i'm o 0 6 0", "in oo6o", "and oo6o", "i'm oo6o", "in o o 6 o", "and o o 6 o", "i'm o o 6 o", "in 0060", "and 0060", "i'm 0060", "in006 0", "and006 0", "i'm006 0", "in 0 0 6 0", "and 0 0 6 0", "i'm 0 0 6 0", "in0060", "and0060", "i'm0060", "M 0 0 6o", "M o 0 60", "M o o 6o", "M 0 0 60", "M 006o", "M o060", "M 0 0 6 o", "M o 0 6 0", "M oo6o", "M o o 6 o", "M 0060", "M006 0", "M 0 0 6 0", "M0060", "N 0 0 6o", "N o 0 60", "N o o 6o", "N 0 0 60", "N 006o", "N o060", "N 0 0 6 o", "N o 0 6 0", "N oo6o", "N o o 6 o", "N 0060", "N006 0", "N 0 0 6 0", "N0060"]}, {"code": "M1810", "variations": ["in 1 810", "and 1 810", "i'm 1 810", "in 181 o", "and 181 o", "i'm 181 o", "in 181 o ", "and 181 o ", "i'm 181 o ", "in 181 0", "and 181 0", "i'm 181 0", "in 1 8 1o", "and 1 8 1o", "i'm 1 8 1o", "in 18 10", "and 18 10", "i'm 18 10", "in 1 8 10", "and 1 8 10", "i'm 1 8 10", "in 18 1 o", "and 18 1 o", "i'm 18 1 o", "in 18 1 0", "and 18 1 0", "i'm 18 1 0", "in 181o", "and 181o", "i'm 181o", "in 1 8 1 o", "and 1 8 1 o", "i'm 1 8 1 o", "in 1810", "and 1810", "i'm 1810", "in 1 8 1 0", "and 1 8 1 0", "i'm 1 8 1 0", "in1810", "and1810", "i'm1810", "M 1 810", "M 181 o", "M 181 o ", "M 181 0", "M 1 8 1o", "M 18 10", "M 1 8 10", "M 18 1 o", "M 18 1 0", "M 181o", "M 1 8 1 o", "M 1810", "18 10", "1810", "M 1 8 1 0", "M1810", "N 1 810", "N 181 o", "N 181 o ", "N 181 0", "N 1 8 1o", "N 18 10", "N 1 8 10", "N 18 1 o", "N 18 1 0", "N 181o", "N 1 8 1 o", "N 1810", "N18 10", "N 1 8 1 0", "N1810"]}, {"code": "M0030", "variations": ["in 0 0 3o", "and 0 0 3o", "i'm 0 0 3o", "in o 0 30", "and o 0 30", "i'm o 0 30", "in o o 3o", "and o o 3o", "i'm o o 3o", "in 0 0 30", "and 0 0 30", "i'm 0 0 30", "in 003o", "and 003o", "i'm 003o", "in o030", "and o030", "i'm o030", "in 0 0 3 o", "and 0 0 3 o", "i'm 0 0 3 o", "in o 0 3 0", "and o 0 3 0", "i'm o 0 3 0", "in oo3o", "and oo3o", "i'm oo3o", "in o o 3 o", "and o o 3 o", "i'm o o 3 o", "in 0030", "and 0030", "i'm 0030", "in003 0", "and003 0", "i'm003 0", "in 0 0 3 0", "and 0 0 3 0", "i'm 0 0 3 0", "in0030", "and0030", "i'm0030", "M 0 0 3o", "M o 0 30", "M o o 3o", "M 0 0 30", "M 003o", "M o030", "M 0 0 3 o", "M o 0 3 0", "M oo3o", "M o o 3 o", "M 0030", "M003 0", "M 0 0 3 0", "M0030", "N 0 0 3o", "N o 0 30", "N o o 3o", "N 0 0 30", "N 003o", "N o030", "N 0 0 3 o", "N o 0 3 0", "N oo3o", "N o o 3 o", "N 0030", "N003 0", "N 0 0 3 0", "N0030"]}, {"code": "M0040", "variations": ["in 0 0 4o", "and 0 0 4o", "i'm 0 0 4o", "in o 0 40", "and o 0 40", "i'm o 0 40", "in o o 4o", "and o o 4o", "i'm o o 4o", "in 0 0 40", "and 0 0 40", "i'm 0 0 40", "in 004o", "and 004o", "i'm 004o", "in o040", "and o040", "i'm o040", "in 0 0 4 o", "and 0 0 4 o", "i'm 0 0 4 o", "in o 0 4 0", "and o 0 4 0", "i'm o 0 4 0", "in oo4o", "and oo4o", "i'm oo4o", "in o o 4 o", "and o o 4 o", "i'm o o 4 o", "in 0040", "and 0040", "i'm 0040", "in004 0", "and004 0", "i'm004 0", "in 0 0 4 0", "and 0 0 4 0", "i'm 0 0 4 0", "in0040", "and0040", "i'm0040", "M 0 0 4o", "M o 0 40", "M o o 4o", "M 0 0 40", "M 004o", "M o040", "M 0 0 4 o", "M o 0 4 0", "M oo4o", "M o o 4 o", "M 0040", "M004 0", "M 0 0 4 0", "M0040", "N 0 0 4o", "N o 0 40", "N o o 4o", "N 0 0 40", "N 004o", "N o040", "N 0 0 4 o", "N o 0 4 0", "N oo4o", "N o o 4 o", "N 0040", "N004 0", "N 0 0 4 0", "N0040"]}, {"code": "M0066", "variations": ["in 0 o 66", "and 0 o 66", "i'm 0 o 66", "in o 0 66", "and o 0 66", "i'm o 0 66", "in o o 66", "and o o 66", "i'm o o 66", "in 0 0 66", "and 0 0 66", "i'm 0 0 66", "in 0o66", "and 0o66", "i'm 0o66", "in o066", "and o066", "i'm o066", "in 0 o 6 6", "and 0 o 6 6", "i'm 0 o 6 6", "in o 0 6 6", "and o 0 6 6", "i'm o 0 6 6", "in oo66", "and oo66", "i'm oo66", "in o o 6 6", "and o o 6 6", "i'm o o 6 6", "in 0066", "and 0066", "i'm 0066", "in006 6", "and006 6", "i'm006 6", "in 0 0 6 6", "and 0 0 6 6", "i'm 0 0 6 6", "in0066", "and0066", "i'm0066", "M 0 o 66", "M o 0 66", "M o o 66", "M 0 0 66", "M 0o66", "M o066", "M 0 o 6 6", "M o 0 6 6", "M oo66", "M o o 6 6", "M 0066", "M006 6", "M 0 0 6 6", "M0066", "N 0 o 66", "N o 0 66", "N o o 66", "N 0 0 66", "N 0o66", "N o066", "N 0 o 6 6", "N o 0 6 6", "N oo66", "N o o 6 6", "N 0066", "N006 6", "N 0 0 6 6", "N0066"]}, {"code": "M0069", "variations": ["in 0 o 69", "and 0 o 69", "i'm 0 o 69", "in o 0 69", "and o 0 69", "i'm o 0 69", "in o o 69", "and o o 69", "i'm o o 69", "in 0 0 69", "and 0 0 69", "i'm 0 0 69", "in 0o69", "and 0o69", "i'm 0o69", "in o069", "and o069", "i'm o069", "in 0 o 6 9", "and 0 o 6 9", "i'm 0 o 6 9", "in o 0 6 9", "and o 0 6 9", "i'm o 0 6 9", "in oo69", "and oo69", "i'm oo69", "in o o 6 9", "and o o 6 9", "i'm o o 6 9", "in 0069", "and 0069", "i'm 0069", "in006 9", "and006 9", "i'm006 9", "in 0 0 6 9", "and 0 0 6 9", "i'm 0 0 6 9", "in0069", "and0069", "i'm0069", "M 0 o 69", "M o 0 69", "M o o 69", "M 0 0 69", "M 0o69", "M o069", "M 0 o 6 9", "M o 0 6 9", "M oo69", "M o o 6 9", "M 0069", "M006 9", "M 0 0 6 9", "M0069", "N 0 o 69", "N o 0 69", "N o o 69", "N 0 0 69", "N 0o69", "N o069", "N 0 o 6 9", "N o 0 6 9", "N oo69", "N o o 6 9", "N 0069", "N006 9", "N 0 0 6 9", "N0069"]}, {"code": "M0050", "variations": ["in 0 0 5o", "and 0 0 5o", "i'm 0 0 5o", "in o 0 50", "and o 0 50", "i'm o 0 50", "in o o 5o", "and o o 5o", "i'm o o 5o", "in 0 0 50", "and 0 0 50", "i'm 0 0 50", "in 005o", "and 005o", "i'm 005o", "in o050", "and o050", "i'm o050", "in 0 0 5 o", "and 0 0 5 o", "i'm 0 0 5 o", "in o 0 5 0", "and o 0 5 0", "i'm o 0 5 0", "in oo5o", "and oo5o", "i'm oo5o", "in o o 5 o", "and o o 5 o", "i'm o o 5 o", "in 0050", "and 0050", "i'm 0050", "in005 0", "and005 0", "i'm005 0", "in 0 0 5 0", "and 0 0 5 0", "i'm 0 0 5 0", "in0050", "and0050", "i'm0050", "M 0 0 5o", "M o 0 50", "M o o 5o", "M 0 0 50", "M 005o", "M o050", "M 0 0 5 o", "M o 0 5 0", "M oo5o", "M o o 5 o", "M 0050", "M005 0", "M 0 0 5 0", "M0050", "N 0 0 5o", "N o 0 50", "N o o 5o", "N 0 0 50", "N 005o", "N o050", "N 0 0 5 o", "N o 0 5 0", "N oo5o", "N o o 5 o", "N 0050", "N005 0", "N 0 0 5 0", "N0050"]}, {"code": "M0063", "variations": ["in 0 o 63", "and 0 o 63", "i'm 0 o 63", "in o 0 63", "and o 0 63", "i'm o 0 63", "in o o 63", "and o o 63", "i'm o o 63", "in 0 0 63", "and 0 0 63", "i'm 0 0 63", "in 0o63", "and 0o63", "i'm 0o63", "in o063", "and o063", "i'm o063", "in 0 o 6 3", "and 0 o 6 3", "i'm 0 o 6 3", "in o 0 6 3", "and o 0 6 3", "i'm o 0 6 3", "in oo63", "and oo63", "i'm oo63", "in o o 6 3", "and o o 6 3", "i'm o o 6 3", "in 0063", "and 0063", "i'm 0063", "in006 3", "and006 3", "i'm006 3", "in 0 0 6 3", "and 0 0 6 3", "i'm 0 0 6 3", "in0063", "and0063", "i'm0063", "M 0 o 63", "M o 0 63", "M o o 63", "M 0 0 63", "M 0o63", "M o063", "M 0 o 6 3", "M o 0 6 3", "M oo63", "M o o 6 3", "M 0063", "M006 3", "M 0 0 6 3", "M0063", "N 0 o 63", "N o 0 63", "N o o 63", "N 0 0 63", "N 0o63", "N o063", "N 0 o 6 3", "N o 0 6 3", "N oo63", "N o o 6 3", "N 0063", "N006 3", "N 0 0 6 3", "N0063"]}, {"code": "M0064", "variations": ["in 0 o 64", "and 0 o 64", "i'm 0 o 64", "in o 0 64", "and o 0 64", "i'm o 0 64", "in o o 64", "and o o 64", "i'm o o 64", "in 0 0 64", "and 0 0 64", "i'm 0 0 64", "in 0o64", "and 0o64", "i'm 0o64", "in o064", "and o064", "i'm o064", "in 0 o 6 4", "and 0 o 6 4", "i'm 0 o 6 4", "in o 0 6 4", "and o 0 6 4", "i'm o 0 6 4", "in oo64", "and oo64", "i'm oo64", "in o o 6 4", "and o o 6 4", "i'm o o 6 4", "in 0064", "and 0064", "i'm 0064", "in006 4", "and006 4", "i'm006 4", "in 0 0 6 4", "and 0 0 6 4", "i'm 0 0 6 4", "in0064", "and0064", "i'm0064", "M 0 o 64", "M o 0 64", "M o o 64", "M 0 0 64", "M 0o64", "M o064", "M 0 o 6 4", "M o 0 6 4", "M oo64", "M o o 6 4", "M 0064", "M006 4", "M 0 0 6 4", "M0064", "N 0 o 64", "N o 0 64", "N o o 64", "N 0 0 64", "N 0o64", "N o064", "N 0 o 6 4", "N o 0 6 4", "N oo64", "N o o 6 4", "N 0064", "N006 4", "N 0 0 6 4", "N0064"]}, {"code": "M0065", "variations": ["in 0 o 65", "and 0 o 65", "i'm 0 o 65", "in o 0 65", "and o 0 65", "i'm o 0 65", "in o o 65", "and o o 65", "i'm o o 65", "in 0 0 65", "and 0 0 65", "i'm 0 0 65", "in 0o65", "and 0o65", "i'm 0o65", "in o065", "and o065", "i'm o065", "in 0 o 6 5", "and 0 o 6 5", "i'm 0 o 6 5", "in o 0 6 5", "and o 0 6 5", "i'm o 0 6 5", "in oo65", "and oo65", "i'm oo65", "in o o 6 5", "and o o 6 5", "i'm o o 6 5", "in 0065", "and 0065", "i'm 0065", "in006 5", "and006 5", "i'm006 5", "in 0 0 6 5", "and 0 0 6 5", "i'm 0 0 6 5", "in0065", "and0065", "i'm0065", "M 0 o 65", "M o 0 65", "M o o 65", "M 0 0 65", "M 0o65", "M o065", "M 0 o 6 5", "M o 0 6 5", "M oo65", "M o o 6 5", "M 0065", "M006 5", "M 0 0 6 5", "M0065", "N 0 o 65", "N o 0 65", "N o o 65", "N 0 0 65", "N 0o65", "N o065", "N 0 o 6 5", "N o 0 6 5", "N oo65", "N o o 6 5", "N 0065", "N006 5", "N 0 0 6 5", "N0065"]}, {"code": "A1005", "variations": ["A 100 5", "A 10 o 5", "A 1o 0 5", "A 1o o 5", "A 10 0 5", "A 1 0 o 5", "A 1 o 0 5", "A 1oo5", "A 1 o o 5", "A 1005", "A1 005", "A 1 0 0 5", "A1005"]}, {"code": "A1010", "variations": ["A 101 o", "A 1o1 0", "A 101 o ", "A 101 0", "A 10 1 o ", "A 10 10", "A 10 1 o", "A 1o 1 0", "A 1o 1 o", "A 10 1 0", "A 1 0 1 o", "A 1 o 1 0", "A 1o1o", "A 1 o 1 o", "A 1010", "A1 010", "A 1 0 1 0", "A1010"]}, {"code": "A1110A", "variations": ["A 111 o A", "A 111 0 A", "A 11 10 A", "A 1 1 10 A", "A 11 1 o A", "A 11 1 0 A", "A 111o A", "A 1 1 1 o A", "A 1110 A", "A11 10A", "A 1 1 1 0 A", "A1110A"]}, {"code": "A1110B", "variations": ["A 111 o B", "A 111 0 B", "A 11 10 B", "A 1 1 10 B", "A 11 1 o B", "A 11 1 0 B", "A 111o B", "A 1 1 1 o B", "A 1110 B", "A11 10B", "A 1 1 1 0 B", "A1110B"]}, {"code": "M0150", "variations": ["in 0 15o", "and 0 15o", "i'm 0 15o", "in o 150", "and o 150", "i'm o 150", "in 0 150", "and 0 150", "i'm 0 150", "in 0 1 5o", "and 0 1 5o", "i'm 0 1 5o", "in o 1 50", "and o 1 50", "i'm o 1 50", "in o 1 5o", "and o 1 5o", "i'm o 1 5o", "in 0 1 50", "and 0 1 50", "i'm 0 1 50", "in 0 15 o", "and 0 15 o", "i'm 0 15 o", "in o 15 0", "and o 15 0", "i'm o 15 0", "in o 15 o", "and o 15 o", "i'm o 15 o", "in 0 15 0", "and 0 15 0", "i'm 0 15 0", "in 015o", "and 015o", "i'm 015o", "in o150", "and o150", "i'm o150", "in 0 1 5 o", "and 0 1 5 o", "i'm 0 1 5 o", "in o 1 5 0", "and o 1 5 0", "i'm o 1 5 0", "in o15o", "and o15o", "i'm o15o", "in o 1 5 o", "and o 1 5 o", "i'm o 1 5 o", "in 0150", "and 0150", "i'm 0150", "in015 0", "and015 0", "i'm015 0", "in 0 1 5 0", "and 0 1 5 0", "i'm 0 1 5 0", "in0150", "and0150", "i'm0150", "M 0 15o", "M o 150", "M 0 150", "M 0 1 5o", "M o 1 50", "M o 1 5o", "M 0 1 50", "M 0 15 o", "M o 15 0", "M o 15 o", "M 0 15 0", "M 015o", "M o150", "M 0 1 5 o", "M o 1 5 0", "M o15o", "M o 1 5 o", "M 0150", "M015 0", "M 0 1 5 0", "M0150", "N 0 15o", "N o 150", "N 0 150", "N 0 1 5o", "N o 1 50", "N o 1 5o", "N 0 1 50", "N 0 15 o", "N o 15 0", "N o 15 o", "N 0 15 0", "N 015o", "N o150", "N 0 1 5 o", "N o 1 5 0", "N o15o", "N o 1 5 o", "N 0150", "N015 0", "N 0 1 5 0", "N0150"]}, {"code": "M0102", "variations": ["in 0 1o2", "and 0 1o2", "i'm 0 1o2", "in o 102", "and o 102", "i'm o 102", "in 0 102", "and 0 102", "i'm 0 102", "in 0 1o 2", "and 0 1o 2", "i'm 0 1o 2", "in o 10 2", "and o 10 2", "i'm o 10 2", "in o 1o 2", "and o 1o 2", "i'm o 1o 2", "in 0 10 2", "and 0 10 2", "i'm 0 10 2", "in 01o2", "and 01o2", "i'm 01o2", "in o102", "and o102", "i'm o102", "in 0 1 o 2", "and 0 1 o 2", "i'm 0 1 o 2", "in o 1 0 2", "and o 1 0 2", "i'm o 1 0 2", "in o1o2", "and o1o2", "i'm o1o2", "in o 1 o 2", "and o 1 o 2", "i'm o 1 o 2", "in 0102", "and 0102", "i'm 0102", "in01 02", "and01 02", "i'm01 02", "in 0 1 0 2", "and 0 1 0 2", "i'm 0 1 0 2", "in0102", "and0102", "i'm0102", "M 0 1o2", "M o 102", "M 0 102", "M 0 1o 2", "M o 10 2", "M o 1o 2", "M 0 10 2", "M 01o2", "M o102", "M 0 1 o 2", "M o 1 0 2", "M o1o2", "M o 1 o 2", "M 0102", "M01 02", "M 0 1 0 2", "M0102", "N 0 1o2", "N o 102", "N 0 102", "N 0 1o 2", "N o 10 2", "N o 1o 2", "N 0 10 2", "N 01o2", "N o102", "N 0 1 o 2", "N o 1 0 2", "N o1o2", "N o 1 o 2", "N 0102", "N01 02", "N 0 1 0 2", "N0102"]}, {"code": "M0104", "variations": ["in 0 1o4", "and 0 1o4", "i'm 0 1o4", "in o 104", "and o 104", "i'm o 104", "in 0 104", "and 0 104", "i'm 0 104", "in 0 1o 4", "and 0 1o 4", "i'm 0 1o 4", "in o 10 4", "and o 10 4", "i'm o 10 4", "in o 1o 4", "and o 1o 4", "i'm o 1o 4", "in 0 10 4", "and 0 10 4", "i'm 0 10 4", "in 01o4", "and 01o4", "i'm 01o4", "in o104", "and o104", "i'm o104", "in 0 1 o 4", "and 0 1 o 4", "i'm 0 1 o 4", "in o 1 0 4", "and o 1 0 4", "i'm o 1 0 4", "in o1o4", "and o1o4", "i'm o1o4", "in o 1 o 4", "and o 1 o 4", "i'm o 1 o 4", "in 0104", "and 0104", "i'm 0104", "in01 04", "and01 04", "i'm01 04", "in 0 1 0 4", "and 0 1 0 4", "i'm 0 1 0 4", "in0104", "and0104", "i'm0104", "M 0 1o4", "M o 104", "M 0 104", "M 0 1o 4", "M o 10 4", "M o 1o 4", "M 0 10 4", "M 01o4", "M o104", "M 0 1 o 4", "M o 1 0 4", "M o1o4", "M o 1 o 4", "M 0104", "M01 04", "M 0 1 0 4", "M0104", "N 0 1o4", "N o 104", "N 0 104", "N 0 1o 4", "N o 10 4", "N o 1o 4", "N 0 10 4", "N 01o4", "N o104", "N 0 1 o 4", "N o 1 0 4", "N o1o4", "N o 1 o 4", "N 0104", "N01 04", "N 0 1 0 4", "N0104"]}, {"code": "M1000", "variations": ["in 100 o", "and 100 o", "i'm 100 o", "in 1o0 0", "and 1o0 0", "i'm 1o0 0", "in 100 o ", "and 100 o ", "i'm 100 o ", "in 100 0", "and 100 0", "i'm 100 0", "in 10 0 o", "and 10 0 o", "i'm 10 0 o", "in 1o 0 0", "and 1o 0 0", "i'm 1o 0 0", "in 1o o o", "and 1o o o", "i'm 1o o o", "in 10 0 0", "and 10 0 0", "i'm 10 0 0", "in 1 0 0 o", "and 1 0 0 o", "i'm 1 0 0 o", "in 1 o 0 0", "and 1 o 0 0", "i'm 1 o 0 0", "in 1ooo", "and 1ooo", "i'm 1ooo", "in 1 o o o", "and 1 o o o", "i'm 1 o o o", "in 1000", "and 1000", "i'm 1000", "in 1 0 0 0", "and 1 0 0 0", "i'm 1 0 0 0", "in1000", "and1000", "i'm1000", "M 100 o", "M 1o0 0", "M 100 o ", "M 100 0", "M 10 0 o", "M 1o 0 0", "M 1o o o", "M 10 0 0", "M 1 0 0 o", "M 1 o 0 0", "M 1ooo", "M 1 o o o", "M 1000", "1 000", "1000", "M 1 0 0 0", "M1000", "N 100 o", "N 1o0 0", "N 100 o ", "N 100 0", "N 10 0 o", "N 1o 0 0", "N 1o o o", "N 10 0 0", "N 1 0 0 o", "N 1 o 0 0", "N 1ooo", "N 1 o o o", "N 1000", "N1 000", "N 1 0 0 0", "N1000"]}, {"code": "M10231", "variations": ["in 1 o 231", "and 1 o 231", "i'm 1 o 231", "in 1 0 231", "and 1 0 231", "i'm 1 0 231", "in 102 3 1", "and 102 3 1", "i'm 102 3 1", "in 1 o 2 31", "and 1 o 2 31", "i'm 1 o 2 31", "in 1 0 2 31", "and 1 0 2 31", "i'm 1 0 2 31", "in 1 o 23 1", "and 1 o 23 1", "i'm 1 o 23 1", "in 10 23 1", "and 10 23 1", "i'm 10 23 1", "in 1 0 23 1", "and 1 0 23 1", "i'm 1 0 23 1", "in 1o 2 3 1", "and 1o 2 3 1", "i'm 1o 2 3 1", "in 10 2 3 1", "and 10 2 3 1", "i'm 10 2 3 1", "in 1o231", "and 1o231", "i'm 1o231", "in 1 o 2 3 1", "and 1 o 2 3 1", "i'm 1 o 2 3 1", "in 10231", "and 10231", "i'm 10231", "in 1 0 2 3 1", "and 1 0 2 3 1", "i'm 1 0 2 3 1", "in10231", "and10231", "i'm10231", "M 1 o 231", "M 1 0 231", "M 102 3 1", "M 1 o 2 31", "M 1 0 2 31", "M 1 o 23 1", "M 10 23 1", "M 1 0 23 1", "M 1o 2 3 1", "M 10 2 3 1", "M 1o231", "M 1 o 2 3 1", "M 10231", "1 023 1", "10231", "M 1 0 2 3 1", "M10231", "N 1 o 231", "N 1 0 231", "N 102 3 1", "N 1 o 2 31", "N 1 0 2 31", "N 1 o 23 1", "N 10 23 1", "N 1 0 23 1", "N 1o 2 3 1", "N 10 2 3 1", "N 1o231", "N 1 o 2 3 1", "N 10231", "N1 023 1", "N 1 0 2 3 1", "N10231"]}, {"code": "M10232", "variations": ["in 1 o 232", "and 1 o 232", "i'm 1 o 232", "in 1 0 232", "and 1 0 232", "i'm 1 0 232", "in 102 3 2", "and 102 3 2", "i'm 102 3 2", "in 1 o 2 32", "and 1 o 2 32", "i'm 1 o 2 32", "in 1 0 2 32", "and 1 0 2 32", "i'm 1 0 2 32", "in 1 o 23 2", "and 1 o 23 2", "i'm 1 o 23 2", "in 10 23 2", "and 10 23 2", "i'm 10 23 2", "in 1 0 23 2", "and 1 0 23 2", "i'm 1 0 23 2", "in 1o 2 3 2", "and 1o 2 3 2", "i'm 1o 2 3 2", "in 10 2 3 2", "and 10 2 3 2", "i'm 10 2 3 2", "in 1o232", "and 1o232", "i'm 1o232", "in 1 o 2 3 2", "and 1 o 2 3 2", "i'm 1 o 2 3 2", "in 10232", "and 10232", "i'm 10232", "in 1 0 2 3 2", "and 1 0 2 3 2", "i'm 1 0 2 3 2", "in10232", "and10232", "i'm10232", "M 1 o 232", "M 1 0 232", "M 102 3 2", "M 1 o 2 32", "M 1 0 2 32", "M 1 o 23 2", "M 10 23 2", "M 1 0 23 2", "M 1o 2 3 2", "M 10 2 3 2", "M 1o232", "M 1 o 2 3 2", "M 10232", "1 023 2", "10232", "M 1 0 2 3 2", "M10232", "N 1 o 232", "N 1 0 232", "N 102 3 2", "N 1 o 2 32", "N 1 0 2 32", "N 1 o 23 2", "N 10 23 2", "N 1 0 23 2", "N 1o 2 3 2", "N 10 2 3 2", "N 1o232", "N 1 o 2 3 2", "N 10232", "N1 023 2", "N 1 0 2 3 2", "N10232"]}, {"code": "M1028", "variations": ["in 102 8", "and 102 8", "i'm 102 8", "in 1 o 28", "and 1 o 28", "i'm 1 o 28", "in 10 28", "and 10 28", "i'm 10 28", "in 1 0 28", "and 1 0 28", "i'm 1 0 28", "in 1o 2 8", "and 1o 2 8", "i'm 1o 2 8", "in 10 2 8", "and 10 2 8", "i'm 10 2 8", "in 1o28", "and 1o28", "i'm 1o28", "in 1 o 2 8", "and 1 o 2 8", "i'm 1 o 2 8", "in 1028", "and 1028", "i'm 1028", "in 1 0 2 8", "and 1 0 2 8", "i'm 1 0 2 8", "in1028", "and1028", "i'm1028", "M 102 8", "M 1 o 28", "M 10 28", "M 1 0 28", "M 1o 2 8", "M 10 2 8", "M 1o28", "M 1 o 2 8", "M 1028", "1 028", "1028", "M 1 0 2 8", "M1028", "N 102 8", "N 1 o 28", "N 10 28", "N 1 0 28", "N 1o 2 8", "N 10 2 8", "N 1o28", "N 1 o 2 8", "N 1028", "N1 028", "N 1 0 2 8", "N1028"]}, {"code": "M2001", "variations": ["in 200 1", "and 200 1", "i'm 200 1", "in 20 o 1", "and 20 o 1", "i'm 20 o 1", "in 2o 0 1", "and 2o 0 1", "i'm 2o 0 1", "in 2o o 1", "and 2o o 1", "i'm 2o o 1", "in 20 0 1", "and 20 0 1", "i'm 20 0 1", "in 2 0 o 1", "and 2 0 o 1", "i'm 2 0 o 1", "in 2 o 0 1", "and 2 o 0 1", "i'm 2 o 0 1", "in 2oo1", "and 2oo1", "i'm 2oo1", "in 2 o o 1", "and 2 o o 1", "i'm 2 o o 1", "in 2001", "and 2001", "i'm 2001", "in 2 0 0 1", "and 2 0 0 1", "i'm 2 0 0 1", "in2001", "and2001", "i'm2001", "M 200 1", "M 20 o 1", "M 2o 0 1", "M 2o o 1", "M 20 0 1", "M 2 0 o 1", "M 2 o 0 1", "M 2oo1", "M 2 o o 1", "M 2001", "2 001", "2001", "M 2 0 0 1", "M2001", "N 200 1", "N 20 o 1", "N 2o 0 1", "N 2o o 1", "N 20 0 1", "N 2 0 o 1", "N 2 o 0 1", "N 2oo1", "N 2 o o 1", "N 2001", "N2 001", "N 2 0 0 1", "N2001"]}, {"code": "M2003", "variations": ["in 200 3", "and 200 3", "i'm 200 3", "in 20 o 3", "and 20 o 3", "i'm 20 o 3", "in 2o 0 3", "and 2o 0 3", "i'm 2o 0 3", "in 2o o 3", "and 2o o 3", "i'm 2o o 3", "in 20 0 3", "and 20 0 3", "i'm 20 0 3", "in 2 0 o 3", "and 2 0 o 3", "i'm 2 0 o 3", "in 2 o 0 3", "and 2 o 0 3", "i'm 2 o 0 3", "in 2oo3", "and 2oo3", "i'm 2oo3", "in 2 o o 3", "and 2 o o 3", "i'm 2 o o 3", "in 2003", "and 2003", "i'm 2003", "in 2 0 0 3", "and 2 0 0 3", "i'm 2 0 0 3", "in2003", "and2003", "i'm2003", "M 200 3", "M 20 o 3", "M 2o 0 3", "M 2o o 3", "M 20 0 3", "M 2 0 o 3", "M 2 o 0 3", "M 2oo3", "M 2 o o 3", "M 2003", "2 003", "2003", "M 2 0 0 3", "M2003", "N 200 3", "N 20 o 3", "N 2o 0 3", "N 2o o 3", "N 20 0 3", "N 2 0 o 3", "N 2 o 0 3", "N 2oo3", "N 2 o o 3", "N 2003", "N2 003", "N 2 0 0 3", "N2003"]}, {"code": "N04151", "variations": ["N o 4 151", "N 0 4 151", "N o 415 1", "N 0 415 1", "N o 41 51", "N o 4 1 51", "N 0 41 51", "N 0 4 1 51", "N o 41 5 1", "N 0 41 5 1", "N o4151", "N o 4 1 5 1", "N 04151", "N041 51", "N 0 4 1 5 1", "N04151", "in o 4 151", "and o 4 151", "i'm o 4 151", "in 0 4 151", "and 0 4 151", "i'm 0 4 151", "in o 415 1", "and o 415 1", "i'm o 415 1", "in 0 415 1", "and 0 415 1", "i'm 0 415 1", "in o 41 51", "and o 41 51", "i'm o 41 51", "in o 4 1 51", "and o 4 1 51", "i'm o 4 1 51", "in 0 41 51", "and 0 41 51", "i'm 0 41 51", "in 0 4 1 51", "and 0 4 1 51", "i'm 0 4 1 51", "in o 41 5 1", "and o 41 5 1", "i'm o 41 5 1", "in 0 41 5 1", "and 0 41 5 1", "i'm 0 41 5 1", "in o4151", "and o4151", "i'm o4151", "in o 4 1 5 1", "and o 4 1 5 1", "i'm o 4 1 5 1", "in 04151", "and 04151", "i'm 04151", "in041 51", "and041 51", "i'm041 51", "in 0 4 1 5 1", "and 0 4 1 5 1", "i'm 0 4 1 5 1", "in04151", "and04151", "i'm04151", "M o 4 151", "M 0 4 151", "M o 415 1", "M 0 415 1", "M o 41 51", "M o 4 1 51", "M 0 41 51", "M 0 4 1 51", "M o 41 5 1", "M 0 41 5 1", "M o4151", "M o 4 1 5 1", "M 04151", "M041 51", "M 0 4 1 5 1", "M04151"]}, {"code": "N04152", "variations": ["N o 4 152", "N 0 4 152", "N o 415 2", "N 0 415 2", "N o 41 52", "N o 4 1 52", "N 0 41 52", "N 0 4 1 52", "N o 41 5 2", "N 0 41 5 2", "N o4152", "N o 4 1 5 2", "N 04152", "N041 52", "N 0 4 1 5 2", "N04152", "in o 4 152", "and o 4 152", "i'm o 4 152", "in 0 4 152", "and 0 4 152", "i'm 0 4 152", "in o 415 2", "and o 415 2", "i'm o 415 2", "in 0 415 2", "and 0 415 2", "i'm 0 415 2", "in o 41 52", "and o 41 52", "i'm o 41 52", "in o 4 1 52", "and o 4 1 52", "i'm o 4 1 52", "in 0 41 52", "and 0 41 52", "i'm 0 41 52", "in 0 4 1 52", "and 0 4 1 52", "i'm 0 4 1 52", "in o 41 5 2", "and o 41 5 2", "i'm o 41 5 2", "in 0 41 5 2", "and 0 41 5 2", "i'm 0 41 5 2", "in o4152", "and o4152", "i'm o4152", "in o 4 1 5 2", "and o 4 1 5 2", "i'm o 4 1 5 2", "in 04152", "and 04152", "i'm 04152", "in041 52", "and041 52", "i'm041 52", "in 0 4 1 5 2", "and 0 4 1 5 2", "i'm 0 4 1 5 2", "in04152", "and04152", "i'm04152", "M o 4 152", "M 0 4 152", "M o 415 2", "M 0 415 2", "M o 41 52", "M o 4 1 52", "M 0 41 52", "M 0 4 1 52", "M o 41 5 2", "M 0 41 5 2", "M o4152", "M o 4 1 5 2", "M 04152", "M041 52", "M 0 4 1 5 2", "M04152"]}, {"code": "M2010", "variations": ["in 201 o", "and 201 o", "i'm 201 o", "in 2o1 0", "and 2o1 0", "i'm 2o1 0", "in 201 o ", "and 201 o ", "i'm 201 o ", "in 201 0", "and 201 0", "i'm 201 0", "in 2 o 1o", "and 2 o 1o", "i'm 2 o 1o", "in 2 0 1o", "and 2 0 1o", "i'm 2 0 1o", "in 2 o 10", "and 2 o 10", "i'm 2 o 10", "in 20 10", "and 20 10", "i'm 20 10", "in 2 0 10", "and 2 0 10", "i'm 2 0 10", "in 20 1 o", "and 20 1 o", "i'm 20 1 o", "in 2o 1 0", "and 2o 1 0", "i'm 2o 1 0", "in 2o 1 o", "and 2o 1 o", "i'm 2o 1 o", "in 20 1 0", "and 20 1 0", "i'm 20 1 0", "in 2 0 1 o", "and 2 0 1 o", "i'm 2 0 1 o", "in 2 o 1 0", "and 2 o 1 0", "i'm 2 o 1 0", "in 2o1o", "and 2o1o", "i'm 2o1o", "in 2 o 1 o", "and 2 o 1 o", "i'm 2 o 1 o", "in 2010", "and 2010", "i'm 2010", "in 2 0 1 0", "and 2 0 1 0", "i'm 2 0 1 0", "in2010", "and2010", "i'm2010", "M 201 o", "M 2o1 0", "M 201 o ", "M 201 0", "M 2 o 1o", "M 2 0 1o", "M 2 o 10", "M 20 10", "M 2 0 10", "M 20 1 o", "M 2o 1 0", "M 2o 1 o", "M 20 1 0", "M 2 0 1 o", "M 2 o 1 0", "M 2o1o", "M 2 o 1 o", "M 2010", "2 010", "2010", "M 2 0 1 0", "M2010", "N 201 o", "N 2o1 0", "N 201 o ", "N 201 0", "N 2 o 1o", "N 2 0 1o", "N 2 o 10", "N 20 10", "N 2 0 10", "N 20 1 o", "N 2o 1 0", "N 2o 1 o", "N 20 1 0", "N 2 0 1 o", "N 2 o 1 0", "N 2o1o", "N 2 o 1 o", "N 2010", "N2 010", "N 2 0 1 0", "N2010"]}, {"code": "M2020", "variations": ["in 202 o", "and 202 o", "i'm 202 o", "in 2o2 0", "and 2o2 0", "i'm 2o2 0", "in 202 o ", "and 202 o ", "i'm 202 o ", "in 202 0", "and 202 0", "i'm 202 0", "in 20 2 o ", "and 20 2 o ", "i'm 20 2 o ", "in 20 20", "and 20 20", "i'm 20 20", "in 20 2 o", "and 20 2 o", "i'm 20 2 o", "in 2o 2 0", "and 2o 2 0", "i'm 2o 2 0", "in 2o 2 o", "and 2o 2 o", "i'm 2o 2 o", "in 20 2 0", "and 20 2 0", "i'm 20 2 0", "in 2 0 2 o", "and 2 0 2 o", "i'm 2 0 2 o", "in 2 o 2 0", "and 2 o 2 0", "i'm 2 o 2 0", "in 2o2o", "and 2o2o", "i'm 2o2o", "in 2 o 2 o", "and 2 o 2 o", "i'm 2 o 2 o", "in 2020", "and 2020", "i'm 2020", "in 2 0 2 0", "and 2 0 2 0", "i'm 2 0 2 0", "in2020", "and2020", "i'm2020", "M 202 o", "M 2o2 0", "M 202 o ", "M 202 0", "M 20 2 o ", "M 20 20", "M 20 2 o", "M 2o 2 0", "M 2o 2 o", "M 20 2 0", "M 2 0 2 o", "M 2 o 2 0", "M 2o2o", "M 2 o 2 o", "M 2020", "2 020", "2020", "M 2 0 2 0", "M2020", "N 202 o", "N 2o2 0", "N 202 o ", "N 202 0", "N 20 2 o ", "N 20 20", "N 20 2 o", "N 2o 2 0", "N 2o 2 o", "N 20 2 0", "N 2 0 2 o", "N 2 o 2 0", "N 2o2o", "N 2 o 2 o", "N 2020", "N2 020", "N 2 0 2 0", "N2020"]}, {"code": "B1000", "variations": ["B 100 o", "B 1o0 0", "B 100 o ", "B 100 0", "B 10 0 o", "B 1o 0 0", "B 1o o o", "B 10 0 0", "B 1 0 0 o", "B 1 o 0 0", "B 1ooo", "B 1 o o o", "B 1000", "B1 000", "B 1 0 0 0", "B1000"]}, {"code": "B0200", "variations": ["B 0 20o", "B o 200", "B 0 200", "B 0 20 o", "B o 20 0", "B o 2o o", "B 0 20 0", "B 020o", "B o200", "B 0 2 0 o", "B o 2 0 0", "B o2oo", "B o 2 o o", "B 0200", "B02 00", "B 0 2 0 0", "B0200"]}, {"code": "M2030", "variations": ["in 203 o", "and 203 o", "i'm 203 o", "in 2o3 0", "and 2o3 0", "i'm 2o3 0", "in 203 o ", "and 203 o ", "i'm 203 o ", "in 203 0", "and 203 0", "i'm 203 0", "in 2 o 3o", "and 2 o 3o", "i'm 2 o 3o", "in 2 0 3o", "and 2 0 3o", "i'm 2 0 3o", "in 2 o 30", "and 2 o 30", "i'm 2 o 30", "in 20 30", "and 20 30", "i'm 20 30", "in 2 0 30", "and 2 0 30", "i'm 2 0 30", "in 20 3 o", "and 20 3 o", "i'm 20 3 o", "in 2o 3 0", "and 2o 3 0", "i'm 2o 3 0", "in 2o 3 o", "and 2o 3 o", "i'm 2o 3 o", "in 20 3 0", "and 20 3 0", "i'm 20 3 0", "in 2 0 3 o", "and 2 0 3 o", "i'm 2 0 3 o", "in 2 o 3 0", "and 2 o 3 0", "i'm 2 o 3 0", "in 2o3o", "and 2o3o", "i'm 2o3o", "in 2 o 3 o", "and 2 o 3 o", "i'm 2 o 3 o", "in 2030", "and 2030", "i'm 2030", "in 2 0 3 0", "and 2 0 3 0", "i'm 2 0 3 0", "in2030", "and2030", "i'm2030", "M 203 o", "M 2o3 0", "M 203 o ", "M 203 0", "M 2 o 3o", "M 2 0 3o", "M 2 o 30", "M 20 30", "M 2 0 30", "M 20 3 o", "M 2o 3 0", "M 2o 3 o", "M 20 3 0", "M 2 0 3 o", "M 2 o 3 0", "M 2o3o", "M 2 o 3 o", "M 2030", "2 030", "2030", "M 2 0 3 0", "M2030", "m 23rd", "N 203 o", "N 2o3 0", "N 203 o ", "N 203 0", "N 2 o 3o", "N 2 0 3o", "N 2 o 30", "N 20 30", "N 2 0 30", "N 20 3 o", "N 2o 3 0", "N 2o 3 o", "N 20 3 0", "N 2 0 3 o", "N 2 o 3 0", "N 2o3o", "N 2 o 3 o", "N 2030", "N2 030", "N 2 0 3 0", "N2030"]}, {"code": "GG0100A", "variations": ["G G 0100 A", "G G 0 10o A", "G G o 100 A", "G G 0 100 A", "G G 0 10 o A", "G G o 10 0 A", "G G o 1o o A", "G G 0 10 0 A", "GG 010o A", "GG o100 A", "G G 0 1 0 o A", "G G o 1 0 0 A", "GG o1oo A", "G G o 1 o o A", "GG 0100 A", "GG01 00A", "G G 0 1 0 0 A", "GG0100A"]}, {"code": "GG0100B", "variations": ["G G 0100 B", "G G 0 10o B", "G G o 100 B", "G G 0 100 B", "G G 0 10 o B", "G G o 10 0 B", "G G o 1o o B", "G G 0 10 0 B", "GG 010o B", "GG o100 B", "G G 0 1 0 o B", "G G o 1 0 0 B", "GG o1oo B", "G G o 1 o o B", "GG 0100 B", "GG01 00B", "G G 0 1 0 0 B", "GG0100B"]}, {"code": "GG0100C", "variations": ["G G 0100 C", "G G 0 10o C", "G G o 100 C", "G G 0 100 C", "G G 0 10 o C", "G G o 10 0 C", "G G o 1o o C", "G G 0 10 0 C", "GG 010o C", "GG o100 C", "G G 0 1 0 o C", "G G o 1 0 0 C", "GG o1oo C", "G G o 1 o o C", "GG 0100 C", "GG01 00C", "G G 0 1 0 0 C", "GG0100C"]}, {"code": "GG0100D", "variations": ["G G 0100 D", "G G 0 10o D", "G G o 100 D", "G G 0 100 D", "G G 0 10 o D", "G G o 10 0 D", "G G o 1o o D", "G G 0 10 0 D", "GG 010o D", "GG o100 D", "G G 0 1 0 o D", "G G o 1 0 0 D", "GG o1oo D", "G G o 1 o o D", "GG 0100 D", "GG01 00D", "G G 0 1 0 0 D", "GG0100D"]}, {"code": "GG0110", "variations": ["G G 0110", "G G 0 11o", "G G o 110", "G G 0 110", "G G 0 1 1o", "G G o 1 10", "G G o 1 1o", "G G 0 1 10", "G G 0 11 o", "G G o 11 0", "G G o 11 o", "G G 0 11 0", "GG 011o", "GG o110", "G G 0 1 1 o", "G G o 1 1 0", "GG o11o", "G G o 1 1 o", "GG 0110", "GG011 0", "G G 0 1 1 0", "GG0110", "gg 110th", "gg 110", "gg110", "g g 110", "g g 110th"]}, {"code": "A1250", "variations": ["A 1 250", "A 125 o", "A 125 o ", "A 125 0", "A 1 2 5o", "A 12 50", "A 1 2 50", "A 12 5 o", "A 12 5 0", "A 125o", "A 1 2 5 o", "A 1250", "A12 50", "A 1 2 5 0", "A1250"]}, {"code": "M1100", "variations": ["in 1 100", "and 1 100", "i'm 1 100", "in 110 o", "and 110 o", "i'm 110 o", "in 11o 0", "and 11o 0", "i'm 11o 0", "in 110 o ", "and 110 o ", "i'm 110 o ", "in 110 0", "and 110 0", "i'm 110 0", "in 11 0 o", "and 11 0 o", "i'm 11 0 o", "in 11 o 0", "and 11 o 0", "i'm 11 o 0", "in 11 o o", "and 11 o o", "i'm 11 o o", "in 11 0 0", "and 11 0 0", "i'm 11 0 0", "in 1 1 0 o", "and 1 1 0 o", "i'm 1 1 0 o", "in 1 1 o 0", "and 1 1 o 0", "i'm 1 1 o 0", "in 11oo", "and 11oo", "i'm 11oo", "in 1 1 o o", "and 1 1 o o", "i'm 1 1 o o", "in 1100", "and 1100", "i'm 1100", "in 1 1 0 0", "and 1 1 0 0", "i'm 1 1 0 0", "in1100", "and1100", "i'm1100", "M 1 100", "M 110 o", "M 11o 0", "M 110 o ", "M 110 0", "M 11 0 o", "M 11 o 0", "M 11 o o", "M 11 0 0", "M 1 1 0 o", "M 1 1 o 0", "M 11oo", "M 1 1 o o", "M 1100", "11 00", "1100", "M 1 1 0 0", "M1100", "N 1 100", "N 110 o", "N 11o 0", "N 110 o ", "N 110 0", "N 11 0 o", "N 11 o 0", "N 11 o o", "N 11 0 0", "N 1 1 0 o", "N 1 1 o 0", "N 11oo", "N 1 1 o o", "N 1100", "N11 00", "N 1 1 0 0", "N1100"]}, {"code": "M2102F", "variations": ["in 210 2 f", "and 210 2 f", "i'm 210 2 f", "in 21 o 2 f", "and 21 o 2 f", "i'm 21 o 2 f", "in 21 0 2 f", "and 21 0 2 f", "i'm 21 0 2 f", "in 21o2 f", "and 21o2 f", "i'm 21o2 f", "in 2 1 o 2 f", "and 2 1 o 2 f", "i'm 2 1 o 2 f", "in 2102 f", "and 2102 f", "i'm 2102 f", "21 02f", "2102f", "in 2 1 0 2 f", "and 2 1 0 2 f", "i'm 2 1 0 2 f", "in2102f", "and2102f", "i'm2102f", "M 210 2 F", "M 21 o 2 F", "M 21 0 2 F", "M 21o2 F", "M 2 1 o 2 F", "M 2102 F", "21 02F", "2102F", "M 2 1 0 2 F", "M2102F", "N 210 2 F", "N 21 o 2 F", "N 21 0 2 F", "N 21o2 F", "N 2 1 o 2 F", "N 2102 F", "N21 02F", "N 2 1 0 2 F", "N2102F"]}, {"code": "J0510", "variations": ["J 0 51o", "J o 510", "J 0 510", "J 0 5 1o", "J o 5 10", "J o 5 1o", "J 0 5 10", "J 0 51 o", "J o 51 0", "J o 51 o", "J 0 51 0", "J 051o", "J o510", "J 0 5 1 o", "J o 5 1 0", "J o51o", "J o 5 1 o", "J 0510", "J051 0", "J 0 5 1 0", "J0510"]}, {"code": "J0520", "variations": ["J 0 52o", "J o 520", "J 0 520", "J 0 5 2o", "J o 5 20", "J o 5 2o", "J 0 5 20", "J 0 52 o", "J o 52 0", "J o 52 o", "J 0 52 0", "J 052o", "J o520", "J 0 5 2 o", "J o 5 2 0", "J o52o", "J o 5 2 o", "J 0520", "J052 0", "J 0 5 2 0", "J0520"]}, {"code": "J0530", "variations": ["J 0 53o", "J o 530", "J 0 530", "J 0 5 3o", "J o 5 30", "J o 5 3o", "J 0 5 30", "J 0 53 o", "J o 53 0", "J o 53 o", "J 0 53 0", "J 053o", "J o530", "J 0 5 3 o", "J o 5 3 0", "J o53o", "J o 5 3 o", "J 0530", "J053 0", "J 0 5 3 0", "J0530"]}, {"code": "M1600", "variations": ["in 1 600", "and 1 600", "i'm 1 600", "in 160 o", "and 160 o", "i'm 160 o", "in 16o 0", "and 16o 0", "i'm 16o 0", "in 160 o ", "and 160 o ", "i'm 160 o ", "in 160 0", "and 160 0", "i'm 160 0", "in 16 0 o", "and 16 0 o", "i'm 16 0 o", "in 16 o 0", "and 16 o 0", "i'm 16 o 0", "in 16 o o", "and 16 o o", "i'm 16 o o", "in 16 0 0", "and 16 0 0", "i'm 16 0 0", "in 1 6 0 o", "and 1 6 0 o", "i'm 1 6 0 o", "in 1 6 o 0", "and 1 6 o 0", "i'm 1 6 o 0", "in 16oo", "and 16oo", "i'm 16oo", "in 1 6 o o", "and 1 6 o o", "i'm 1 6 o o", "in 1600", "and 1600", "i'm 1600", "in 1 6 0 0", "and 1 6 0 0", "i'm 1 6 0 0", "in1600", "and1600", "i'm1600", "M 1 600", "M 160 o", "M 16o 0", "M 160 o ", "M 160 0", "M 16 0 o", "M 16 o 0", "M 16 o o", "M 16 0 0", "M 1 6 0 o", "M 1 6 o 0", "M 16oo", "M 1 6 o o", "M 1600", "16 00", "1600", "M 1 6 0 0", "M1600", "N 1 600", "N 160 o", "N 16o 0", "N 160 o ", "N 160 0", "N 16 0 o", "N 16 o 0", "N 16 o o", "N 16 0 0", "N 1 6 0 o", "N 1 6 o 0", "N 16oo", "N 1 6 o o", "N 1600", "N16 00", "N 1 6 0 0", "N1600"]}, {"code": "M1610", "variations": ["in 1 610", "and 1 610", "i'm 1 610", "in 161 o", "and 161 o", "i'm 161 o", "in 161 o ", "and 161 o ", "i'm 161 o ", "in 161 0", "and 161 0", "i'm 161 0", "in 1 6 1o", "and 1 6 1o", "i'm 1 6 1o", "in 16 10", "and 16 10", "i'm 16 10", "in 1 6 10", "and 1 6 10", "i'm 1 6 10", "in 16 1 o", "and 16 1 o", "i'm 16 1 o", "in 16 1 0", "and 16 1 0", "i'm 16 1 0", "in 161o", "and 161o", "i'm 161o", "in 1 6 1 o", "and 1 6 1 o", "i'm 1 6 1 o", "in 1610", "and 1610", "i'm 1610", "in 1 6 1 0", "and 1 6 1 0", "i'm 1 6 1 0", "in1610", "and1610", "i'm1610", "M 1 610", "M 161 o", "M 161 o ", "M 161 0", "M 1 6 1o", "M 16 10", "M 1 6 10", "M 16 1 o", "M 16 1 0", "M 161o", "M 1 6 1 o", "M 1610", "16 10", "1610", "M 1 6 1 0", "M1610", "N 1 610", "N 161 o", "N 161 o ", "N 161 0", "N 1 6 1o", "N 16 10", "N 1 6 10", "N 16 1 o", "N 16 1 0", "N 161o", "N 1 6 1 o", "N 1610", "N16 10", "N 1 6 1 0", "N1610"]}, {"code": "M1620", "variations": ["in 1 620", "and 1 620", "i'm 1 620", "in 162 o", "and 162 o", "i'm 162 o", "in 162 o ", "and 162 o ", "i'm 162 o ", "in 162 0", "and 162 0", "i'm 162 0", "in 1 6 2o", "and 1 6 2o", "i'm 1 6 2o", "in 16 20", "and 16 20", "i'm 16 20", "in 1 6 20", "and 1 6 20", "i'm 1 6 20", "in 16 2 o", "and 16 2 o", "i'm 16 2 o", "in 16 2 0", "and 16 2 0", "i'm 16 2 0", "in 162o", "and 162o", "i'm 162o", "in 1 6 2 o", "and 1 6 2 o", "i'm 1 6 2 o", "in 1620", "and 1620", "i'm 1620", "in 1 6 2 0", "and 1 6 2 0", "i'm 1 6 2 0", "in1620", "and1620", "i'm1620", "M 1 620", "M 162 o", "M 162 o ", "M 162 0", "M 1 6 2o", "M 16 20", "M 1 6 20", "M 16 2 o", "M 16 2 0", "M 162o", "M 1 6 2 o", "M 1620", "16 20", "1620", "M 1 6 2 0", "M1620", "N 1 620", "N 162 o", "N 162 o ", "N 162 0", "N 1 6 2o", "N 16 20", "N 1 6 20", "N 16 2 o", "N 16 2 0", "N 162o", "N 1 6 2 o", "N 1620", "N16 20", "N 1 6 2 0", "N1620"]}, {"code": "M1630", "variations": ["in 1 630", "and 1 630", "i'm 1 630", "in 163 o", "and 163 o", "i'm 163 o", "in 163 o ", "and 163 o ", "i'm 163 o ", "in 163 0", "and 163 0", "i'm 163 0", "in 1 6 3o", "and 1 6 3o", "i'm 1 6 3o", "in 16 30", "and 16 30", "i'm 16 30", "in 1 6 30", "and 1 6 30", "i'm 1 6 30", "in 16 3 o", "and 16 3 o", "i'm 16 3 o", "in 16 3 0", "and 16 3 0", "i'm 16 3 0", "in 163o", "and 163o", "i'm 163o", "in 1 6 3 o", "and 1 6 3 o", "i'm 1 6 3 o", "in 1630", "and 1630", "i'm 1630", "in 1 6 3 0", "and 1 6 3 0", "i'm 1 6 3 0", "in1630", "and1630", "i'm1630", "M 1 630", "M 163 o", "M 163 o ", "M 163 0", "M 1 6 3o", "M 16 30", "M 1 6 30", "M 16 3 o", "M 16 3 0", "M 163o", "M 1 6 3 o", "M 1630", "16 30", "1630", "M 1 6 3 0", "M1630", "N 1 630", "N 163 o", "N 163 o ", "N 163 0", "N 1 6 3o", "N 16 30", "N 1 6 30", "N 16 3 o", "N 16 3 0", "N 163o", "N 1 6 3 o", "N 1630", "N16 30", "N 1 6 3 0", "N1630"]}, {"code": "K0520", "variations": ["K 0 52o", "K o 520", "K 0 520", "K 0 5 2o", "K o 5 20", "K o 5 2o", "K 0 5 20", "K 0 52 o", "K o 52 0", "K o 52 o", "K 0 52 0", "K 052o", "K o520", "K 0 5 2 o", "K o 5 2 0", "K o52o", "K o 5 2 o", "K 0520", "K052 0", "K 0 5 2 0", "K0520"]}, {"code": "B1300", "variations": ["B 1 300", "B 130 o", "B 13o 0", "B 130 o ", "B 130 0", "B 13 0 o", "B 13 o 0", "B 13 o o", "B 13 0 0", "B 1 3 0 o", "B 1 3 o 0", "B 13oo", "B 1 3 o o", "B 1300", "B13 00", "B 1 3 0 0", "B1300"]}, {"code": "C0100", "variations": ["C 0 10o", "C o 100", "C 0 100", "C 0 10 o", "C o 10 0", "C o 1o o", "C 0 10 0", "C 010o", "C o100", "C 0 1 0 o", "C o 1 0 0", "C o1oo", "C o 1 o o", "C 0100", "C01 00", "C 0 1 0 0", "C0100"]}, {"code": "C0200", "variations": ["C 0 20o", "C o 200", "C 0 200", "C 0 20 o", "C o 20 0", "C o 2o o", "C 0 20 0", "C 020o", "C o200", "C 0 2 0 o", "C o 2 0 0", "C o2oo", "C o 2 o o", "C 0200", "C02 00", "C 0 2 0 0", "C0200"]}, {"code": "C0300A", "variations": ["C 0 30o A", "C o 300 A", "C 0 300 A", "C 0 30 o A", "C o 30 0 A", "C o 3o o A", "C 0 30 0 A", "C 030o A", "C o300 A", "C 0 3 0 o A", "C o 3 0 0 A", "C o3oo A", "C o 3 o o A", "C 0300 A", "C03 00A", "C 0 3 0 0 A", "C0300A"]}, {"code": "C0300B", "variations": ["C 0 30o B", "C o 300 B", "C 0 300 B", "C 0 30 o B", "C o 30 0 B", "C o 3o o B", "C 0 30 0 B", "C 030o B", "C o300 B", "C 0 3 0 o B", "C o 3 0 0 B", "C o3oo B", "C o 3 o o B", "C 0300 B", "C03 00B", "C 0 3 0 0 B", "C0300B"]}, {"code": "C0300C", "variations": ["C 0 30o C", "C o 300 C", "C 0 300 C", "C 0 30 o C", "C o 30 0 C", "C o 3o o C", "C 0 30 0 C", "C 030o C", "C o300 C", "C 0 3 0 o C", "C o 3 0 0 C", "C o3oo C", "C o 3 o o C", "C 0300 C", "C03 00C", "C 0 3 0 0 C", "C0300C"]}, {"code": "C0400A", "variations": ["C 0 40o A", "C o 400 A", "C 0 400 A", "C 0 40 o A", "C o 40 0 A", "C o 4o o A", "C 0 40 0 A", "C 040o A", "C o400 A", "C 0 4 0 o A", "C o 4 0 0 A", "C o4oo A", "C o 4 o o A", "C 0400 A", "C04 00A", "C 0 4 0 0 A", "C0400A"]}, {"code": "C0400B", "variations": ["C 0 40o B", "C o 400 B", "C 0 400 B", "C 0 40 o B", "C o 40 0 B", "C o 4o o B", "C 0 40 0 B", "C 040o B", "C o400 B", "C 0 4 0 o B", "C o 4 0 0 B", "C o4oo B", "C o 4 o o B", "C 0400 B", "C04 00B", "C 0 4 0 0 B", "C0400B"]}, {"code": "C0400C", "variations": ["C 0 40o C", "C o 400 C", "C 0 400 C", "C 0 40 o C", "C o 40 0 C", "C o 4o o C", "C 0 40 0 C", "C 040o C", "C o400 C", "C 0 4 0 o C", "C o 4 0 0 C", "C o4oo C", "C o 4 o o C", "C 0400 C", "C04 00C", "C 0 4 0 0 C", "C0400C"]}, {"code": "C0500", "variations": ["C 0 50o", "C o 500", "C 0 500", "C 0 50 o", "C o 50 0", "C o 5o o", "C 0 50 0", "C 050o", "C o500", "C 0 5 0 o", "C o 5 0 0", "C o5oo", "C o 5 o o", "C 0500", "C05 00", "C 0 5 0 0", "C0500"]}, {"code": "C1310A", "variations": ["C 131 o A", "C 131 0 A", "C 13 10 A", "C 1 3 10 A", "C 13 1 o A", "C 13 1 0 A", "C 131o A", "C 1 3 1 o A", "C 1310 A", "C13 10A", "C 1 3 1 0 A", "C1310A"]}, {"code": "C1310B", "variations": ["C 131 o B", "C 131 0 B", "C 13 10 B", "C 1 3 10 B", "C 13 1 o B", "C 13 1 0 B", "C 131o B", "C 1 3 1 o B", "C 1310 B", "C13 10B", "C 1 3 1 0 B", "C1310B"]}, {"code": "C1310C", "variations": ["C 131 o C", "C 131 0 C", "C 13 10 C", "C 1 3 10 C", "C 13 1 o C", "C 13 1 0 C", "C 131o C", "C 1 3 1 o C", "C 1310 C", "C13 10C", "C 1 3 1 0 C", "C1310C"]}, {"code": "C1310D", "variations": ["C 131 o D", "C 131 0 D", "C 13 10 D", "C 1 3 10 D", "C 13 1 o D", "C 13 1 0 D", "C 131o D", "C 1 3 1 o D", "C 1310 D", "C13 10D", "C 1 3 1 0 D", "C1310D"]}, {"code": "M1700", "variations": ["in 1 700", "and 1 700", "i'm 1 700", "in 170 o", "and 170 o", "i'm 170 o", "in 17o 0", "and 17o 0", "i'm 17o 0", "in 170 o ", "and 170 o ", "i'm 170 o ", "in 170 0", "and 170 0", "i'm 170 0", "in 17 0 o", "and 17 0 o", "i'm 17 0 o", "in 17 o 0", "and 17 o 0", "i'm 17 o 0", "in 17 o o", "and 17 o o", "i'm 17 o o", "in 17 0 0", "and 17 0 0", "i'm 17 0 0", "in 1 7 0 o", "and 1 7 0 o", "i'm 1 7 0 o", "in 1 7 o 0", "and 1 7 o 0", "i'm 1 7 o 0", "in 17oo", "and 17oo", "i'm 17oo", "in 1 7 o o", "and 1 7 o o", "i'm 1 7 o o", "in 1700", "and 1700", "i'm 1700", "in 1 7 0 0", "and 1 7 0 0", "i'm 1 7 0 0", "in1700", "and1700", "i'm1700", "M 1 700", "M 170 o", "M 17o 0", "M 170 o ", "M 170 0", "M 17 0 o", "M 17 o 0", "M 17 o o", "M 17 0 0", "M 1 7 0 o", "M 1 7 o 0", "M 17oo", "M 1 7 o o", "M 1700", "17 00", "1700", "M 1 7 0 0", "M1700", "N 1 700", "N 170 o", "N 17o 0", "N 170 o ", "N 170 0", "N 17 0 o", "N 17 o 0", "N 17 o o", "N 17 0 0", "N 1 7 0 o", "N 1 7 o 0", "N 17oo", "N 1 7 o o", "N 1700", "N17 00", "N 1 7 0 0", "N1700"]}, {"code": "M1710", "variations": ["in 1 710", "and 1 710", "i'm 1 710", "in 171 o", "and 171 o", "i'm 171 o", "in 171 o ", "and 171 o ", "i'm 171 o ", "in 171 0", "and 171 0", "i'm 171 0", "in 1 7 1o", "and 1 7 1o", "i'm 1 7 1o", "in 17 10", "and 17 10", "i'm 17 10", "in 1 7 10", "and 1 7 10", "i'm 1 7 10", "in 17 1 o", "and 17 1 o", "i'm 17 1 o", "in 17 1 0", "and 17 1 0", "i'm 17 1 0", "in 171o", "and 171o", "i'm 171o", "in 1 7 1 o", "and 1 7 1 o", "i'm 1 7 1 o", "in 1710", "and 1710", "i'm 1710", "in 1 7 1 0", "and 1 7 1 0", "i'm 1 7 1 0", "in1710", "and1710", "i'm1710", "M 1 710", "M 171 o", "M 171 o ", "M 171 0", "M 1 7 1o", "M 17 10", "M 1 7 10", "M 17 1 o", "M 17 1 0", "M 171o", "M 1 7 1 o", "M 1710", "17 10", "1710", "M 1 7 1 0", "M1710", "N 1 710", "N 171 o", "N 171 o ", "N 171 0", "N 1 7 1o", "N 17 10", "N 1 7 10", "N 17 1 o", "N 17 1 0", "N 171o", "N 1 7 1 o", "N 1710", "N17 10", "N 1 7 1 0", "N1710"]}, {"code": "M1720", "variations": ["in 1 720", "and 1 720", "i'm 1 720", "in 172 o", "and 172 o", "i'm 172 o", "in 172 o ", "and 172 o ", "i'm 172 o ", "in 172 0", "and 172 0", "i'm 172 0", "in 1 7 2o", "and 1 7 2o", "i'm 1 7 2o", "in 17 20", "and 17 20", "i'm 17 20", "in 1 7 20", "and 1 7 20", "i'm 1 7 20", "in 17 2 o", "and 17 2 o", "i'm 17 2 o", "in 17 2 0", "and 17 2 0", "i'm 17 2 0", "in 172o", "and 172o", "i'm 172o", "in 1 7 2 o", "and 1 7 2 o", "i'm 1 7 2 o", "in 1720", "and 1720", "i'm 1720", "in 1 7 2 0", "and 1 7 2 0", "i'm 1 7 2 0", "in1720", "and1720", "i'm1720", "M 1 720", "M 172 o", "M 172 o ", "M 172 0", "M 1 7 2o", "M 17 20", "M 1 7 20", "M 17 2 o", "M 17 2 0", "M 172o", "M 1 7 2 o", "M 1720", "17 20", "1720", "M 1 7 2 0", "M1720", "N 1 720", "N 172 o", "N 172 o ", "N 172 0", "N 1 7 2o", "N 17 20", "N 1 7 20", "N 17 2 o", "N 17 2 0", "N 172o", "N 1 7 2 o", "N 1720", "N17 20", "N 1 7 2 0", "N1720"]}, {"code": "D0150A1", "variations": ["D 0 15o A 1", "D o 150 A 1", "D 0 150 A 1", "D 0 15 o A 1", "D o 15 0 A 1", "D o 15 o A 1", "D 0 15 0 A 1", "D 015o A 1", "D o150 A 1", "D 0 1 5 o A 1", "D o 1 5 0 A 1", "D o15o A 1", "D o 1 5 o A 1", "D 0150 A 1", "D015 0A1", "D 0 1 5 0 A 1", "D0150A1"]}, {"code": "D0150A2", "variations": ["D 0 15o A 2", "D o 150 A 2", "D 0 150 A 2", "D 0 15 o A 2", "D o 15 0 A 2", "D o 15 o A 2", "D 0 15 0 A 2", "D 015o A 2", "D o150 A 2", "D 0 1 5 o A 2", "D o 1 5 0 A 2", "D o15o A 2", "D o 1 5 o A 2", "D 0150 A 2", "D015 0A2", "D 0 1 5 0 A 2", "D0150A2"]}, {"code": "D0150B1", "variations": ["D 0 15o B 1", "D o 150 B 1", "D 0 150 B 1", "D 0 15 o B 1", "D o 15 0 B 1", "D o 15 o B 1", "D 0 15 0 B 1", "D 015o B 1", "D o150 B 1", "D 0 1 5 o B 1", "D o 1 5 0 B 1", "D o15o B 1", "D o 1 5 o B 1", "D 0150 B 1", "D015 0B1", "D 0 1 5 0 B 1", "D0150B1"]}, {"code": "D0150B2", "variations": ["D 0 15o B 2", "D o 150 B 2", "D 0 150 B 2", "D 0 15 o B 2", "D o 15 0 B 2", "D o 15 o B 2", "D 0 15 0 B 2", "D 015o B 2", "D o150 B 2", "D 0 1 5 o B 2", "D o 1 5 0 B 2", "D o15o B 2", "D o 1 5 o B 2", "D 0150 B 2", "D015 0B2", "D 0 1 5 0 B 2", "D0150B2"]}, {"code": "D0150C1", "variations": ["D 0 15o C 1", "D o 150 C 1", "D 0 150 C 1", "D 0 15 o C 1", "D o 15 0 C 1", "D o 15 o C 1", "D 0 15 0 C 1", "D 015o C 1", "D o150 C 1", "D 0 1 5 o C 1", "D o 1 5 0 C 1", "D o15o C 1", "D o 1 5 o C 1", "D 0150 C 1", "D015 0C1", "D 0 1 5 0 C 1", "D0150C1"]}, {"code": "D0150C2", "variations": ["D 0 15o C 2", "D o 150 C 2", "D 0 150 C 2", "D 0 15 o C 2", "D o 15 0 C 2", "D o 15 o C 2", "D 0 15 0 C 2", "D 015o C 2", "D o150 C 2", "D 0 1 5 o C 2", "D o 1 5 0 C 2", "D o15o C 2", "D o 1 5 o C 2", "D 0150 C 2", "D015 0C2", "D 0 1 5 0 C 2", "D0150C2"]}, {"code": "D0150D1", "variations": ["D 0 15o D 1", "D o 150 D 1", "D 0 150 D 1", "D 0 15 o D 1", "D o 15 0 D 1", "D o 15 o D 1", "D 0 15 0 D 1", "D 015o D 1", "D o150 D 1", "D 0 1 5 o D 1", "D o 1 5 0 D 1", "D o15o D 1", "D o 1 5 o D 1", "D 0150 D 1", "D015 0D1", "D 0 1 5 0 D 1", "D0150D1"]}, {"code": "D0150D2", "variations": ["D 0 15o D 2", "D o 150 D 2", "D 0 150 D 2", "D 0 15 o D 2", "D o 15 0 D 2", "D o 15 o D 2", "D 0 15 0 D 2", "D 015o D 2", "D o150 D 2", "D 0 1 5 o D 2", "D o 1 5 0 D 2", "D o15o D 2", "D o 1 5 o D 2", "D 0150 D 2", "D015 0D2", "D 0 1 5 0 D 2", "D0150D2"]}, {"code": "D0150E1", "variations": ["D 0 15o E 1", "D o 150 E 1", "D 0 150 E 1", "D 0 15 o E 1", "D o 15 0 E 1", "D o 15 o E 1", "D 0 15 0 E 1", "D 015o E 1", "D o150 E 1", "D 0 1 5 o E 1", "D o 1 5 0 E 1", "D o15o E 1", "D o 1 5 o E 1", "D 0150 E 1", "D015 0E1", "D 0 1 5 0 E 1", "D0150E1"]}, {"code": "D0150E2", "variations": ["D 0 15o E 2", "D o 150 E 2", "D 0 150 E 2", "D 0 15 o E 2", "D o 15 0 E 2", "D o 15 o E 2", "D 0 15 0 E 2", "D 015o E 2", "D o150 E 2", "D 0 1 5 o E 2", "D o 1 5 0 E 2", "D o15o E 2", "D o 1 5 o E 2", "D 0150 E 2", "D015 0E2", "D 0 1 5 0 E 2", "D0150E2"]}, {"code": "D0150F1", "variations": ["D 0 15o F 1", "D o 150 F 1", "D 0 150 F 1", "D 0 15 o F 1", "D o 15 0 F 1", "D o 15 o F 1", "D 0 15 0 F 1", "D 015o F 1", "D o150 F 1", "D 0 1 5 o F 1", "D o 1 5 0 F 1", "D o15o F 1", "D o 1 5 o F 1", "D 0150 F 1", "D015 0F1", "D 0 1 5 0 F 1", "D0150F1"]}, {"code": "D0150F2", "variations": ["D 0 15o F 2", "D o 150 F 2", "D 0 150 F 2", "D 0 15 o F 2", "D o 15 0 F 2", "D o 15 o F 2", "D 0 15 0 F 2", "D 015o F 2", "D o150 F 2", "D 0 1 5 o F 2", "D o 1 5 0 F 2", "D o15o F 2", "D o 1 5 o F 2", "D 0150 F 2", "D015 0F2", "D 0 1 5 0 F 2", "D0150F2"]}, {"code": "D0150G1", "variations": ["D 0 15o G 1", "D o 150 G 1", "D 0 150 G 1", "D 0 15 o G 1", "D o 15 0 G 1", "D o 15 o G 1", "D 0 15 0 G 1", "D 015o G 1", "D o150 G 1", "D 0 1 5 o G 1", "D o 1 5 0 G 1", "D o15o G 1", "D o 1 5 o G 1", "D 0150 G 1", "D015 0G1", "D 0 1 5 0 G 1", "D0150G1"]}, {"code": "D0150G2", "variations": ["D 0 15o G 2", "D o 150 G 2", "D 0 150 G 2", "D 0 15 o G 2", "D o 15 0 G 2", "D o 15 o G 2", "D 0 15 0 G 2", "D 015o G 2", "D o150 G 2", "D 0 1 5 o G 2", "D o 1 5 0 G 2", "D o15o G 2", "D o 1 5 o G 2", "D 0150 G 2", "D015 0G2", "D 0 1 5 0 G 2", "D0150G2"]}, {"code": "D0150H1", "variations": ["D 0 15o H 1", "D o 150 H 1", "D 0 150 H 1", "D 0 15 o H 1", "D o 15 0 H 1", "D o 15 o H 1", "D 0 15 0 H 1", "D 015o H 1", "D o150 H 1", "D 0 1 5 o H 1", "D o 1 5 0 H 1", "D o15o H 1", "D o 1 5 o H 1", "D 0150 H 1", "D015 0H1", "D 0 1 5 0 H 1", "D0150H1"]}, {"code": "D0150H2", "variations": ["D 0 15o H 2", "D o 150 H 2", "D 0 150 H 2", "D 0 15 o H 2", "D o 15 0 H 2", "D o 15 o H 2", "D 0 15 0 H 2", "D 015o H 2", "D o150 H 2", "D 0 1 5 o H 2", "D o 1 5 0 H 2", "D o15o H 2", "D o 1 5 o H 2", "D 0150 H 2", "D015 0H2", "D 0 1 5 0 H 2", "D0150H2"]}, {"code": "D0150I1", "variations": ["D 0 15o I 1", "D o 150 I 1", "D 0 150 I 1", "D 0 15 o I 1", "D o 15 0 I 1", "D o 15 o I 1", "D 0 15 0 I 1", "D 015o I 1", "D o150 I 1", "D 0 1 5 o I 1", "D o 1 5 0 I 1", "D o15o I 1", "D o 1 5 o I 1", "D 0150 I 1", "D015 0I1", "D 0 1 5 0 I 1", "D0150I1"]}, {"code": "D0150I2", "variations": ["D 0 15o I 2", "D o 150 I 2", "D 0 150 I 2", "D 0 15 o I 2", "D o 15 0 I 2", "D o 15 o I 2", "D 0 15 0 I 2", "D 015o I 2", "D o150 I 2", "D 0 1 5 o I 2", "D o 1 5 0 I 2", "D o15o I 2", "D o 1 5 o I 2", "D 0150 I 2", "D015 0I2", "D 0 1 5 0 I 2", "D0150I2"]}, {"code": "D0160", "variations": ["D 0 16o", "D o 160", "D 0 160", "D 0 1 6o", "D o 1 60", "D o 1 6o", "D 0 1 60", "D 0 16 o", "D o 16 0", "D o 16 o", "D 0 16 0", "D 016o", "D o160", "D 0 1 6 o", "D o 1 6 0", "D o16o", "D o 1 6 o", "D 0160", "D016 0", "D 0 1 6 0", "D0160"]}, {"code": "D0700", "variations": ["D 0 70o", "D o 700", "D 0 700", "D 0 70 o", "D o 70 0", "D o 7o o", "D 0 70 0", "D 070o", "D o700", "D 0 7 0 o", "D o 7 0 0", "D o7oo", "D o 7 o o", "D 0700", "D07 00", "D 0 7 0 0", "D0700"]}, {"code": "M1740", "variations": ["in 1 740", "and 1 740", "i'm 1 740", "in 174 o", "and 174 o", "i'm 174 o", "in 174 o ", "and 174 o ", "i'm 174 o ", "in 174 0", "and 174 0", "i'm 174 0", "in 1 7 4o", "and 1 7 4o", "i'm 1 7 4o", "in 17 40", "and 17 40", "i'm 17 40", "in 1 7 40", "and 1 7 40", "i'm 1 7 40", "in 17 4 o", "and 17 4 o", "i'm 17 4 o", "in 17 4 0", "and 17 4 0", "i'm 17 4 0", "in 174o", "and 174o", "i'm 174o", "in 1 7 4 o", "and 1 7 4 o", "i'm 1 7 4 o", "in 1740", "and 1740", "i'm 1740", "in 1 7 4 0", "and 1 7 4 0", "i'm 1 7 4 0", "in1740", "and1740", "i'm1740", "M 1 740", "M 174 o", "M 174 o ", "M 174 0", "M 1 7 4o", "M 17 40", "M 1 7 40", "M 17 4 o", "M 17 4 0", "M 174o", "M 1 7 4 o", "M 1740", "17 40", "1740", "M 1 7 4 0", "M1740", "N 1 740", "N 174 o", "N 174 o ", "N 174 0", "N 1 7 4o", "N 17 40", "N 1 7 40", "N 17 4 o", "N 17 4 0", "N 174o", "N 1 7 4 o", "N 1740", "N17 40", "N 1 7 4 0", "N1740"]}, {"code": "M1745", "variations": ["in 1 745", "and 1 745", "i'm 1 745", "in 174 5", "and 174 5", "i'm 174 5", "in 17 45", "and 17 45", "i'm 17 45", "in 1 7 45", "and 1 7 45", "i'm 1 7 45", "in 17 4 5", "and 17 4 5", "i'm 17 4 5", "in 1745", "and 1745", "i'm 1745", "in 1 7 4 5", "and 1 7 4 5", "i'm 1 7 4 5", "in1745", "and1745", "i'm1745", "M 1 745", "M 174 5", "M 17 45", "M 1 7 45", "M 17 4 5", "M 1745", "17 45", "1745", "M 1 7 4 5", "M1745", "N 1 745", "N 174 5", "N 17 45", "N 1 7 45", "N 17 4 5", "N 1745", "N17 45", "N 1 7 4 5", "N1745"]}, {"code": "M1060_HEIGHT", "variations": ["in 1060 _ h e i g h t", "and 1060 _ h e i g h t", "i'm 1060 _ h e i g h t", "in 1o6 0 _ h e i g h t", "and 1o6 0 _ h e i g h t", "i'm 1o6 0 _ h e i g h t", "in 106 o _ h e i g h t", "and 106 o _ h e i g h t", "i'm 106 o _ h e i g h t", "in 106 0 _ h e i g h t", "and 106 0 _ h e i g h t", "i'm 106 0 _ h e i g h t", "in 1 0 6o _ h e i g h t", "and 1 0 6o _ h e i g h t", "i'm 1 0 6o _ h e i g h t", "in 1 o 60 _ h e i g h t", "and 1 o 60 _ h e i g h t", "i'm 1 o 60 _ h e i g h t", "in 10 60 _ h e i g h t", "and 10 60 _ h e i g h t", "i'm 10 60 _ h e i g h t", "in 1 0 60 _ h e i g h t", "and 1 0 60 _ h e i g h t", "i'm 1 0 60 _ h e i g h t", "in 10 6 o _ h e i g h t", "and 10 6 o _ h e i g h t", "i'm 10 6 o _ h e i g h t", "in 1o 6 0 _ h e i g h t", "and 1o 6 0 _ h e i g h t", "i'm 1o 6 0 _ h e i g h t", "in 1o 6 o _ h e i g h t", "and 1o 6 o _ h e i g h t", "i'm 1o 6 o _ h e i g h t", "in 10 6 0 _ h e i g h t", "and 10 6 0 _ h e i g h t", "i'm 10 6 0 _ h e i g h t", "in 1 0 6 o _ h e i g h t", "and 1 0 6 o _ h e i g h t", "i'm 1 0 6 o _ h e i g h t", "in 1 o 6 0 _ h e i g h t", "and 1 o 6 0 _ h e i g h t", "i'm 1 o 6 0 _ h e i g h t", "in 1o6o_height", "and 1o6o_height", "i'm 1o6o_height", "in 1 o 6 o _ h e i g h t", "and 1 o 6 o _ h e i g h t", "i'm 1 o 6 o _ h e i g h t", "in 1060_height", "and 1060_height", "i'm 1060_height", "1 060_height", "1060_height", "in 1 0 6 0 _ h e i g h t", "and 1 0 6 0 _ h e i g h t", "i'm 1 0 6 0 _ h e i g h t", "in1060_height", "and1060_height", "i'm1060_height", "M 1060 _ H E I G H T", "M 1o6 0 _ H E I G H T", "M 106 o _ H E I G H T", "M 106 0 _ H E I G H T", "M 1 0 6o _ H E I G H T", "M 1 o 60 _ H E I G H T", "M 10 60 _ H E I G H T", "M 1 0 60 _ H E I G H T", "M 10 6 o _ H E I G H T", "M 1o 6 0 _ H E I G H T", "M 1o 6 o _ H E I G H T", "M 10 6 0 _ H E I G H T", "M 1 0 6 o _ H E I G H T", "M 1 o 6 0 _ H E I G H T", "M 1o6o_HEIGHT", "M 1 o 6 o _ H E I G H T", "M 1060_HEIGHT", "1 060_HEIGHT", "1060_HEIGHT", "M 1 0 6 0 _ H E I G H T", "M1060_HEIGHT", "N 1060 _ H E I G H T", "N 1o6 0 _ H E I G H T", "N 106 o _ H E I G H T", "N 106 0 _ H E I G H T", "N 1 0 6o _ H E I G H T", "N 1 o 60 _ H E I G H T", "N 10 60 _ H E I G H T", "N 1 0 60 _ H E I G H T", "N 10 6 o _ H E I G H T", "N 1o 6 0 _ H E I G H T", "N 1o 6 o _ H E I G H T", "N 10 6 0 _ H E I G H T", "N 1 0 6 o _ H E I G H T", "N 1 o 6 0 _ H E I G H T", "N 1o6o_HEIGHT", "N 1 o 6 o _ H E I G H T", "N 1060_HEIGHT", "N1 060_HEIGHT", "N 1 0 6 0 _ H E I G H T", "N1060_HEIGHT"]}, {"code": "M1060_WEIGHT", "variations": ["in 1060 _ w e i g h t", "and 1060 _ w e i g h t", "i'm 1060 _ w e i g h t", "in 1o6 0 _ w e i g h t", "and 1o6 0 _ w e i g h t", "i'm 1o6 0 _ w e i g h t", "in 106 o _ w e i g h t", "and 106 o _ w e i g h t", "i'm 106 o _ w e i g h t", "in 106 0 _ w e i g h t", "and 106 0 _ w e i g h t", "i'm 106 0 _ w e i g h t", "in 1 0 6o _ w e i g h t", "and 1 0 6o _ w e i g h t", "i'm 1 0 6o _ w e i g h t", "in 1 o 60 _ w e i g h t", "and 1 o 60 _ w e i g h t", "i'm 1 o 60 _ w e i g h t", "in 10 60 _ w e i g h t", "and 10 60 _ w e i g h t", "i'm 10 60 _ w e i g h t", "in 1 0 60 _ w e i g h t", "and 1 0 60 _ w e i g h t", "i'm 1 0 60 _ w e i g h t", "in 10 6 o _ w e i g h t", "and 10 6 o _ w e i g h t", "i'm 10 6 o _ w e i g h t", "in 1o 6 0 _ w e i g h t", "and 1o 6 0 _ w e i g h t", "i'm 1o 6 0 _ w e i g h t", "in 1o 6 o _ w e i g h t", "and 1o 6 o _ w e i g h t", "i'm 1o 6 o _ w e i g h t", "in 10 6 0 _ w e i g h t", "and 10 6 0 _ w e i g h t", "i'm 10 6 0 _ w e i g h t", "in 1 0 6 o _ w e i g h t", "and 1 0 6 o _ w e i g h t", "i'm 1 0 6 o _ w e i g h t", "in 1 o 6 0 _ w e i g h t", "and 1 o 6 0 _ w e i g h t", "i'm 1 o 6 0 _ w e i g h t", "in 1o6o_weight", "and 1o6o_weight", "i'm 1o6o_weight", "in 1 o 6 o _ w e i g h t", "and 1 o 6 o _ w e i g h t", "i'm 1 o 6 o _ w e i g h t", "in 1060_weight", "and 1060_weight", "i'm 1060_weight", "1 060_weight", "1060_weight", "in 1 0 6 0 _ w e i g h t", "and 1 0 6 0 _ w e i g h t", "i'm 1 0 6 0 _ w e i g h t", "in1060_weight", "and1060_weight", "i'm1060_weight", "M 1060 _ W E I G H T", "M 1o6 0 _ W E I G H T", "M 106 o _ W E I G H T", "M 106 0 _ W E I G H T", "M 1 0 6o _ W E I G H T", "M 1 o 60 _ W E I G H T", "M 10 60 _ W E I G H T", "M 1 0 60 _ W E I G H T", "M 10 6 o _ W E I G H T", "M 1o 6 0 _ W E I G H T", "M 1o 6 o _ W E I G H T", "M 10 6 0 _ W E I G H T", "M 1 0 6 o _ W E I G H T", "M 1 o 6 0 _ W E I G H T", "M 1o6o_WEIGHT", "M 1 o 6 o _ W E I G H T", "M 1060_WEIGHT", "1 060_WEIGHT", "1060_WEIGHT", "M 1 0 6 0 _ W E I G H T", "M1060_WEIGHT", "N 1060 _ W E I G H T", "N 1o6 0 _ W E I G H T", "N 106 o _ W E I G H T", "N 106 0 _ W E I G H T", "N 1 0 6o _ W E I G H T", "N 1 o 60 _ W E I G H T", "N 10 60 _ W E I G H T", "N 1 0 60 _ W E I G H T", "N 10 6 o _ W E I G H T", "N 1o 6 0 _ W E I G H T", "N 1o 6 o _ W E I G H T", "N 10 6 0 _ W E I G H T", "N 1 0 6 o _ W E I G H T", "N 1 o 6 0 _ W E I G H T", "N 1o6o_WEIGHT", "N 1 o 6 o _ W E I G H T", "N 1060_WEIGHT", "N1 060_WEIGHT", "N 1 0 6 0 _ W E I G H T", "N1060_WEIGHT"]}, {"code": "M1800", "variations": ["in 1 800", "and 1 800", "i'm 1 800", "in 180 o", "and 180 o", "i'm 180 o", "in 18o 0", "and 18o 0", "i'm 18o 0", "in 180 o ", "and 180 o ", "i'm 180 o ", "in 180 0", "and 180 0", "i'm 180 0", "in 18 0 o", "and 18 0 o", "i'm 18 0 o", "in 18 o 0", "and 18 o 0", "i'm 18 o 0", "in 18 o o", "and 18 o o", "i'm 18 o o", "in 18 0 0", "and 18 0 0", "i'm 18 0 0", "in 1 8 0 o", "and 1 8 0 o", "i'm 1 8 0 o", "in 1 8 o 0", "and 1 8 o 0", "i'm 1 8 o 0", "in 18oo", "and 18oo", "i'm 18oo", "in 1 8 o o", "and 1 8 o o", "i'm 1 8 o o", "in 1800", "and 1800", "i'm 1800", "in 1 8 0 0", "and 1 8 0 0", "i'm 1 8 0 0", "in1800", "and1800", "i'm1800", "M 1 800", "M 180 o", "M 18o 0", "M 180 o ", "M 180 0", "M 18 0 o", "M 18 o 0", "M 18 o o", "M 18 0 0", "M 1 8 0 o", "M 1 8 o 0", "M 18oo", "M 1 8 o o", "M 1800", "18 00", "1800", "M 1 8 0 0", "M1800", "N 1 800", "N 180 o", "N 18o 0", "N 180 o ", "N 180 0", "N 18 0 o", "N 18 o 0", "N 18 o o", "N 18 0 0", "N 1 8 0 o", "N 1 8 o 0", "N 18oo", "N 1 8 o o", "N 1800", "N18 00", "N 1 8 0 0", "N1800"]}, {"code": "M1820", "variations": ["in 1 820", "and 1 820", "i'm 1 820", "in 182 o", "and 182 o", "i'm 182 o", "in 182 o ", "and 182 o ", "i'm 182 o ", "in 182 0", "and 182 0", "i'm 182 0", "in 1 8 2o", "and 1 8 2o", "i'm 1 8 2o", "in 18 20", "and 18 20", "i'm 18 20", "in 1 8 20", "and 1 8 20", "i'm 1 8 20", "in 18 2 o", "and 18 2 o", "i'm 18 2 o", "in 18 2 0", "and 18 2 0", "i'm 18 2 0", "in 182o", "and 182o", "i'm 182o", "in 1 8 2 o", "and 1 8 2 o", "i'm 1 8 2 o", "in 1820", "and 1820", "i'm 1820", "in 1 8 2 0", "and 1 8 2 0", "i'm 1 8 2 0", "in1820", "and1820", "i'm1820", "M 1 820", "M 182 o", "M 182 o ", "M 182 0", "M 1 8 2o", "M 18 20", "M 1 8 20", "M 18 2 o", "M 18 2 0", "M 182o", "M 1 8 2 o", "M 1820", "18 20", "1820", "M 1 8 2 0", "M1820", "N 1 820", "N 182 o", "N 182 o ", "N 182 0", "N 1 8 2o", "N 18 20", "N 1 8 20", "N 18 2 o", "N 18 2 0", "N 182o", "N 1 8 2 o", "N 1820", "N18 20", "N 1 8 2 0", "N1820"]}, {"code": "M1830", "variations": ["in 1 830", "and 1 830", "i'm 1 830", "in 183 o", "and 183 o", "i'm 183 o", "in 183 o ", "and 183 o ", "i'm 183 o ", "in 183 0", "and 183 0", "i'm 183 0", "in 1 8 3o", "and 1 8 3o", "i'm 1 8 3o", "in 18 30", "and 18 30", "i'm 18 30", "in 1 8 30", "and 1 8 30", "i'm 1 8 30", "in 18 3 o", "and 18 3 o", "i'm 18 3 o", "in 18 3 0", "and 18 3 0", "i'm 18 3 0", "in 183o", "and 183o", "i'm 183o", "in 1 8 3 o", "and 1 8 3 o", "i'm 1 8 3 o", "in 1830", "and 1830", "i'm 1830", "in 1 8 3 0", "and 1 8 3 0", "i'm 1 8 3 0", "in1830", "and1830", "i'm1830", "M 1 830", "M 183 o", "M 183 o ", "M 183 0", "M 1 8 3o", "M 18 30", "M 1 8 30", "M 18 3 o", "M 18 3 0", "M <PERSON><PERSON>", "M 1 8 3 o", "M 1830", "18 30", "1830", "M 1 8 3 0", "M1830", "N 1 830", "N 183 o", "N 183 o ", "N 183 0", "N 1 8 3o", "N 18 30", "N 1 8 30", "N 18 3 o", "N 18 3 0", "N 183o", "N 1 8 3 o", "N 1830", "N18 30", "N 1 8 3 0", "N1830"]}, {"code": "M1840", "variations": ["in 1 840", "and 1 840", "i'm 1 840", "in 184 o", "and 184 o", "i'm 184 o", "in 184 o ", "and 184 o ", "i'm 184 o ", "in 184 0", "and 184 0", "i'm 184 0", "in 1 8 4o", "and 1 8 4o", "i'm 1 8 4o", "in 18 40", "and 18 40", "i'm 18 40", "in 1 8 40", "and 1 8 40", "i'm 1 8 40", "in 18 4 o", "and 18 4 o", "i'm 18 4 o", "in 18 4 0", "and 18 4 0", "i'm 18 4 0", "in 184o", "and 184o", "i'm 184o", "in 1 8 4 o", "and 1 8 4 o", "i'm 1 8 4 o", "in 1840", "and 1840", "i'm 1840", "in 1 8 4 0", "and 1 8 4 0", "i'm 1 8 4 0", "in1840", "and1840", "i'm1840", "M 1 840", "M 184 o", "M 184 o ", "M 184 0", "M 1 8 4o", "M 18 40", "M 1 8 40", "M 18 4 o", "M 18 4 0", "M 184o", "M 1 8 4 o", "M 1840", "18 40", "1840", "M 1 8 4 0", "M1840", "N 1 840", "N 184 o", "N 184 o ", "N 184 0", "N 1 8 4o", "N 18 40", "N 1 8 40", "N 18 4 o", "N 18 4 0", "N 184o", "N 1 8 4 o", "N 1840", "N18 40", "N 1 8 4 0", "N1840"]}, {"code": "M1845", "variations": ["in 1 845", "and 1 845", "i'm 1 845", "in 184 5", "and 184 5", "i'm 184 5", "in 18 45", "and 18 45", "i'm 18 45", "in 1 8 45", "and 1 8 45", "i'm 1 8 45", "in 18 4 5", "and 18 4 5", "i'm 18 4 5", "in 1845", "and 1845", "i'm 1845", "in 1 8 4 5", "and 1 8 4 5", "i'm 1 8 4 5", "in1845", "and1845", "i'm1845", "M 1 845", "M 184 5", "M 18 45", "M 1 8 45", "M 18 4 5", "M 1845", "18 45", "1845", "M 1 8 4 5", "M1845", "N 1 845", "N 184 5", "N 18 45", "N 1 8 45", "N 18 4 5", "N 1845", "N18 45", "N 1 8 4 5", "N1845"]}, {"code": "M1850", "variations": ["in 1 850", "and 1 850", "i'm 1 850", "in 185 o", "and 185 o", "i'm 185 o", "in 185 o ", "and 185 o ", "i'm 185 o ", "in 185 0", "and 185 0", "i'm 185 0", "in 1 8 5o", "and 1 8 5o", "i'm 1 8 5o", "in 18 50", "and 18 50", "i'm 18 50", "in 1 8 50", "and 1 8 50", "i'm 1 8 50", "in 18 5 o", "and 18 5 o", "i'm 18 5 o", "in 18 5 0", "and 18 5 0", "i'm 18 5 0", "in 185o", "and 185o", "i'm 185o", "in 1 8 5 o", "and 1 8 5 o", "i'm 1 8 5 o", "in 1850", "and 1850", "i'm 1850", "in 1 8 5 0", "and 1 8 5 0", "i'm 1 8 5 0", "in1850", "and1850", "i'm1850", "M 1 850", "M 185 o", "M 185 o ", "M 185 0", "M 1 8 5o", "M 18 50", "M 1 8 50", "M 18 5 o", "M 18 5 0", "M 185o", "M 1 8 5 o", "M 1850", "18 50", "1850", "M 1 8 5 0", "M1850", "N 1 850", "N 185 o", "N 185 o ", "N 185 0", "N 1 8 5o", "N 18 50", "N 1 8 50", "N 18 5 o", "N 18 5 0", "N 185o", "N 1 8 5 o", "N 1850", "N18 50", "N 1 8 5 0", "N1850"]}, {"code": "M1860", "variations": ["in 1 860", "and 1 860", "i'm 1 860", "in 186 o", "and 186 o", "i'm 186 o", "in 186 o ", "and 186 o ", "i'm 186 o ", "in 186 0", "and 186 0", "i'm 186 0", "in 1 8 6o", "and 1 8 6o", "i'm 1 8 6o", "in 18 60", "and 18 60", "i'm 18 60", "in 1 8 60", "and 1 8 60", "i'm 1 8 60", "in 18 6 o", "and 18 6 o", "i'm 18 6 o", "in 18 6 0", "and 18 6 0", "i'm 18 6 0", "in 186o", "and 186o", "i'm 186o", "in 1 8 6 o", "and 1 8 6 o", "i'm 1 8 6 o", "in 1860", "and 1860", "i'm 1860", "in 1 8 6 0", "and 1 8 6 0", "i'm 1 8 6 0", "in1860", "and1860", "i'm1860", "M 1 860", "M 186 o", "M 186 o ", "M 186 0", "M 1 8 6o", "M 18 60", "M 1 8 60", "M 18 6 o", "M 18 6 0", "M 186o", "M 1 8 6 o", "M 1860", "18 60", "1860", "M 1 8 6 0", "M1860", "N 1 860", "N 186 o", "N 186 o ", "N 186 0", "N 1 8 6o", "N 18 60", "N 1 8 60", "N 18 6 o", "N 18 6 0", "N 186o", "N 1 8 6 o", "N 1860", "N18 60", "N 1 8 6 0", "N1860"]}, {"code": "M1870", "variations": ["in 1 870", "and 1 870", "i'm 1 870", "in 187 o", "and 187 o", "i'm 187 o", "in 187 o ", "and 187 o ", "i'm 187 o ", "in 187 0", "and 187 0", "i'm 187 0", "in 1 8 7o", "and 1 8 7o", "i'm 1 8 7o", "in 18 70", "and 18 70", "i'm 18 70", "in 1 8 70", "and 1 8 70", "i'm 1 8 70", "in 18 7 o", "and 18 7 o", "i'm 18 7 o", "in 18 7 0", "and 18 7 0", "i'm 18 7 0", "in 187o", "and 187o", "i'm 187o", "in 1 8 7 o", "and 1 8 7 o", "i'm 1 8 7 o", "in 1870", "and 1870", "i'm 1870", "in 1 8 7 0", "and 1 8 7 0", "i'm 1 8 7 0", "in1870", "and1870", "i'm1870", "M 1 870", "M 187 o", "M 187 o ", "M 187 0", "M 1 8 7o", "M 18 70", "M 1 8 70", "M 18 7 o", "M 18 7 0", "M 187o", "M 1 8 7 o", "M 1870", "18 70", "1870", "M 1 8 7 0", "M1870", "N 1 870", "N 187 o", "N 187 o ", "N 187 0", "N 1 8 7o", "N 18 70", "N 1 8 70", "N 18 7 o", "N 18 7 0", "N 187o", "N 1 8 7 o", "N 1870", "N18 70", "N 1 8 7 0", "N1870"]}, {"code": "GG0130A1", "variations": ["G G 0130 A 1", "G G 0 13o A 1", "G G o 130 A 1", "G G 0 130 A 1", "G G 0 13 o A 1", "G G o 13 0 A 1", "G G o 13 o A 1", "G G 0 13 0 A 1", "GG 013o A 1", "GG o130 A 1", "G G 0 1 3 o A 1", "G G o 1 3 0 A 1", "GG o13o A 1", "G G o 1 3 o A 1", "GG 0130 A 1", "GG013 0A1", "G G 0 1 3 0 A 1", "GG0130A1"]}, {"code": "GG0130A2", "variations": ["G G 0130 A 2", "G G 0 13o A 2", "G G o 130 A 2", "G G 0 130 A 2", "G G 0 13 o A 2", "G G o 13 0 A 2", "G G o 13 o A 2", "G G 0 13 0 A 2", "GG 013o A 2", "GG o130 A 2", "G G 0 1 3 o A 2", "G G o 1 3 0 A 2", "GG o13o A 2", "G G o 1 3 o A 2", "GG 0130 A 2", "GG013 0A2", "G G 0 1 3 0 A 2", "GG0130A2"]}, {"code": "GG0130B1", "variations": ["G G 0130 B 1", "G G 0 13o B 1", "G G o 130 B 1", "G G 0 130 B 1", "G G 0 13 o B 1", "G G o 13 0 B 1", "G G o 13 o B 1", "G G 0 13 0 B 1", "GG 013o B 1", "GG o130 B 1", "G G 0 1 3 o B 1", "G G o 1 3 0 B 1", "GG o13o B 1", "G G o 1 3 o B 1", "GG 0130 B 1", "GG013 0B1", "G G 0 1 3 0 B 1", "GG0130B1"]}, {"code": "GG0130B2", "variations": ["G G 0130 B 2", "G G 0 13o B 2", "G G o 130 B 2", "G G 0 130 B 2", "G G 0 13 o B 2", "G G o 13 0 B 2", "G G o 13 o B 2", "G G 0 13 0 B 2", "GG 013o B 2", "GG o130 B 2", "G G 0 1 3 o B 2", "G G o 1 3 0 B 2", "GG o13o B 2", "G G o 1 3 o B 2", "GG 0130 B 2", "GG013 0B2", "G G 0 1 3 0 B 2", "GG0130B2"]}, {"code": "GG0130C1", "variations": ["G G 0130 C 1", "G G 0 13o C 1", "G G o 130 C 1", "G G 0 130 C 1", "G G 0 13 o C 1", "G G o 13 0 C 1", "G G o 13 o C 1", "G G 0 13 0 C 1", "GG 013o C 1", "GG o130 C 1", "G G 0 1 3 o C 1", "G G o 1 3 0 C 1", "GG o13o C 1", "G G o 1 3 o C 1", "GG 0130 C 1", "GG013 0C1", "G G 0 1 3 0 C 1", "GG0130C1"]}, {"code": "GG0130C2", "variations": ["G G 0130 C 2", "G G 0 13o C 2", "G G o 130 C 2", "G G 0 130 C 2", "G G 0 13 o C 2", "G G o 13 0 C 2", "G G o 13 o C 2", "G G 0 13 0 C 2", "GG 013o C 2", "GG o130 C 2", "G G 0 1 3 o C 2", "G G o 1 3 0 C 2", "GG o13o C 2", "G G o 1 3 o C 2", "GG 0130 C 2", "GG013 0C2", "G G 0 1 3 0 C 2", "GG0130C2"]}, {"code": "GG0130E1", "variations": ["G G 0130 E 1", "G G 0 13o E 1", "G G o 130 E 1", "G G 0 130 E 1", "G G 0 13 o E 1", "G G o 13 0 E 1", "G G o 13 o E 1", "G G 0 13 0 E 1", "GG 013o E 1", "GG o130 E 1", "G G 0 1 3 o E 1", "G G o 1 3 0 E 1", "GG o13o E 1", "G G o 1 3 o E 1", "GG 0130 E 1", "GG013 0E1", "G G 0 1 3 0 E 1", "GG0130E1"]}, {"code": "GG0130E2", "variations": ["G G 0130 E 2", "G G 0 13o E 2", "G G o 130 E 2", "G G 0 130 E 2", "G G 0 13 o E 2", "G G o 13 0 E 2", "G G o 13 o E 2", "G G 0 13 0 E 2", "GG 013o E 2", "GG o130 E 2", "G G 0 1 3 o E 2", "G G o 1 3 0 E 2", "GG o13o E 2", "G G o 1 3 o E 2", "GG 0130 E 2", "GG013 0E2", "G G 0 1 3 0 E 2", "GG0130E2"]}, {"code": "GG0130F1", "variations": ["G G 0130 F 1", "G G 0 13o F 1", "G G o 130 F 1", "G G 0 130 F 1", "G G 0 13 o F 1", "G G o 13 0 F 1", "G G o 13 o F 1", "G G 0 13 0 F 1", "GG 013o F 1", "GG o130 F 1", "G G 0 1 3 o F 1", "G G o 1 3 0 F 1", "GG o13o F 1", "G G o 1 3 o F 1", "GG 0130 F 1", "GG013 0F1", "G G 0 1 3 0 F 1", "GG0130F1"]}, {"code": "GG0130F2", "variations": ["G G 0130 F 2", "G G 0 13o F 2", "G G o 130 F 2", "G G 0 130 F 2", "G G 0 13 o F 2", "G G o 13 0 F 2", "G G o 13 o F 2", "G G 0 13 0 F 2", "GG 013o F 2", "GG o130 F 2", "G G 0 1 3 o F 2", "G G o 1 3 0 F 2", "GG o13o F 2", "G G o 1 3 o F 2", "GG 0130 F 2", "GG013 0F2", "G G 0 1 3 0 F 2", "GG0130F2"]}, {"code": "GG0130G1", "variations": ["G G 0130 G 1", "G G 0 13o G 1", "G G o 130 G 1", "G G 0 130 G 1", "G G 0 13 o G 1", "G G o 13 0 G 1", "G G o 13 o G 1", "G G 0 13 0 G 1", "GG 013o G 1", "GG o130 G 1", "G G 0 1 3 o G 1", "G G o 1 3 0 G 1", "GG o13o G 1", "G G o 1 3 o G 1", "GG 0130 G 1", "GG013 0G1", "G G 0 1 3 0 G 1", "GG0130G1"]}, {"code": "GG0130G2", "variations": ["G G 0130 G 2", "G G 0 13o G 2", "G G o 130 G 2", "G G 0 130 G 2", "G G 0 13 o G 2", "G G o 13 0 G 2", "G G o 13 o G 2", "G G 0 13 0 G 2", "GG 013o G 2", "GG o130 G 2", "G G 0 1 3 o G 2", "G G o 1 3 0 G 2", "GG o13o G 2", "G G o 1 3 o G 2", "GG 0130 G 2", "GG013 0G2", "G G 0 1 3 0 G 2", "GG0130G2"]}, {"code": "GG0130H1", "variations": ["G G 0130 H 1", "G G 0 13o H 1", "G G o 130 H 1", "G G 0 130 H 1", "G G 0 13 o H 1", "G G o 13 0 H 1", "G G o 13 o H 1", "G G 0 13 0 H 1", "GG 013o H 1", "GG o130 H 1", "G G 0 1 3 o H 1", "G G o 1 3 0 H 1", "GG o13o H 1", "G G o 1 3 o H 1", "GG 0130 H 1", "GG013 0H1", "G G 0 1 3 0 H 1", "GG0130H1"]}, {"code": "GG0130H2", "variations": ["G G 0130 H 2", "G G 0 13o H 2", "G G o 130 H 2", "G G 0 130 H 2", "G G 0 13 o H 2", "G G o 13 0 H 2", "G G o 13 o H 2", "G G 0 13 0 H 2", "GG 013o H 2", "GG o130 H 2", "G G 0 1 3 o H 2", "G G o 1 3 0 H 2", "GG o13o H 2", "G G o 1 3 o H 2", "GG 0130 H 2", "GG013 0H2", "G G 0 1 3 0 H 2", "GG0130H2"]}, {"code": "GG0170A1", "variations": ["G G 0170 A 1", "G G 0 17o A 1", "G G o 170 A 1", "G G 0 170 A 1", "G G 0 17 o A 1", "G G o 17 0 A 1", "G G o 17 o A 1", "G G 0 17 0 A 1", "GG 017o A 1", "GG o170 A 1", "G G 0 1 7 o A 1", "G G o 1 7 0 A 1", "GG o17o A 1", "G G o 1 7 o A 1", "GG 0170 A 1", "GG017 0A1", "G G 0 1 7 0 A 1", "GG0170A1"]}, {"code": "GG0170A2", "variations": ["G G 0170 A 2", "G G 0 17o A 2", "G G o 170 A 2", "G G 0 170 A 2", "G G 0 17 o A 2", "G G o 17 0 A 2", "G G o 17 o A 2", "G G 0 17 0 A 2", "GG 017o A 2", "GG o170 A 2", "G G 0 1 7 o A 2", "G G o 1 7 0 A 2", "GG o17o A 2", "G G o 1 7 o A 2", "GG 0170 A 2", "GG017 0A2", "G G 0 1 7 0 A 2", "GG0170A2"]}, {"code": "GG0170B1", "variations": ["G G 0170 B 1", "G G 0 17o B 1", "G G o 170 B 1", "G G 0 170 B 1", "G G 0 17 o B 1", "G G o 17 0 B 1", "G G o 17 o B 1", "G G 0 17 0 B 1", "GG 017o B 1", "GG o170 B 1", "G G 0 1 7 o B 1", "G G o 1 7 0 B 1", "GG o17o B 1", "G G o 1 7 o B 1", "GG 0170 B 1", "GG017 0B1", "G G 0 1 7 0 B 1", "GG0170B1"]}, {"code": "GG0170B2", "variations": ["G G 0170 B 2", "G G 0 17o B 2", "G G o 170 B 2", "G G 0 170 B 2", "G G 0 17 o B 2", "G G o 17 0 B 2", "G G o 17 o B 2", "G G 0 17 0 B 2", "GG 017o B 2", "GG o170 B 2", "G G 0 1 7 o B 2", "G G o 1 7 0 B 2", "GG o17o B 2", "G G o 1 7 o B 2", "GG 0170 B 2", "GG017 0B2", "G G 0 1 7 0 B 2", "GG0170B2"]}, {"code": "GG0170C1", "variations": ["G G 0170 C 1", "G G 0 17o C 1", "G G o 170 C 1", "G G 0 170 C 1", "G G 0 17 o C 1", "G G o 17 0 C 1", "G G o 17 o C 1", "G G 0 17 0 C 1", "GG 017o C 1", "GG o170 C 1", "G G 0 1 7 o C 1", "G G o 1 7 0 C 1", "GG o17o C 1", "G G o 1 7 o C 1", "GG 0170 C 1", "GG017 0C1", "G G 0 1 7 0 C 1", "GG0170C1"]}, {"code": "GG0170C2", "variations": ["G G 0170 C 2", "G G 0 17o C 2", "G G o 170 C 2", "G G 0 170 C 2", "G G 0 17 o C 2", "G G o 17 0 C 2", "G G o 17 o C 2", "G G 0 17 0 C 2", "GG 017o C 2", "GG o170 C 2", "G G 0 1 7 o C 2", "G G o 1 7 0 C 2", "GG o17o C 2", "G G o 1 7 o C 2", "GG 0170 C 2", "GG017 0C2", "G G 0 1 7 0 C 2", "GG0170C2"]}, {"code": "GG0170D1", "variations": ["G G 0170 D 1", "G G 0 17o D 1", "G G o 170 D 1", "G G 0 170 D 1", "G G 0 17 o D 1", "G G o 17 0 D 1", "G G o 17 o D 1", "G G 0 17 0 D 1", "GG 017o D 1", "GG o170 D 1", "G G 0 1 7 o D 1", "G G o 1 7 0 D 1", "GG o17o D 1", "G G o 1 7 o D 1", "GG 0170 D 1", "GG017 0D1", "G G 0 1 7 0 D 1", "GG0170D1"]}, {"code": "GG0170D2", "variations": ["G G 0170 D 2", "G G 0 17o D 2", "G G o 170 D 2", "G G 0 170 D 2", "G G 0 17 o D 2", "G G o 17 0 D 2", "G G o 17 o D 2", "G G 0 17 0 D 2", "GG 017o D 2", "GG o170 D 2", "G G 0 1 7 o D 2", "G G o 1 7 0 D 2", "GG o17o D 2", "G G o 1 7 o D 2", "GG 0170 D 2", "GG017 0D2", "G G 0 1 7 0 D 2", "GG0170D2"]}, {"code": "GG0170E1", "variations": ["G G 0170 E 1", "G G 0 17o E 1", "G G o 170 E 1", "G G 0 170 E 1", "G G 0 17 o E 1", "G G o 17 0 E 1", "G G o 17 o E 1", "G G 0 17 0 E 1", "GG 017o E 1", "GG o170 E 1", "G G 0 1 7 o E 1", "G G o 1 7 0 E 1", "GG o17o E 1", "G G o 1 7 o E 1", "GG 0170 E 1", "GG017 0E1", "G G 0 1 7 0 E 1", "GG0170E1"]}, {"code": "GG0170E2", "variations": ["G G 0170 E 2", "G G 0 17o E 2", "G G o 170 E 2", "G G 0 170 E 2", "G G 0 17 o E 2", "G G o 17 0 E 2", "G G o 17 o E 2", "G G 0 17 0 E 2", "GG 017o E 2", "GG o170 E 2", "G G 0 1 7 o E 2", "G G o 1 7 0 E 2", "GG o17o E 2", "G G o 1 7 o E 2", "GG 0170 E 2", "GG017 0E2", "G G 0 1 7 0 E 2", "GG0170E2"]}, {"code": "GG0170F1", "variations": ["G G 0170 F 1", "G G 0 17o F 1", "G G o 170 F 1", "G G 0 170 F 1", "G G 0 17 o F 1", "G G o 17 0 F 1", "G G o 17 o F 1", "G G 0 17 0 F 1", "GG 017o F 1", "GG o170 F 1", "G G 0 1 7 o F 1", "G G o 1 7 0 F 1", "GG o17o F 1", "G G o 1 7 o F 1", "GG 0170 F 1", "GG017 0F1", "G G 0 1 7 0 F 1", "GG0170F1"]}, {"code": "GG0170F2", "variations": ["G G 0170 F 2", "G G 0 17o F 2", "G G o 170 F 2", "G G 0 170 F 2", "G G 0 17 o F 2", "G G o 17 0 F 2", "G G o 17 o F 2", "G G 0 17 0 F 2", "GG 017o F 2", "GG o170 F 2", "G G 0 1 7 o F 2", "G G o 1 7 0 F 2", "GG o17o F 2", "G G o 1 7 o F 2", "GG 0170 F 2", "GG017 0F2", "G G 0 1 7 0 F 2", "GG0170F2"]}, {"code": "GG0170G1", "variations": ["G G 0170 G 1", "G G 0 17o G 1", "G G o 170 G 1", "G G 0 170 G 1", "G G 0 17 o G 1", "G G o 17 0 G 1", "G G o 17 o G 1", "G G 0 17 0 G 1", "GG 017o G 1", "GG o170 G 1", "G G 0 1 7 o G 1", "G G o 1 7 0 G 1", "GG o17o G 1", "G G o 1 7 o G 1", "GG 0170 G 1", "GG017 0G1", "G G 0 1 7 0 G 1", "GG0170G1"]}, {"code": "GG0170G2", "variations": ["G G 0170 G 2", "G G 0 17o G 2", "G G o 170 G 2", "G G 0 170 G 2", "G G 0 17 o G 2", "G G o 17 0 G 2", "G G o 17 o G 2", "G G 0 17 0 G 2", "GG 017o G 2", "GG o170 G 2", "G G 0 1 7 o G 2", "G G o 1 7 0 G 2", "GG o17o G 2", "G G o 1 7 o G 2", "GG 0170 G 2", "GG017 0G2", "G G 0 1 7 0 G 2", "GG0170G2"]}, {"code": "GG0170I1", "variations": ["G G 0170 I 1", "G G 0 17o I 1", "G G o 170 I 1", "G G 0 170 I 1", "G G 0 17 o I 1", "G G o 17 0 I 1", "G G o 17 o I 1", "G G 0 17 0 I 1", "GG 017o I 1", "GG o170 I 1", "G G 0 1 7 o I 1", "G G o 1 7 0 I 1", "GG o17o I 1", "G G o 1 7 o I 1", "GG 0170 I 1", "GG017 0I1", "G G 0 1 7 0 I 1", "GG0170I1"]}, {"code": "GG0170I2", "variations": ["G G 0170 I 2", "G G 0 17o I 2", "G G o 170 I 2", "G G 0 170 I 2", "G G 0 17 o I 2", "G G o 17 0 I 2", "G G o 17 o I 2", "G G 0 17 0 I 2", "GG 017o I 2", "GG o170 I 2", "G G 0 1 7 o I 2", "G G o 1 7 0 I 2", "GG o17o I 2", "G G o 1 7 o I 2", "GG 0170 I 2", "GG017 0I2", "G G 0 1 7 0 I 2", "GG0170I2"]}, {"code": "GG0170J1", "variations": ["G G 0170 J 1", "G G 0 17o J 1", "G G o 170 J 1", "G G 0 170 J 1", "G G 0 17 o J 1", "G G o 17 0 J 1", "G G o 17 o J 1", "G G 0 17 0 J 1", "GG 017o J 1", "GG o170 J 1", "G G 0 1 7 o J 1", "G G o 1 7 0 J 1", "GG o17o J 1", "G G o 1 7 o J 1", "GG 0170 J 1", "GG017 0J1", "G G 0 1 7 0 J 1", "GG0170J1"]}, {"code": "GG0170J2", "variations": ["G G 0170 J 2", "G G 0 17o J 2", "G G o 170 J 2", "G G 0 170 J 2", "G G 0 17 o J 2", "G G o 17 0 J 2", "G G o 17 o J 2", "G G 0 17 0 J 2", "GG 017o J 2", "GG o170 J 2", "G G 0 1 7 o J 2", "G G o 1 7 0 J 2", "GG o17o J 2", "G G o 1 7 o J 2", "GG 0170 J 2", "GG017 0J2", "G G 0 1 7 0 J 2", "GG0170J2"]}, {"code": "GG0170K1", "variations": ["G G 0170 K 1", "G G 0 17o K 1", "G G o 170 K 1", "G G 0 170 K 1", "G G 0 17 o K 1", "G G o 17 0 K 1", "G G o 17 o K 1", "G G 0 17 0 K 1", "GG 017o K 1", "GG o170 K 1", "G G 0 1 7 o K 1", "G G o 1 7 0 K 1", "GG o17o K 1", "G G o 1 7 o K 1", "GG 0170 K 1", "GG017 0K1", "G G 0 1 7 0 K 1", "GG0170K1"]}, {"code": "GG0170K2", "variations": ["G G 0170 K 2", "G G 0 17o K 2", "G G o 170 K 2", "G G 0 170 K 2", "G G 0 17 o K 2", "G G o 17 0 K 2", "G G o 17 o K 2", "G G 0 17 0 K 2", "GG 017o K 2", "GG o170 K 2", "G G 0 1 7 o K 2", "G G o 1 7 0 K 2", "GG o17o K 2", "G G o 1 7 o K 2", "GG 0170 K 2", "GG017 0K2", "G G 0 1 7 0 K 2", "GG0170K2"]}, {"code": "GG0170L1", "variations": ["G G 0170 L 1", "G G 0 17o L 1", "G G o 170 L 1", "G G 0 170 L 1", "G G 0 17 o L 1", "G G o 17 0 L 1", "G G o 17 o L 1", "G G 0 17 0 L 1", "GG 017o L 1", "GG o170 L 1", "G G 0 1 7 o L 1", "G G o 1 7 0 L 1", "GG o17o L 1", "G G o 1 7 o L 1", "GG 0170 L 1", "GG017 0L1", "G G 0 1 7 0 L 1", "GG0170L1"]}, {"code": "GG0170L2", "variations": ["G G 0170 L 2", "G G 0 17o L 2", "G G o 170 L 2", "G G 0 170 L 2", "G G 0 17 o L 2", "G G o 17 0 L 2", "G G o 17 o L 2", "G G 0 17 0 L 2", "GG 017o L 2", "GG o170 L 2", "G G 0 1 7 o L 2", "G G o 1 7 0 L 2", "GG o17o L 2", "G G o 1 7 o L 2", "GG 0170 L 2", "GG017 0L2", "G G 0 1 7 0 L 2", "GG0170L2"]}, {"code": "GG0170M1", "variations": ["G G 0170 M 1", "G G 0 17o M 1", "G G o 170 M 1", "G G 0 170 M 1", "G G 0 17 o M 1", "G G o 17 0 M 1", "G G o 17 o M 1", "G G 0 17 0 M 1", "GG 017o M 1", "GG o170 M 1", "G G 0 1 7 o M 1", "G G o 1 7 0 M 1", "GG o17o M 1", "G G o 1 7 o M 1", "GG 0170 M 1", "GG017 0M1", "G G 0 1 7 0 M 1", "GG0170M1"]}, {"code": "GG0170M2", "variations": ["G G 0170 M 2", "G G 0 17o M 2", "G G o 170 M 2", "G G 0 170 M 2", "G G 0 17 o M 2", "G G o 17 0 M 2", "G G o 17 o M 2", "G G 0 17 0 M 2", "GG 017o M 2", "GG o170 M 2", "G G 0 1 7 o M 2", "G G o 1 7 0 M 2", "GG o17o M 2", "G G o 1 7 o M 2", "GG 0170 M 2", "GG017 0M2", "G G 0 1 7 0 M 2", "GG0170M2"]}, {"code": "GG0170N1", "variations": ["G G 0170 N 1", "G G 0 17o N 1", "G G o 170 N 1", "G G 0 170 N 1", "G G 0 17 o N 1", "G G o 17 0 N 1", "G G o 17 o N 1", "G G 0 17 0 N 1", "GG 017o N 1", "GG o170 N 1", "G G 0 1 7 o N 1", "G G o 1 7 0 N 1", "GG o17o N 1", "G G o 1 7 o N 1", "GG 0170 N 1", "GG017 0N1", "G G 0 1 7 0 N 1", "GG0170N1"]}, {"code": "GG0170N2", "variations": ["G G 0170 N 2", "G G 0 17o N 2", "G G o 170 N 2", "G G 0 170 N 2", "G G 0 17 o N 2", "G G o 17 0 N 2", "G G o 17 o N 2", "G G 0 17 0 N 2", "GG 017o N 2", "GG o170 N 2", "G G 0 1 7 o N 2", "G G o 1 7 0 N 2", "GG o17o N 2", "G G o 1 7 o N 2", "GG 0170 N 2", "GG017 0N2", "G G 0 1 7 0 N 2", "GG0170N2"]}, {"code": "GG0170O1", "variations": ["G G 0170 O 1", "G G 0 17o O 1", "G G o 170 O 1", "G G 0 170 O 1", "G G 0 17 o O 1", "G G o 17 0 O 1", "G G o 17 o O 1", "G G 0 17 0 O 1", "GG 017o O 1", "GG o170 O 1", "G G 0 1 7 o O 1", "G G o 1 7 0 O 1", "GG o17o O 1", "G G o 1 7 o O 1", "GG 0170 O 1", "GG017 0O1", "G G 0 1 7 0 O 1", "GG0170O1"]}, {"code": "GG0170O2", "variations": ["G G 0170 O 2", "G G 0 17o O 2", "G G o 170 O 2", "G G 0 170 O 2", "G G 0 17 o O 2", "G G o 17 0 O 2", "G G o 17 o O 2", "G G 0 17 0 O 2", "GG 017o O 2", "GG o170 O 2", "G G 0 1 7 o O 2", "G G o 1 7 0 O 2", "GG o17o O 2", "G G o 1 7 o O 2", "GG 0170 O 2", "GG017 0O2", "G G 0 1 7 0 O 2", "GG0170O2"]}, {"code": "GG0170P1", "variations": ["G G 0170 P 1", "G G 0 17o P 1", "G G o 170 P 1", "G G 0 170 P 1", "G G 0 17 o P 1", "G G o 17 0 P 1", "G G o 17 o P 1", "G G 0 17 0 P 1", "GG 017o P 1", "GG o170 P 1", "G G 0 1 7 o P 1", "G G o 1 7 0 P 1", "GG o17o P 1", "G G o 1 7 o P 1", "GG 0170 P 1", "GG017 0P1", "G G 0 1 7 0 P 1", "GG0170P1"]}, {"code": "GG0170P2", "variations": ["G G 0170 P 2", "G G 0 17o P 2", "G G o 170 P 2", "G G 0 170 P 2", "G G 0 17 o P 2", "G G o 17 0 P 2", "G G o 17 o P 2", "G G 0 17 0 P 2", "GG 017o P 2", "GG o170 P 2", "G G 0 1 7 o P 2", "G G o 1 7 0 P 2", "GG o17o P 2", "G G o 1 7 o P 2", "GG 0170 P 2", "GG017 0P2", "G G 0 1 7 0 P 2", "GG0170P2"]}, {"code": "GG0170Q", "variations": ["G G 0170 Q", "G G 0 17o Q", "G G o 170 Q", "G G 0 170 Q", "G G 0 17 o Q", "G G o 17 0 Q", "G G o 17 o Q", "G G 0 17 0 Q", "GG 017o Q", "GG o170 Q", "G G 0 1 7 o Q", "G G o 1 7 0 Q", "GG o17o Q", "G G o 1 7 o Q", "GG 0170 Q", "GG017 0Q", "G G 0 1 7 0 Q", "GG0170Q"]}, {"code": "GG0170R1", "variations": ["G G 0170 R 1", "G G 0 17o L 1", "G G o 170 L 1", "G G 0 170 L 1", "G G 0 17 o L 1", "G G o 17 0 L 1", "G G o 17 o L 1", "G G 0 17 0 L 1", "G   G   0   1   7   o   L   1", "G   G   o   1   7   0   L   1", "G G o 1 7 o L 1", "G G 0 1 7 0 L 1", "G G 0 17o R 1", "G G o 170 R 1", "G G 0 170 R 1", "G G 0 17 o R 1", "G G o 17 0 R 1", "G G o 17 o R 1", "G G 0 17 0 R 1", "GG 017o R 1", "GG o170 R 1", "G G 0 1 7 o R 1", "G G o 1 7 0 R 1", "GG o17o R 1", "G G o 1 7 o R 1", "GG 0170 R 1", "GG017 0R1", "G G 0 1 7 0 R 1", "GG0170R1"]}, {"code": "GG0170R2", "variations": ["G G 0170 R 2", "G G 0 17o L 2", "G G o 170 L 2", "G G 0 170 L 2", "G G 0 17 o L 2", "G G o 17 0 L 2", "G G o 17 o L 2", "G G 0 17 0 L 2", "G   G   0   1   7   o   L   2", "G   G   o   1   7   0   L   2", "G G o 1 7 o L 2", "G G 0 1 7 0 L 2", "G G 0 17o R 2", "G G o 170 R 2", "G G 0 170 R 2", "G G 0 17 o R 2", "G G o 17 0 R 2", "G G o 17 o R 2", "G G 0 17 0 R 2", "GG 017o R 2", "GG o170 R 2", "G G 0 1 7 o R 2", "G G o 1 7 0 R 2", "GG o17o R 2", "G G o 1 7 o R 2", "GG 0170 R 2", "GG017 0R2", "G G 0 1 7 0 R 2", "GG0170R2"]}, {"code": "GG0170RR1", "variations": ["G G 0170 R R 1", "G G 0 17o L L 1", "G G o 170 L L 1", "G G 0 170 L L 1", "G G 0 17 o L L 1", "G G o 17 0 L L 1", "G G o 17 o L L 1", "G G 0 17 0 L L 1", "G   G   0   1   7   o   L   L   1", "G   G   o   1   7   0   L   L   1", "G G o 1 7 o L L 1", "G G 0 1 7 0 L L 1", "G G 0 17o R R 1", "G G o 170 R R 1", "G G 0 170 R R 1", "G G 0 17 o R R 1", "G G o 17 0 R R 1", "G G o 17 o R R 1", "G G 0 17 0 R R 1", "GG 017o RR 1", "GG o170 RR 1", "G G 0 1 7 o R R 1", "G G o 1 7 0 R R 1", "GG o17o RR 1", "G G o 1 7 o R R 1", "GG 0170 RR 1", "GG017 0RR1", "G G 0 1 7 0 R R 1", "GG0170RR1"]}, {"code": "GG0170S1", "variations": ["G G 0170 S 1", "G G 0 17o S 1", "G G o 170 S 1", "G G 0 170 S 1", "G G 0 17 o S 1", "G G o 17 0 S 1", "G G o 17 o S 1", "G G 0 17 0 S 1", "GG 017o S 1", "GG o170 S 1", "G G 0 1 7 o S 1", "G G o 1 7 0 S 1", "GG o17o S 1", "G G o 1 7 o S 1", "GG 0170 S 1", "GG017 0S1", "G G 0 1 7 0 S 1", "GG0170S1"]}, {"code": "GG0170S2", "variations": ["G G 0170 S 2", "G G 0 17o S 2", "G G o 170 S 2", "G G 0 170 S 2", "G G 0 17 o S 2", "G G o 17 0 S 2", "G G o 17 o S 2", "G G 0 17 0 S 2", "GG 017o S 2", "GG o170 S 2", "G G 0 1 7 o S 2", "G G o 1 7 0 S 2", "GG o17o S 2", "G G o 1 7 o S 2", "GG 0170 S 2", "GG017 0S2", "G G 0 1 7 0 S 2", "GG0170S2"]}, {"code": "GG0170SS1", "variations": ["G G 0170 S S 1", "G G 0 17o S S 1", "G G o 170 S S 1", "G G 0 170 S S 1", "G G 0 17 o S S 1", "G G o 17 0 S S 1", "G G o 17 o S S 1", "G G 0 17 0 S S 1", "GG 017o SS 1", "GG o170 SS 1", "G G 0 1 7 o S S 1", "G G o 1 7 0 S S 1", "GG o17o SS 1", "G G o 1 7 o S S 1", "GG 0170 SS 1", "GG017 0SS1", "G G 0 1 7 0 S S 1", "GG0170SS1"]}, {"code": "M1306", "variations": ["in 1 306", "and 1 306", "i'm 1 306", "in 130 6", "and 130 6", "i'm 130 6", "in 13 o 6", "and 13 o 6", "i'm 13 o 6", "in 13 0 6", "and 13 0 6", "i'm 13 0 6", "in 13o6", "and 13o6", "i'm 13o6", "in 1 3 o 6", "and 1 3 o 6", "i'm 1 3 o 6", "in 1306", "and 1306", "i'm 1306", "in 1 3 0 6", "and 1 3 0 6", "i'm 1 3 0 6", "in1306", "and1306", "i'm1306", "M 1 306", "M 130 6", "M 13 o 6", "M 13 0 6", "M 13o6", "M 1 3 o 6", "M 1306", "13 06", "1306", "M 1 3 0 6", "M1306", "N 1 306", "N 130 6", "N 13 o 6", "N 13 0 6", "N 13o6", "N 1 3 o 6", "N 1306", "N13 06", "N 1 3 0 6", "N1306"]}, {"code": "M1311A", "variations": ["in 131 1 a", "and 131 1 a", "i'm 131 1 a", "in 13 11 a", "and 13 11 a", "i'm 13 11 a", "in 1 3 11 a", "and 1 3 11 a", "i'm 1 3 11 a", "in 13 1 1 a", "and 13 1 1 a", "i'm 13 1 1 a", "in 1311 a", "and 1311 a", "i'm 1311 a", "13 11a", "1311a", "in 1 3 1 1 a", "and 1 3 1 1 a", "i'm 1 3 1 1 a", "in1311a", "and1311a", "i'm1311a", "M 131 1 A", "M 13 11 A", "M 1 3 11 A", "M 13 1 1 A", "M 1311 A", "13 11A", "1311A", "M 1 3 1 1 A", "M1311A", "N 131 1 A", "N 13 11 A", "N 1 3 11 A", "N 13 1 1 A", "N 1311 A", "N13 11A", "N 1 3 1 1 A", "N1311A"]}, {"code": "M1311B", "variations": ["in 131 1 b", "and 131 1 b", "i'm 131 1 b", "in 13 11 b", "and 13 11 b", "i'm 13 11 b", "in 1 3 11 b", "and 1 3 11 b", "i'm 1 3 11 b", "in 13 1 1 b", "and 13 1 1 b", "i'm 13 1 1 b", "in 1311 b", "and 1311 b", "i'm 1311 b", "13 11b", "1311b", "in 1 3 1 1 b", "and 1 3 1 1 b", "i'm 1 3 1 1 b", "in1311b", "and1311b", "i'm1311b", "M 131 1 B", "M 13 11 B", "M 1 3 11 B", "M 13 1 1 B", "M 1311 B", "13 11B", "1311B", "M 1 3 1 1 B", "M1311B", "N 131 1 B", "N 13 11 B", "N 1 3 11 B", "N 13 1 1 B", "N 1311 B", "N13 11B", "N 1 3 1 1 B", "N1311B"]}, {"code": "M1311C", "variations": ["in 131 1 c", "and 131 1 c", "i'm 131 1 c", "in 13 11 c", "and 13 11 c", "i'm 13 11 c", "in 1 3 11 c", "and 1 3 11 c", "i'm 1 3 11 c", "in 13 1 1 c", "and 13 1 1 c", "i'm 13 1 1 c", "in 1311 c", "and 1311 c", "i'm 1311 c", "13 11c", "1311c", "in 1 3 1 1 c", "and 1 3 1 1 c", "i'm 1 3 1 1 c", "in1311c", "and1311c", "i'm1311c", "M 131 1 C", "M 13 11 C", "M 1 3 11 C", "M 13 1 1 C", "M 1311 C", "13 11C", "1311C", "M 1 3 1 1 C", "M1311C", "N 131 1 C", "N 13 11 C", "N 1 3 11 C", "N 13 1 1 C", "N 1311 C", "N13 11C", "N 1 3 1 1 C", "N1311C"]}, {"code": "M1311D", "variations": ["in 131 1 d", "and 131 1 d", "i'm 131 1 d", "in 13 11 d", "and 13 11 d", "i'm 13 11 d", "in 1 3 11 d", "and 1 3 11 d", "i'm 1 3 11 d", "in 13 1 1 d", "and 13 1 1 d", "i'm 13 1 1 d", "in 1311 d", "and 1311 d", "i'm 1311 d", "13 11d", "1311d", "in 1 3 1 1 d", "and 1 3 1 1 d", "i'm 1 3 1 1 d", "in1311d", "and1311d", "i'm1311d", "M 131 1 D", "M 13 11 D", "M 1 3 11 D", "M 13 1 1 D", "M 1311 D", "13 11D", "1311D", "M 1 3 1 1 D", "M1311D", "N 131 1 D", "N 13 11 D", "N 1 3 11 D", "N 13 1 1 D", "N 1311 D", "N13 11D", "N 1 3 1 1 D", "N1311D"]}, {"code": "M1311E", "variations": ["in 131 1 e", "and 131 1 e", "i'm 131 1 e", "in 13 11 e", "and 13 11 e", "i'm 13 11 e", "in 1 3 11 e", "and 1 3 11 e", "i'm 1 3 11 e", "in 13 1 1 e", "and 13 1 1 e", "i'm 13 1 1 e", "in 1311 e", "and 1311 e", "i'm 1311 e", "13 11e", "1311e", "in 1 3 1 1 e", "and 1 3 1 1 e", "i'm 1 3 1 1 e", "in1311e", "and1311e", "i'm1311e", "M 131 1 E", "M 13 11 E", "M 1 3 11 E", "M 13 1 1 E", "M 1311 E", "13 11E", "1311E", "M 1 3 1 1 E", "M1311E", "N 131 1 E", "N 13 11 E", "N 1 3 11 E", "N 13 1 1 E", "N 1311 E", "N13 11E", "N 1 3 1 1 E", "N1311E"]}, {"code": "M1311F", "variations": ["in 131 1 f", "and 131 1 f", "i'm 131 1 f", "in 13 11 f", "and 13 11 f", "i'm 13 11 f", "in 1 3 11 f", "and 1 3 11 f", "i'm 1 3 11 f", "in 13 1 1 f", "and 13 1 1 f", "i'm 13 1 1 f", "in 1311 f", "and 1311 f", "i'm 1311 f", "13 11f", "1311f", "in 1 3 1 1 f", "and 1 3 1 1 f", "i'm 1 3 1 1 f", "in1311f", "and1311f", "i'm1311f", "M 131 1 F", "M 13 11 F", "M 1 3 11 F", "M 13 1 1 F", "M 1311 F", "13 11F", "1311F", "M 1 3 1 1 F", "M1311F", "N 131 1 F", "N 13 11 F", "N 1 3 11 F", "N 13 1 1 F", "N 1311 F", "N13 11F", "N 1 3 1 1 F", "N1311F"]}, {"code": "M1322", "variations": ["in 1 322", "and 1 322", "i'm 1 322", "in 132 2", "and 132 2", "i'm 132 2", "in 13 22", "and 13 22", "i'm 13 22", "in 1 3 22", "and 1 3 22", "i'm 1 3 22", "in 13 2 2", "and 13 2 2", "i'm 13 2 2", "in 1322", "and 1322", "i'm 1322", "in 1 3 2 2", "and 1 3 2 2", "i'm 1 3 2 2", "in1322", "and1322", "i'm1322", "M 1 322", "M 132 2", "M 13 22", "M 1 3 22", "M 13 2 2", "M 1322", "13 22", "1322", "M 1 3 2 2", "M1322", "N 1 322", "N 132 2", "N 13 22", "N 1 3 22", "N 13 2 2", "N 1322", "N13 22", "N 1 3 2 2", "N1322"]}, {"code": "M1324", "variations": ["in 1 324", "and 1 324", "i'm 1 324", "in 132 4", "and 132 4", "i'm 132 4", "in 13 24", "and 13 24", "i'm 13 24", "in 1 3 24", "and 1 3 24", "i'm 1 3 24", "in 13 2 4", "and 13 2 4", "i'm 13 2 4", "in 1324", "and 1324", "i'm 1324", "in 1 3 2 4", "and 1 3 2 4", "i'm 1 3 2 4", "in1324", "and1324", "i'm1324", "M 1 324", "M 132 4", "M 13 24", "M 1 3 24", "M 13 2 4", "M 1324", "13 24", "1324", "M 1 3 2 4", "M1324", "N 1 324", "N 132 4", "N 13 24", "N 1 3 24", "N 13 2 4", "N 1324", "N13 24", "N 1 3 2 4", "N1324"]}, {"code": "M1330", "variations": ["in 1 330", "and 1 330", "i'm 1 330", "in 133 o", "and 133 o", "i'm 133 o", "in 133 o ", "and 133 o ", "i'm 133 o ", "in 133 0", "and 133 0", "i'm 133 0", "in 1 3 3o", "and 1 3 3o", "i'm 1 3 3o", "in 13 30", "and 13 30", "i'm 13 30", "in 1 3 30", "and 1 3 30", "i'm 1 3 30", "in 13 3 o", "and 13 3 o", "i'm 13 3 o", "in 13 3 0", "and 13 3 0", "i'm 13 3 0", "in 133o", "and 133o", "i'm 133o", "in 1 3 3 o", "and 1 3 3 o", "i'm 1 3 3 o", "in 1330", "and 1330", "i'm 1330", "in 1 3 3 0", "and 1 3 3 0", "i'm 1 3 3 0", "in1330", "and1330", "i'm1330", "M 1 330", "M 133 o", "M 133 o ", "M 133 0", "M 1 3 3o", "M 13 30", "M 1 3 30", "M 13 3 o", "M 13 3 0", "M 133o", "M 1 3 3 o", "M 1330", "13 30", "1330", "M 1 3 3 0", "M1330", "N 1 330", "N 133 o", "N 133 o ", "N 133 0", "N 1 3 3o", "N 13 30", "N 1 3 30", "N 13 3 o", "N 13 3 0", "N 133o", "N 1 3 3 o", "N 1330", "N13 30", "N 1 3 3 0", "N1330"]}, {"code": "M1332", "variations": ["in 1 332", "and 1 332", "i'm 1 332", "in 133 2", "and 133 2", "i'm 133 2", "in 13 32", "and 13 32", "i'm 13 32", "in 1 3 32", "and 1 3 32", "i'm 1 3 32", "in 13 3 2", "and 13 3 2", "i'm 13 3 2", "in 1332", "and 1332", "i'm 1332", "in 1 3 3 2", "and 1 3 3 2", "i'm 1 3 3 2", "in1332", "and1332", "i'm1332", "M 1 332", "M 133 2", "M 13 32", "M 1 3 32", "M 13 3 2", "M 1332", "13 32", "1332", "M 1 3 3 2", "M1332", "N 1 332", "N 133 2", "N 13 32", "N 1 3 32", "N 13 3 2", "N 1332", "N13 32", "N 1 3 3 2", "N1332"]}, {"code": "M1334", "variations": ["in 1 334", "and 1 334", "i'm 1 334", "in 133 4", "and 133 4", "i'm 133 4", "in 13 34", "and 13 34", "i'm 13 34", "in 1 3 34", "and 1 3 34", "i'm 1 3 34", "in 13 3 4", "and 13 3 4", "i'm 13 3 4", "in 1334", "and 1334", "i'm 1334", "in 1 3 3 4", "and 1 3 3 4", "i'm 1 3 3 4", "in1334", "and1334", "i'm1334", "M 1 334", "M 133 4", "M 13 34", "M 1 3 34", "M 13 3 4", "M 1334", "13 34", "1334", "M 1 3 3 4", "M1334", "N 1 334", "N 133 4", "N 13 34", "N 1 3 34", "N 13 3 4", "N 1334", "N13 34", "N 1 3 3 4", "N1334"]}, {"code": "M1340", "variations": ["in 1 340", "and 1 340", "i'm 1 340", "in 134 o", "and 134 o", "i'm 134 o", "in 134 o ", "and 134 o ", "i'm 134 o ", "in 134 0", "and 134 0", "i'm 134 0", "in 1 3 4o", "and 1 3 4o", "i'm 1 3 4o", "in 13 40", "and 13 40", "i'm 13 40", "in 1 3 40", "and 1 3 40", "i'm 1 3 40", "in 13 4 o", "and 13 4 o", "i'm 13 4 o", "in 13 4 0", "and 13 4 0", "i'm 13 4 0", "in 134o", "and 134o", "i'm 134o", "in 1 3 4 o", "and 1 3 4 o", "i'm 1 3 4 o", "in 1340", "and 1340", "i'm 1340", "in 1 3 4 0", "and 1 3 4 0", "i'm 1 3 4 0", "in1340", "and1340", "i'm1340", "M 1 340", "M 134 o", "M 134 o ", "M 134 0", "M 1 3 4o", "M 13 40", "M 1 3 40", "M 13 4 o", "M 13 4 0", "M 134o", "M 1 3 4 o", "M 1340", "13 40", "1340", "M 1 3 4 0", "M1340", "N 1 340", "N 134 o", "N 134 o ", "N 134 0", "N 1 3 4o", "N 13 40", "N 1 3 40", "N 13 4 o", "N 13 4 0", "N 134o", "N 1 3 4 o", "N 1340", "N13 40", "N 1 3 4 0", "N1340"]}, {"code": "M1342", "variations": ["in 1 342", "and 1 342", "i'm 1 342", "in 134 2", "and 134 2", "i'm 134 2", "in 13 42", "and 13 42", "i'm 13 42", "in 1 3 42", "and 1 3 42", "i'm 1 3 42", "in 13 4 2", "and 13 4 2", "i'm 13 4 2", "in 1342", "and 1342", "i'm 1342", "in 1 3 4 2", "and 1 3 4 2", "i'm 1 3 4 2", "in1342", "and1342", "i'm1342", "M 1 342", "M 134 2", "M 13 42", "M 1 3 42", "M 13 4 2", "M 1342", "13 42", "1342", "M 1 3 4 2", "M1342", "N 1 342", "N 134 2", "N 13 42", "N 1 3 42", "N 13 4 2", "N 1342", "N13 42", "N 1 3 4 2", "N1342"]}, {"code": "M1400", "variations": ["in 1 400", "and 1 400", "i'm 1 400", "in 140 o", "and 140 o", "i'm 140 o", "in 14o 0", "and 14o 0", "i'm 14o 0", "in 140 o ", "and 140 o ", "i'm 140 o ", "in 140 0", "and 140 0", "i'm 140 0", "in 14 0 o", "and 14 0 o", "i'm 14 0 o", "in 14 o 0", "and 14 o 0", "i'm 14 o 0", "in 14 o o", "and 14 o o", "i'm 14 o o", "in 14 0 0", "and 14 0 0", "i'm 14 0 0", "in 1 4 0 o", "and 1 4 0 o", "i'm 1 4 0 o", "in 1 4 o 0", "and 1 4 o 0", "i'm 1 4 o 0", "in 14oo", "and 14oo", "i'm 14oo", "in 1 4 o o", "and 1 4 o o", "i'm 1 4 o o", "in 1400", "and 1400", "i'm 1400", "in 1 4 0 0", "and 1 4 0 0", "i'm 1 4 0 0", "in1400", "and1400", "i'm1400", "M 1 400", "M 140 o", "M 14o 0", "M 140 o ", "M 140 0", "M 14 0 o", "M 14 o 0", "M 14 o o", "M 14 0 0", "M 1 4 0 o", "M 1 4 o 0", "M 14oo", "M 1 4 o o", "M 1400", "14 00", "1400", "M 1 4 0 0", "M1400", "N 1 400", "N 140 o", "N 14o 0", "N 140 o ", "N 140 0", "N 14 0 o", "N 14 o 0", "N 14 o o", "N 14 0 0", "N 1 4 0 o", "N 1 4 o 0", "N 14oo", "N 1 4 o o", "N 1400", "N14 00", "N 1 4 0 0", "N1400"]}, {"code": "M1033", "variations": ["in 103 3", "and 103 3", "i'm 103 3", "in 1 o 33", "and 1 o 33", "i'm 1 o 33", "in 10 33", "and 10 33", "i'm 10 33", "in 1 0 33", "and 1 0 33", "i'm 1 0 33", "in 1o 3 3", "and 1o 3 3", "i'm 1o 3 3", "in 10 3 3", "and 10 3 3", "i'm 10 3 3", "in 1o33", "and 1o33", "i'm 1o33", "in 1 o 3 3", "and 1 o 3 3", "i'm 1 o 3 3", "in 1033", "and 1033", "i'm 1033", "in 1 0 3 3", "and 1 0 3 3", "i'm 1 0 3 3", "in1033", "and1033", "i'm1033", "M 103 3", "M 1 o 33", "M 10 33", "M 1 0 33", "M 1o 3 3", "M 10 3 3", "M 1o33", "M 1 o 3 3", "M 1033", "1 033", "1033", "M 1 0 3 3", "M1033", "N 103 3", "N 1 o 33", "N 10 33", "N 1 0 33", "N 1o 3 3", "N 10 3 3", "N 1o33", "N 1 o 3 3", "N 1033", "N1 033", "N 1 0 3 3", "N1033"]}, {"code": "GG0130", "variations": ["G G 0130", "G G 0 13o", "G G o 130", "G G 0 130", "G G 0 1 3o", "G G o 1 30", "G G o 1 3o", "G G 0 1 30", "G G 0 13 o", "G G o 13 0", "G G o 13 o", "G G 0 13 0", "GG 013o", "GG o130", "G G 0 1 3 o", "G G o 1 3 0", "GG o13o", "G G o 1 3 o", "GG 0130", "GG013 0", "G G 0 1 3 0", "GG0130", "gg 13th", "gg130", "gg 130", "g g 130"]}, {"code": "M1060", "variations": ["in 106 o", "and 106 o", "i'm 106 o", "in 1o6 0", "and 1o6 0", "i'm 1o6 0", "in 106 o ", "and 106 o ", "i'm 106 o ", "in 106 0", "and 106 0", "i'm 106 0", "in 1 o 6o", "and 1 o 6o", "i'm 1 o 6o", "in 1 0 6o", "and 1 0 6o", "i'm 1 0 6o", "in 1 o 60", "and 1 o 60", "i'm 1 o 60", "in 10 60", "and 10 60", "i'm 10 60", "in 1 0 60", "and 1 0 60", "i'm 1 0 60", "in 10 6 o", "and 10 6 o", "i'm 10 6 o", "in 1o 6 0", "and 1o 6 0", "i'm 1o 6 0", "in 1o 6 o", "and 1o 6 o", "i'm 1o 6 o", "in 10 6 0", "and 10 6 0", "i'm 10 6 0", "in 1 0 6 o", "and 1 0 6 o", "i'm 1 0 6 o", "in 1 o 6 0", "and 1 o 6 0", "i'm 1 o 6 0", "in 1o6o", "and 1o6o", "i'm 1o6o", "in 1 o 6 o", "and 1 o 6 o", "i'm 1 o 6 o", "in 1060", "and 1060", "i'm 1060", "in 1 0 6 0", "and 1 0 6 0", "i'm 1 0 6 0", "in1060", "and1060", "i'm1060", "M 106 o", "M 1o6 0", "M 106 o ", "M 106 0", "M 1 o 6o", "M 1 0 6o", "M 1 o 60", "M 10 60", "M 1 0 60", "M 10 6 o", "M 1o 6 0", "M 1o 6 o", "M 10 6 0", "M 1 0 6 o", "M 1 o 6 0", "M 1o6o", "M 1 o 6 o", "M 1060", "1 060", "1060", "M 1 0 6 0", "M1060", "N 106 o", "N 1o6 0", "N 106 o ", "N 106 0", "N 1 o 6o", "N 1 0 6o", "N 1 o 60", "N 10 60", "N 1 0 60", "N 10 6 o", "N 1o 6 0", "N 1o 6 o", "N 10 6 0", "N 1 0 6 o", "N 1 o 6 0", "N 1o6o", "N 1 o 6 o", "N 1060", "N1 060", "N 1 0 6 0", "N1060"]}, {"code": "GG0170", "variations": ["G G 0170", "G G 0 17o", "G G o 170", "G G 0 170", "G G 0 1 7o", "G G o 1 70", "G G o 1 7o", "G G 0 1 70", "G G 0 17 o", "G G o 17 0", "G G o 17 o", "G G 0 17 0", "GG 017o", "GG o170", "G G 0 1 7 o", "G G o 1 7 0", "GG o17o", "G G o 1 7 o", "GG 0170", "GG017 0", "G G 0 1 7 0", "GG0170", "gg 17th", "juju 170", "gg170", "gg 170", "g g 170"]}, {"code": "SOC", "variations": ["starter care"]}]