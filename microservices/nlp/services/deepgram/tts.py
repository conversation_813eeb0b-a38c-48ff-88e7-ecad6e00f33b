import json
import os
import time

from aiohttp import request


async def call_dg(text: str):
    DEEPGRAM_API_KEY = os.getenv("DEEPGRAM_API_KEY")

    print(f'TTS ...')
    start = time.time()
    outcome = request(
        method="POST",
        url="https://api.beta.deepgram.com/v1/speak?model=alpha-asteria-en&encoding=mp3",
        headers={
            "Authorization": f"Token {DEEPGRAM_API_KEY}",
            "Content-Type": "application/json",
        },
        data=json.dumps({"text": text})
    )

    has_error = False
    async with outcome as response:
        headers = response.headers
        print(f'Headers: {headers}')
        if response.status != 200:
            has_error = True
            print(f'Error: {response.status}')
            print(f'Reason: {response.reason}')
            print(f'Text: {await response.text()}')
        file = await response.read()

    time_taken = round(time.time() - start, 2)
    print(f'TTS complete in {time_taken} seconds ...')

    return file


# async def tts():
#     text = "Am in the living room"
#     result = await call_dg(text)
#
#     with open('file.mp3', 'wb') as f:
#         f.write(result)
#
#
# if __name__ == '__main__':
#     import asyncio
#
#     asyncio.run(tts())
