const fs = require('fs');
const variations = require('./variations.json');
const transcript_codes = require('../../_cache/SOC-BASE-REF/1736094542.065198.json');
const { codes: special_codes, mapping: special_mapping } = require('./specials.json');

const codes = transcript_codes.map((item) => {
    return item.question.startsWith('Based on the OASIS E item') ? item : null
}).filter(Boolean).concat(special_codes);

function _cloneSet(set) {
    // clone a set to avoid mutating the original
    return new Set([...set]);
}

function _spaceOutBasedOnType(code) {
    // for example : GG0130 to GG 0 1 3 0
    return code.replace(/([A-Z]+)([0-9]+)/g, '$1 $2').replace(/([0-9]+)([A-Z]+)/g, '$1 $2');
}

function _removeMCodes(code) {
    // for example : M1800 to 1800
    if (code.charAt(1) === '0') {
        return code
    }
    return code.replace(/^M/g, '');
}

function _splitNumberBySpeechLike(code) {
    // for example : 1810 to 18 10
    if (code.startsWith('0')) {
        return code
    }
    return code.replace(/([1-9]{1,2})([0-9]{1,2})/g, '$1 $2');
}

function _spaceOut(code) {
    return code.split('').join(' ');
}

function _0toO(code, {spaced = false, individual = false} = {}) {
    if (individual) {
        const numbers = code.match(/\s+0\s*/g);
        if (!numbers) return [];
        return numbers.reduce((acc) => {
            acc.push(code.replace('0', 'o'))
            const codeCopy = JSON.parse(JSON.stringify(code));
            const reversed = codeCopy.split('').reverse().join('').replace('0', 'o')
            acc.push(reversed.split('').reverse().join(''))
            return acc
        }, [])
    }

    const regex = spaced ? /\s+0\s*/g : /0/g;
    return code.replace(regex, spaced ? ' o ' : 'o');
}

function groupByTwoInternal(codeVal, first, second, variations) {
    let inPlaceVariation = '';
    if (first) {
        const firstVariation = _spaceOut(codeVal).replace(`${first[0]} ${first[1]}`, first);
        variations.add(firstVariation)
        inPlaceVariation = firstVariation
    }

    variations.add(_0toO(inPlaceVariation, true))
    _0toO(inPlaceVariation, {individual: true}).forEach((v) => variations.add(v))

    if (second) {
        const secondVariation = _spaceOut(codeVal).replace(`${second[0]} ${second[1]}`, second);
        inPlaceVariation = inPlaceVariation.replace(`${second[0]} ${second[1]}`, second);
        variations.add(secondVariation)
        variations.add(inPlaceVariation)
        variations.add(_0toO(secondVariation, {spaced: true}))
        variations.add(_0toO(inPlaceVariation, {spaced: true}))
        _0toO(secondVariation, {individual: true}).forEach((v) => variations.add(v))
        _0toO(inPlaceVariation, {individual: true}).forEach((v) => variations.add(v))
    }
}

function groupByTwo(codeVal, variations) {
    // group two number characters together in the code
    const [first, second] = codeVal.match(/[1-9][0-9]/g);
    groupByTwoInternal(codeVal, first, second, variations)

    const endMatches = codeVal.match(/[1-9][0-9]$/g);
    if (!endMatches) return;
    [first_end, second_end] = endMatches;
    groupByTwoInternal(codeVal, first_end, second_end, variations)
}

function groupByThree(codeVal, variations) {
    const codeNumbered = codeVal.match(/([1-9][0-9][0-9])/g);
    if (codeNumbered) {
        const [three_num] = codeNumbered;
        const threeNumVariation = _spaceOut(codeVal).replace(`${_spaceOut(three_num)}`, three_num);
        variations.add(threeNumVariation)
        variations.add(_0toO(threeNumVariation, {spaced: true}))
        _0toO(threeNumVariation, {individual: true}).forEach((v) => variations.add(v))
    }

    const codeNumberedEnd = codeVal.match(/([1-9][0-9][0-9])$/g);
    if (codeNumberedEnd) {
        const [three_num] = codeNumberedEnd;
        const threeNumVariation = _spaceOut(codeVal).replace(`${_spaceOut(three_num)}`, three_num);
        variations.add(threeNumVariation)
        variations.add(_0toO(threeNumVariation, {spaced: true}))
        _0toO(threeNumVariation, {individual: true}).forEach((v) => variations.add(v))
    }
}

function groupByAllDigits(codeVal, variations) {
    const [all_digits] = codeVal.match(/([0-9]+)/g);
    const allDigitsVariation = _spaceOut(codeVal).replace(`${_spaceOut(all_digits)}`, all_digits);
    variations.add(allDigitsVariation)
}

function generateMisheardCodes(codeVal, variations) {
    if (codeVal.startsWith('M')) {
        const preMisheardVariations = Array.from(_cloneSet(variations));
        const misheardVariations = [];
        preMisheardVariations.forEach((variate) => {
            const misheardCode_im = variate.toLowerCase().replace('m', 'i\'m');
            const misheardCode_and = variate.toLowerCase().replace('m', 'and');
            const misheardCode_in = variate.toLowerCase().replace('m', 'in');
            misheardVariations.push(misheardCode_im, misheardCode_and, misheardCode_in);
        })
        misheardVariations.forEach((misheard) => variations.add(misheard))
    }
}

function reverseVariations(variations) {
    const setArray = Array.from(variations);
    setArray.reverse();
    variations.clear();
    setArray.forEach(item => variations.add(item));
}

function variate() {
    const mappedCodes = codes.reduce((acc, item) => {
        if (item.code.startsWith('M')) {
            acc.push({code: item.code.replace('M', 'N'), mapTo: item.code})
        }
        if (item.code.startsWith('N')) {
            acc.push({code: item.code.replace('N', 'M'), mapTo: item.code})
        }
        return acc
    }, codes)

    const variationMap = mappedCodes.map((code) => {
        const { code: codeVal, mapTo } = code;
        const variations = new Set();

        // split the characters in the code
        variations.add(codeVal)
        variations.add(_spaceOut(codeVal))
        variations.add(_removeMCodes(codeVal))
        variations.add(_splitNumberBySpeechLike(_removeMCodes(codeVal)))
        variations.add(_spaceOutBasedOnType(codeVal))

        // replace the 0 characters in the code with o's
        variations.add(_0toO(_spaceOut(codeVal)))
        variations.add(_0toO(_spaceOutBasedOnType(codeVal)))
        _0toO(_spaceOut(codeVal), {individual: true}).forEach((v) => variations.add(v))
        _0toO(_spaceOutBasedOnType(codeVal), {individual: true}).forEach((v) => variations.add(v))

        // group two number characters together in the code
        groupByTwo(codeVal, variations)

        // group three number characters together in the code
        groupByThree(codeVal, variations)

        const hasR = codeVal.match(/R/gi);
        if (hasR) {
            const rVariation = _spaceOut(codeVal).replace(/R/gi, 'L');
            variations.add(rVariation)
            variations.add(_0toO(rVariation, {spaced: true}))
            _0toO(_spaceOut(rVariation), {individual: true}).forEach((v) => variations.add(v))
            const RtoLCode = codeVal.replace(/R/gi, 'L');
            groupByTwo(RtoLCode, variations)
            groupByThree(RtoLCode, variations)
        }

        groupByAllDigits(codeVal, variations)

        generateMisheardCodes(codeVal, variations)

        reverseVariations(variations)
        const specials = special_mapping[codeVal];
        return {code: mapTo || code.code, variations: Array.from(variations).concat(specials || [])}
    })

    // join possible duplicate codes and their variations
    const variationMapArr = variationMap.reduce((acc, item) => {
        const {code, variations} = item;
        const codeIndex = acc.findIndex((item) => item.code === code);
        if (codeIndex === -1) {
            acc.push(item)
        } else {
            const set = new Set(acc[codeIndex].variations.concat(variations));
            acc[codeIndex].variations = Array.from(set)
        }
        return acc
    }, []);

    // add any missing special codes
    const missingSpecials = Object.keys(special_mapping).filter((special) => {
        return !variationMapArr.find((item) => item.code === special)
    }).map((special) => {
        return {code: special, variations: special_mapping[special]}
    });
    variationMapArr.push(...missingSpecials)

    fs.writeFileSync('./variations.json', JSON.stringify(variationMapArr, null, 2))
}

function counter() {
    let code_count = 0;
    let variation_count = 0;
    let highestVariant = {
        code: '',
        count: 0
    }

    variations.forEach((item) => {
        const {code, variations} = item;
        code_count += 1
        variation_count += variations.length

        if (variations.length > highestVariant.count) {
            highestVariant = {
                code,
                count: variations.length
            }
        }
    });

    console.log('code: ', code_count);
    console.log('variation: ', variation_count);
    console.log('highest-variant - code: ', highestVariant.code);
    console.log('highest-variant - count: ', highestVariant.count);
}

variate()
// counter()
