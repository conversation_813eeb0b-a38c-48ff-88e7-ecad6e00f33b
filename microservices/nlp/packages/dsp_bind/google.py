import os
from typing import Any, Optional

from dsp.modules.lm import LM
from google import genai
from google.genai import types


def backoff_hdlr(details):
    """Handler from https://pypi.org/project/backoff/"""
    print(
        "Backing off {wait:0.1f} seconds after {tries} tries "
        "calling function {target} with kwargs "
        "{kwargs}".format(**details),
    )


def giveup_hdlr(details):
    """wrapper function that decides when to give up on retry"""
    if "rate limits" in details.message:
        return False
    return True


class Gemini(LM):
    """Wrapper around Google's API.

    Currently supported models include `gemini-pro-1.0`.
    """

    def __init__(
            self,
            model: str = "gemini-2.5-flash",
            api_key: Optional[str] = None,
            **kwargs,
    ):
        """
        Parameters
        ----------
        model : str
            Which pre-trained model from Google to use?
        api_key : str
            The API key for Google.
        **kwargs: dict
            Additional arguments to pass to the API provider.
        """
        super().__init__(model)
        api_key = os.environ.get("GOOGLE_API_KEY") if api_key is None else api_key

        self.model = model

        # Google API uses "candidate_count" instead of "n" or "num_generations"
        # For now, google API only supports 1 generation at a time. Raises an error if candidate_count > 1

        self.provider = "google"
        kwargs = {
            "candidate_count": 1,
            "temperature": 0.0 if "temperature" not in kwargs else kwargs["temperature"],
            "max_output_tokens": 4096,
            **kwargs,
        }

        self.llm = genai.Client(api_key=api_key)
        self.kwargs = {
            **kwargs,
        }

        self.history: list[dict[str, Any]] = []

    def basic_request(self, prompt: str, **kwargs):
        raw_kwargs = kwargs
        kwargs = {
            **self.kwargs,
            **kwargs,
        }

        config = types.GenerateContentConfig(**kwargs)

        response = self.llm.models.generate_content(
            model=self.model,
            config=config,
            contents=[types.Content(role="user", parts=[types.Part.from_text(text=prompt)])]
        )

        history = {
            "prompt": prompt,
            "response": [response.text],
            "kwargs": kwargs,
            "raw_kwargs": raw_kwargs
        }
        self.history.append(history)

        return response.text

    def request(self, prompt: str, **kwargs):
        """Handles retrieval of completions from Google whilst handling API errors"""
        return self.basic_request(prompt, **kwargs)

    def __call__(
            self,
            prompt: str,
            only_completed: bool = True,
            return_sorted: bool = False,
            **kwargs,
    ):
        assert only_completed, "for now"
        assert return_sorted is False, "for now"

        completions = [self.request(prompt, **kwargs)]

        return completions
