# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

from .create_event_event import CreateEventEvent
from .create_generation_event import CreateGenerationEvent
from .create_observation_event import CreateObservationEvent
from .create_span_event import CreateSpanEvent
from .score_event import ScoreEvent
from .sdk_log_event import SdkLogEvent
from .trace_event import TraceEvent
from .update_generation_event import UpdateGenerationEvent
from .update_observation_event import UpdateObservationEvent
from .update_span_event import UpdateSpanEvent


class IngestionEvent_TraceCreate(TraceEvent):
    type: typing.Literal["trace-create"] = "trace-create"

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True


class IngestionEvent_ScoreCreate(ScoreEvent):
    type: typing.Literal["score-create"] = "score-create"

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True


class IngestionEvent_EventCreate(CreateEventEvent):
    type: typing.Literal["event-create"] = "event-create"

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True


class IngestionEvent_GenerationCreate(CreateGenerationEvent):
    type: typing.Literal["generation-create"] = "generation-create"

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True


class IngestionEvent_GenerationUpdate(UpdateGenerationEvent):
    type: typing.Literal["generation-update"] = "generation-update"

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True


class IngestionEvent_SpanCreate(CreateSpanEvent):
    type: typing.Literal["span-create"] = "span-create"

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True


class IngestionEvent_SpanUpdate(UpdateSpanEvent):
    type: typing.Literal["span-update"] = "span-update"

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True


class IngestionEvent_SdkLog(SdkLogEvent):
    type: typing.Literal["sdk-log"] = "sdk-log"

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True


class IngestionEvent_ObservationCreate(CreateObservationEvent):
    type: typing.Literal["observation-create"] = "observation-create"

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True


class IngestionEvent_ObservationUpdate(UpdateObservationEvent):
    type: typing.Literal["observation-update"] = "observation-update"

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True


IngestionEvent = typing.Union[
    IngestionEvent_TraceCreate,
    IngestionEvent_ScoreCreate,
    IngestionEvent_EventCreate,
    IngestionEvent_GenerationCreate,
    IngestionEvent_GenerationUpdate,
    IngestionEvent_SpanCreate,
    IngestionEvent_SpanUpdate,
    IngestionEvent_SdkLog,
    IngestionEvent_ObservationCreate,
    IngestionEvent_ObservationUpdate,
]
