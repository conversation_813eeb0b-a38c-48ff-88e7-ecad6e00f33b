# This file was auto-generated by <PERSON>rn from our API Definition.

import datetime as dt
import typing

from ....core.datetime_utils import serialize_datetime
from ....core.pydantic_utilities import pydantic_v1


class CreateTextPromptRequest(pydantic_v1.BaseModel):
    name: str
    prompt: str
    config: typing.Optional[typing.Any] = None
    labels: typing.Optional[typing.List[str]] = pydantic_v1.Field(default=None)
    """
    List of deployment labels of this prompt version.
    """

    tags: typing.Optional[typing.List[str]] = pydantic_v1.Field(default=None)
    """
    List of tags to apply to all versions of this prompt.
    """

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}
