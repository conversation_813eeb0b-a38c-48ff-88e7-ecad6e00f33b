# This file was auto-generated by <PERSON>rn from our API Definition.

import datetime as dt
import typing

from ....core.datetime_utils import serialize_datetime
from ....core.pydantic_utilities import pydantic_v1


class DatasetRunItem(pydantic_v1.BaseModel):
    id: str
    dataset_run_id: str = pydantic_v1.Field(alias="datasetRunId")
    dataset_run_name: str = pydantic_v1.Field(alias="datasetRunName")
    dataset_item_id: str = pydantic_v1.Field(alias="datasetItemId")
    trace_id: str = pydantic_v1.Field(alias="traceId")
    observation_id: typing.Optional[str] = pydantic_v1.Field(
        alias="observationId", default=None
    )
    created_at: dt.datetime = pydantic_v1.Field(alias="createdAt")
    updated_at: dt.datetime = pydantic_v1.Field(alias="updatedAt")

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}
