# This file was auto-generated by <PERSON>rn from our API Definition.

import datetime as dt
import typing

from ....core.datetime_utils import serialize_datetime
from ....core.pydantic_utilities import pydantic_v1
from .map_value import MapValue
from .observation_level import ObservationLevel
from .usage import Usage


class Observation(pydantic_v1.BaseModel):
    id: str
    trace_id: typing.Optional[str] = pydantic_v1.Field(alias="traceId", default=None)
    type: str
    name: typing.Optional[str] = None
    start_time: dt.datetime = pydantic_v1.Field(alias="startTime")
    end_time: typing.Optional[dt.datetime] = pydantic_v1.Field(
        alias="endTime", default=None
    )
    completion_start_time: typing.Optional[dt.datetime] = pydantic_v1.Field(
        alias="completionStartTime", default=None
    )
    model: typing.Optional[str] = None
    model_parameters: typing.Optional[typing.Dict[str, MapValue]] = pydantic_v1.Field(
        alias="modelParameters", default=None
    )
    input: typing.Optional[typing.Any] = None
    version: typing.Optional[str] = None
    metadata: typing.Optional[typing.Any] = None
    output: typing.Optional[typing.Any] = None
    usage: typing.Optional[Usage] = None
    level: ObservationLevel
    status_message: typing.Optional[str] = pydantic_v1.Field(
        alias="statusMessage", default=None
    )
    parent_observation_id: typing.Optional[str] = pydantic_v1.Field(
        alias="parentObservationId", default=None
    )
    prompt_id: typing.Optional[str] = pydantic_v1.Field(alias="promptId", default=None)

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}
