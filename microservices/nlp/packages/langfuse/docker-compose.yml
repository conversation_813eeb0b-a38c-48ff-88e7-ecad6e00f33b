services:
  langfuse-server:
    image: langfuse/langfuse:2
    depends_on:
      db:
        condition: service_healthy
    ports:
      - "3333:3333"
    environment:
      - DATABASE_URL=**************************************/postgres
      - NEXTAUTH_SECRET=mysecret
      - SALT=mysalt
      - HOSTNAME=0.0.0.0
      - PORT=3333
      - NEXTAUTH_URL=http://localhost:3333
      - TELEMETRY_ENABLED=${TELEMETRY_ENABLED:-false}
      - LANGFUSE_ENABLE_EXPERIMENTAL_FEATURES=${LANGFUSE_ENABLE_EXPERIMENTAL_FEATURES:-true}

  db:
    image: postgres
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 3s
      timeout: 3s
      retries: 10
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=postgres
    ports:
      - 5432:5432
    volumes:
      - database_data:/var/lib/postgresql/data

volumes:
  database_data:
    driver: local
