import asyncio
from http import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Text, Union

from fastapi import HTTPException


def validate_field(field_name: Text, body, values: list = None, required: bool = True):
    if field_name not in body.dict():
        if required:
            raise ErrorResponse(
                status=HTTPStatus.BAD_REQUEST,
                reason=f"Required '{field_name}' field is missing",
                message=f"Required '{field_name}' field is missing"
            )
        if (values is not None) and (body.get(field_name) not in values):
            raise ErrorResponse(
                status=HTTPStatus.BAD_REQUEST,
                reason=f"'{field_name}' field is not one of {values}",
                message=f"'{field_name}' field is not one of {values}"
            )


async def run_in_process(executor, fn, *args):
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(executor, fn, *args)  # wait and return result


class ErrorResponse(HTTPException):
    """Common exception to handle failing API requests."""

    def __init__(
            self,
            status: Union[int, HTTPStatus],
            reason: Text,
            message: Text
    ) -> None:
        """Creates error.

        Args:
            status: The HTTP status code to return.
            reason: Short summary of the error.
            message: Detailed explanation of the error.
        """
        self.error_info = {
            "status": "failure",
            "message": message,
            "reason": reason,
            "code": status,
        }
        self.status = status
        self.message = message
        super(ErrorResponse, self).__init__(status_code=status, detail=message)

