FROM python:3.10

COPY . .

COPY ./config/aws /root/.aws

COPY ./config/aws ~/.aws

# Install poetry
RUN pip install poetry

# Configure poetry to create the virtual environment in the project directory
RUN poetry config virtualenvs.in-project true

# Install dependencies using poetry
RUN poetry install --verbose --no-root

EXPOSE 3200

# Use poetry to run the main.py script
ENTRYPOINT ["poetry", "run", "python", "main.py"]
