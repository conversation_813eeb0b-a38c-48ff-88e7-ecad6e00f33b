image: python:3.10

include:
  - local: '/.gitlab/templates.yml'


stages:
  - test
  #- build
  #- deploy
  - pre-deploy
  #- cleanup

test_nlp_microservice:
  stage: test
  interruptible: true
#  rules:
#    - when: always
  except:
    refs:
      - master
      - release
    variables:
      - $CI_COMMIT_MESSAGE =~ /skip-test/
  variables:
    RP_API_KEY: $RP_API_KEY
    RP_ATTRIBUTES: branch:$CI_COMMIT_BRANCH,commit:$CI_COMMIT_SHA
    RP_DESCRIPTION: https://gitlab.patrium.health/engineering/backend/api/-/commit/$CI_COMMIT_SHA
  before_script:
    - cd microservices/nlp
    # Install packages
    - pip install -U poetry pytest
    - poetry config virtualenvs.in-project true
    - poetry install --no-root
    - pytest --version
    # Create config
    - cp config/.env.dev .env
  script:
    - poetry run pytest --cov=comprehend --cov-report=html --cov-report=term -v --junit-xml=report.xml tests/
  after_script:
    - cd microservices/nlp
    - poetry run python tests/upload-report.py
  coverage: '/TOTAL.*? (100(?:\.0+)?\%|[1-9]?\d(?:\.\d+)?\%)$/'
  artifacts:
    when: always
    paths:
      - microservices/nlp/htmlcov/*
    expire_in: 1 week
    reports:
      junit:
        - microservices/nlp/report.xml

dev_pre_deploy_nlp_microservice:
  stage: pre-deploy
  extends: .pre-deploy-template
  only:
    refs:
      - develop
  #  dependencies:
  #    - build_nlp_microservice
  script:
    - export VERSION_TAG="$CI_COMMIT_SHORT_SHA"
    - docker compose -f ./manifests/dev/dev.build.docker-compose.yml build nlp
    - docker compose -f ./manifests/dev/dev.build.docker-compose.yml push nlp
    - export VERSION_TAG="latest"
    - docker compose -f ./manifests/dev/dev.build.docker-compose.yml build nlp
    - docker compose -f ./manifests/dev/dev.build.docker-compose.yml push nlp

stage_pre_deploy_nlp_microservice:
  stage: pre-deploy
  extends: .pre-deploy-template
  only:
    refs:
      - master
      - devops/staging
  script:
    - export VERSION_TAG="$CI_COMMIT_SHORT_SHA"
    - docker compose -f ./manifests/stage/stage.build.docker-compose.yml build nlp
    - docker compose -f ./manifests/stage/stage.build.docker-compose.yml push nlp
    - export VERSION_TAG="latest"
    - docker compose -f ./manifests/stage/stage.build.docker-compose.yml build nlp
    - docker compose -f ./manifests/stage/stage.build.docker-compose.yml push nlp

pre_deploy_nlp_microservice:
  stage: pre-deploy
  extends: .pre-deploy-template
  only:
    refs:
      - release
      - /^release\/.*$/
  script:
    - export VERSION_TAG="$CI_COMMIT_SHORT_SHA"
    - docker compose -f ./manifests/prod/prod.build.docker-compose.yml build nlp
    - docker compose -f ./manifests/prod/prod.build.docker-compose.yml push nlp
    - export VERSION_TAG="latest"
    - docker compose -f ./manifests/prod/prod.build.docker-compose.yml build nlp
    - docker compose -f ./manifests/prod/prod.build.docker-compose.yml push nlp
