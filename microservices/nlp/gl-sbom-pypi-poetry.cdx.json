{"bomFormat": "CycloneDX", "specVersion": "1.4", "serialNumber": "urn:uuid:f22995a8-4857-4d31-acd9-be7861257090", "version": 1, "metadata": {"timestamp": "2025-08-28T23:10:47Z", "tools": [{"vendor": "GitLab", "name": "Gemnasium", "version": "5.9.5"}], "authors": [{"name": "GitLab", "email": "<EMAIL>"}], "properties": [{"name": "gitlab:dependency_scanning:input_file:path", "value": "microservices/nlp/poetry.lock"}, {"name": "gitlab:dependency_scanning:package_manager:name", "value": "poetry"}, {"name": "gitlab:meta:schema_version", "value": "1"}]}, "components": [{"name": "aiohappyeyeballs", "version": "2.6.1", "purl": "pkg:pypi/aiohappyeyeballs@2.6.1", "type": "library", "bom-ref": "pkg:pypi/aiohappyeyeballs@2.6.1"}, {"name": "aiohttp", "version": "3.11.18", "purl": "pkg:pypi/aiohttp@3.11.18", "type": "library", "bom-ref": "pkg:pypi/aiohttp@3.11.18"}, {"name": "aiosignal", "version": "1.3.2", "purl": "pkg:pypi/aiosignal@1.3.2", "type": "library", "bom-ref": "pkg:pypi/aiosignal@1.3.2"}, {"name": "alembic", "version": "1.15.2", "purl": "pkg:pypi/alembic@1.15.2", "type": "library", "bom-ref": "pkg:pypi/alembic@1.15.2"}, {"name": "amazon-sqs-extended-client", "version": "1.0.1", "purl": "pkg:pypi/amazon-sqs-extended-client@1.0.1", "type": "library", "bom-ref": "pkg:pypi/amazon-sqs-extended-client@1.0.1"}, {"name": "annotated-types", "version": "0.7.0", "purl": "pkg:pypi/annotated-types@0.7.0", "type": "library", "bom-ref": "pkg:pypi/annotated-types@0.7.0"}, {"name": "anthropic", "version": "0.51.0", "purl": "pkg:pypi/anthropic@0.51.0", "type": "library", "bom-ref": "pkg:pypi/anthropic@0.51.0"}, {"name": "anyio", "version": "4.9.0", "purl": "pkg:pypi/anyio@4.9.0", "type": "library", "bom-ref": "pkg:pypi/anyio@4.9.0"}, {"name": "async-timeout", "version": "5.0.1", "purl": "pkg:pypi/async-timeout@5.0.1", "type": "library", "bom-ref": "pkg:pypi/async-timeout@5.0.1"}, {"name": "attrs", "version": "25.3.0", "purl": "pkg:pypi/attrs@25.3.0", "type": "library", "bom-ref": "pkg:pypi/attrs@25.3.0"}, {"name": "authlib", "version": "1.3.1", "purl": "pkg:pypi/authlib@1.3.1", "type": "library", "bom-ref": "pkg:pypi/authlib@1.3.1"}, {"name": "backoff", "version": "2.2.1", "purl": "pkg:pypi/backoff@2.2.1", "type": "library", "bom-ref": "pkg:pypi/backoff@2.2.1"}, {"name": "blis", "version": "0.7.11", "purl": "pkg:pypi/blis@0.7.11", "type": "library", "bom-ref": "pkg:pypi/blis@0.7.11"}, {"name": "boto3", "version": "1.38.18", "purl": "pkg:pypi/boto3@1.38.18", "type": "library", "bom-ref": "pkg:pypi/boto3@1.38.18"}, {"name": "botocore", "version": "1.38.18", "purl": "pkg:pypi/botocore@1.38.18", "type": "library", "bom-ref": "pkg:pypi/botocore@1.38.18"}, {"name": "cachetools", "version": "5.5.2", "purl": "pkg:pypi/cachetools@5.5.2", "type": "library", "bom-ref": "pkg:pypi/cachetools@5.5.2"}, {"name": "catalogue", "version": "2.0.10", "purl": "pkg:pypi/catalogue@2.0.10", "type": "library", "bom-ref": "pkg:pypi/catalogue@2.0.10"}, {"name": "certifi", "version": "2025.4.26", "purl": "pkg:pypi/certifi@2025.4.26", "type": "library", "bom-ref": "pkg:pypi/certifi@2025.4.26"}, {"name": "cffi", "version": "1.17.1", "purl": "pkg:pypi/cffi@1.17.1", "type": "library", "bom-ref": "pkg:pypi/cffi@1.17.1"}, {"name": "charset-normalizer", "version": "3.4.2", "purl": "pkg:pypi/charset-normalizer@3.4.2", "type": "library", "bom-ref": "pkg:pypi/charset-normalizer@3.4.2"}, {"name": "click", "version": "8.2.0", "purl": "pkg:pypi/click@8.2.0", "type": "library", "bom-ref": "pkg:pypi/click@8.2.0"}, {"name": "cloudpathlib", "version": "0.21.1", "purl": "pkg:pypi/cloudpathlib@0.21.1", "type": "library", "bom-ref": "pkg:pypi/cloudpathlib@0.21.1"}, {"name": "colorama", "version": "0.4.6", "purl": "pkg:pypi/colorama@0.4.6", "type": "library", "bom-ref": "pkg:pypi/colorama@0.4.6"}, {"name": "colorlog", "version": "6.9.0", "purl": "pkg:pypi/colorlog@6.9.0", "type": "library", "bom-ref": "pkg:pypi/colorlog@6.9.0"}, {"name": "confection", "version": "0.1.5", "purl": "pkg:pypi/confection@0.1.5", "type": "library", "bom-ref": "pkg:pypi/confection@0.1.5"}, {"name": "coverage", "version": "7.10.1", "purl": "pkg:pypi/coverage@7.10.1", "type": "library", "bom-ref": "pkg:pypi/coverage@7.10.1"}, {"name": "cryptography", "version": "45.0.2", "purl": "pkg:pypi/cryptography@45.0.2", "type": "library", "bom-ref": "pkg:pypi/cryptography@45.0.2"}, {"name": "cymem", "version": "2.0.11", "purl": "pkg:pypi/cymem@2.0.11", "type": "library", "bom-ref": "pkg:pypi/cymem@2.0.11"}, {"name": "datasets", "version": "2.14.7", "purl": "pkg:pypi/datasets@2.14.7", "type": "library", "bom-ref": "pkg:pypi/datasets@2.14.7"}, {"name": "deepgram-sdk", "version": "2.12.0", "purl": "pkg:pypi/deepgram-sdk@2.12.0", "type": "library", "bom-ref": "pkg:pypi/deepgram-sdk@2.12.0"}, {"name": "deprecated", "version": "1.2.18", "purl": "pkg:pypi/deprecated@1.2.18", "type": "library", "bom-ref": "pkg:pypi/deprecated@1.2.18"}, {"name": "deprecation", "version": "2.1.0", "purl": "pkg:pypi/deprecation@2.1.0", "type": "library", "bom-ref": "pkg:pypi/deprecation@2.1.0"}, {"name": "dictdiffer", "version": "0.9.0", "purl": "pkg:pypi/dictdiffer@0.9.0", "type": "library", "bom-ref": "pkg:pypi/dictdiffer@0.9.0"}, {"name": "dill", "version": "0.3.7", "purl": "pkg:pypi/dill@0.3.7", "type": "library", "bom-ref": "pkg:pypi/dill@0.3.7"}, {"name": "diskcache", "version": "5.6.3", "purl": "pkg:pypi/diskcache@5.6.3", "type": "library", "bom-ref": "pkg:pypi/diskcache@5.6.3"}, {"name": "distro", "version": "1.9.0", "purl": "pkg:pypi/distro@1.9.0", "type": "library", "bom-ref": "pkg:pypi/distro@1.9.0"}, {"name": "dspy-ai", "version": "2.4.9", "purl": "pkg:pypi/dspy-ai@2.4.9", "type": "library", "bom-ref": "pkg:pypi/dspy-ai@2.4.9"}, {"name": "en-core-web-sm", "version": "3.7.1", "purl": "pkg:pypi/en-core-web-sm@3.7.1", "type": "library", "bom-ref": "pkg:pypi/en-core-web-sm@3.7.1"}, {"name": "exceptiongroup", "version": "1.3.0", "purl": "pkg:pypi/exceptiongroup@1.3.0", "type": "library", "bom-ref": "pkg:pypi/exceptiongroup@1.3.0"}, {"name": "<PERSON><PERSON><PERSON>", "version": "0.115.12", "purl": "pkg:pypi/fastapi@0.115.12", "type": "library", "bom-ref": "pkg:pypi/fastapi@0.115.12"}, {"name": "filelock", "version": "3.18.0", "purl": "pkg:pypi/filelock@3.18.0", "type": "library", "bom-ref": "pkg:pypi/filelock@3.18.0"}, {"name": "frozenlist", "version": "1.6.0", "purl": "pkg:pypi/frozenlist@1.6.0", "type": "library", "bom-ref": "pkg:pypi/frozenlist@1.6.0"}, {"name": "fsspec", "version": "2023.10.0", "purl": "pkg:pypi/fsspec@2023.10.0", "type": "library", "bom-ref": "pkg:pypi/fsspec@2023.10.0"}, {"name": "google-ai-generativelanguage", "version": "0.6.15", "purl": "pkg:pypi/google-ai-generativelanguage@0.6.15", "type": "library", "bom-ref": "pkg:pypi/google-ai-generativelanguage@0.6.15"}, {"name": "google-api-core", "version": "2.24.2", "purl": "pkg:pypi/google-api-core@2.24.2", "type": "library", "bom-ref": "pkg:pypi/google-api-core@2.24.2"}, {"name": "google-api-python-client", "version": "2.169.0", "purl": "pkg:pypi/google-api-python-client@2.169.0", "type": "library", "bom-ref": "pkg:pypi/google-api-python-client@2.169.0"}, {"name": "google-auth", "version": "2.40.1", "purl": "pkg:pypi/google-auth@2.40.1", "type": "library", "bom-ref": "pkg:pypi/google-auth@2.40.1"}, {"name": "google-auth-httplib2", "version": "0.2.0", "purl": "pkg:pypi/google-auth-httplib2@0.2.0", "type": "library", "bom-ref": "pkg:pypi/google-auth-httplib2@0.2.0"}, {"name": "google-cloud-appengine-logging", "version": "1.6.1", "purl": "pkg:pypi/google-cloud-appengine-logging@1.6.1", "type": "library", "bom-ref": "pkg:pypi/google-cloud-appengine-logging@1.6.1"}, {"name": "google-cloud-audit-log", "version": "0.3.2", "purl": "pkg:pypi/google-cloud-audit-log@0.3.2", "type": "library", "bom-ref": "pkg:pypi/google-cloud-audit-log@0.3.2"}, {"name": "google-cloud-core", "version": "2.4.3", "purl": "pkg:pypi/google-cloud-core@2.4.3", "type": "library", "bom-ref": "pkg:pypi/google-cloud-core@2.4.3"}, {"name": "google-cloud-logging", "version": "3.12.1", "purl": "pkg:pypi/google-cloud-logging@3.12.1", "type": "library", "bom-ref": "pkg:pypi/google-cloud-logging@3.12.1"}, {"name": "google-genai", "version": "1.15.0", "purl": "pkg:pypi/google-genai@1.15.0", "type": "library", "bom-ref": "pkg:pypi/google-genai@1.15.0"}, {"name": "google-generativeai", "version": "0.8.5", "purl": "pkg:pypi/google-generativeai@0.8.5", "type": "library", "bom-ref": "pkg:pypi/google-generativeai@0.8.5"}, {"name": "googleapis-common-protos", "version": "1.70.0", "purl": "pkg:pypi/googleapis-common-protos@1.70.0", "type": "library", "bom-ref": "pkg:pypi/googleapis-common-protos@1.70.0"}, {"name": "graphiti-core", "version": "0.6.1", "purl": "pkg:pypi/graphiti-core@0.6.1", "type": "library", "bom-ref": "pkg:pypi/graphiti-core@0.6.1"}, {"name": "greenlet", "version": "3.2.2", "purl": "pkg:pypi/greenlet@3.2.2", "type": "library", "bom-ref": "pkg:pypi/greenlet@3.2.2"}, {"name": "grpc-google-iam-v1", "version": "0.14.2", "purl": "pkg:pypi/grpc-google-iam-v1@0.14.2", "type": "library", "bom-ref": "pkg:pypi/grpc-google-iam-v1@0.14.2"}, {"name": "grpcio", "version": "1.71.0", "purl": "pkg:pypi/grpcio@1.71.0", "type": "library", "bom-ref": "pkg:pypi/grpcio@1.71.0"}, {"name": "grpcio-health-checking", "version": "1.71.0", "purl": "pkg:pypi/grpcio-health-checking@1.71.0", "type": "library", "bom-ref": "pkg:pypi/grpcio-health-checking@1.71.0"}, {"name": "grpcio-status", "version": "1.71.0", "purl": "pkg:pypi/grpcio-status@1.71.0", "type": "library", "bom-ref": "pkg:pypi/grpcio-status@1.71.0"}, {"name": "grpcio-tools", "version": "1.71.0", "purl": "pkg:pypi/grpcio-tools@1.71.0", "type": "library", "bom-ref": "pkg:pypi/grpcio-tools@1.71.0"}, {"name": "h11", "version": "0.16.0", "purl": "pkg:pypi/h11@0.16.0", "type": "library", "bom-ref": "pkg:pypi/h11@0.16.0"}, {"name": "httpcore", "version": "1.0.9", "purl": "pkg:pypi/httpcore@1.0.9", "type": "library", "bom-ref": "pkg:pypi/httpcore@1.0.9"}, {"name": "httplib2", "version": "0.22.0", "purl": "pkg:pypi/httplib2@0.22.0", "type": "library", "bom-ref": "pkg:pypi/httplib2@0.22.0"}, {"name": "httpx", "version": "0.28.1", "purl": "pkg:pypi/httpx@0.28.1", "type": "library", "bom-ref": "pkg:pypi/httpx@0.28.1"}, {"name": "huggingface-hub", "version": "0.31.4", "purl": "pkg:pypi/huggingface-hub@0.31.4", "type": "library", "bom-ref": "pkg:pypi/huggingface-hub@0.31.4"}, {"name": "idna", "version": "3.10", "purl": "pkg:pypi/idna@3.10", "type": "library", "bom-ref": "pkg:pypi/idna@3.10"}, {"name": "importlib-metadata", "version": "8.6.1", "purl": "pkg:pypi/importlib-metadata@8.6.1", "type": "library", "bom-ref": "pkg:pypi/importlib-metadata@8.6.1"}, {"name": "iniconfig", "version": "2.1.0", "purl": "pkg:pypi/iniconfig@2.1.0", "type": "library", "bom-ref": "pkg:pypi/iniconfig@2.1.0"}, {"name": "jinja2", "version": "3.1.6", "purl": "pkg:pypi/jinja2@3.1.6", "type": "library", "bom-ref": "pkg:pypi/jinja2@3.1.6"}, {"name": "jiter", "version": "0.10.0", "purl": "pkg:pypi/jiter@0.10.0", "type": "library", "bom-ref": "pkg:pypi/jiter@0.10.0"}, {"name": "jmespath", "version": "1.0.1", "purl": "pkg:pypi/jmespath@1.0.1", "type": "library", "bom-ref": "pkg:pypi/jmespath@1.0.1"}, {"name": "joblib", "version": "1.3.2", "purl": "pkg:pypi/joblib@1.3.2", "type": "library", "bom-ref": "pkg:pypi/joblib@1.3.2"}, {"name": "json-repair", "version": "0.44.1", "purl": "pkg:pypi/json-repair@0.44.1", "type": "library", "bom-ref": "pkg:pypi/json-repair@0.44.1"}, {"name": "langcodes", "version": "3.5.0", "purl": "pkg:pypi/langcodes@3.5.0", "type": "library", "bom-ref": "pkg:pypi/langcodes@3.5.0"}, {"name": "language-data", "version": "1.3.0", "purl": "pkg:pypi/language-data@1.3.0", "type": "library", "bom-ref": "pkg:pypi/language-data@1.3.0"}, {"name": "loguru", "version": "0.7.3", "purl": "pkg:pypi/loguru@0.7.3", "type": "library", "bom-ref": "pkg:pypi/loguru@0.7.3"}, {"name": "mako", "version": "1.3.10", "purl": "pkg:pypi/mako@1.3.10", "type": "library", "bom-ref": "pkg:pypi/mako@1.3.10"}, {"name": "marisa-trie", "version": "1.2.1", "purl": "pkg:pypi/marisa-trie@1.2.1", "type": "library", "bom-ref": "pkg:pypi/marisa-trie@1.2.1"}, {"name": "markdown-it-py", "version": "3.0.0", "purl": "pkg:pypi/markdown-it-py@3.0.0", "type": "library", "bom-ref": "pkg:pypi/markdown-it-py@3.0.0"}, {"name": "markupsafe", "version": "3.0.2", "purl": "pkg:pypi/markupsafe@3.0.2", "type": "library", "bom-ref": "pkg:pypi/markupsafe@3.0.2"}, {"name": "mdurl", "version": "0.1.2", "purl": "pkg:pypi/mdurl@0.1.2", "type": "library", "bom-ref": "pkg:pypi/mdurl@0.1.2"}, {"name": "multidict", "version": "6.4.3", "purl": "pkg:pypi/multidict@6.4.3", "type": "library", "bom-ref": "pkg:pypi/multidict@6.4.3"}, {"name": "multiprocess", "version": "0.70.15", "purl": "pkg:pypi/multiprocess@0.70.15", "type": "library", "bom-ref": "pkg:pypi/multiprocess@0.70.15"}, {"name": "<PERSON><PERSON>h", "version": "1.0.12", "purl": "pkg:pypi/murmurhash@1.0.12", "type": "library", "bom-ref": "pkg:pypi/murmurhash@1.0.12"}, {"name": "neo4j", "version": "5.28.1", "purl": "pkg:pypi/neo4j@5.28.1", "type": "library", "bom-ref": "pkg:pypi/neo4j@5.28.1"}, {"name": "numpy", "version": "1.26.4", "purl": "pkg:pypi/numpy@1.26.4", "type": "library", "bom-ref": "pkg:pypi/numpy@1.26.4"}, {"name": "openai", "version": "1.79.0", "purl": "pkg:pypi/openai@1.79.0", "type": "library", "bom-ref": "pkg:pypi/openai@1.79.0"}, {"name": "opentelemetry-api", "version": "1.33.1", "purl": "pkg:pypi/opentelemetry-api@1.33.1", "type": "library", "bom-ref": "pkg:pypi/opentelemetry-api@1.33.1"}, {"name": "optuna", "version": "4.3.0", "purl": "pkg:pypi/optuna@4.3.0", "type": "library", "bom-ref": "pkg:pypi/optuna@4.3.0"}, {"name": "packaging", "version": "23.2", "purl": "pkg:pypi/packaging@23.2", "type": "library", "bom-ref": "pkg:pypi/packaging@23.2"}, {"name": "pandas", "version": "2.2.3", "purl": "pkg:pypi/pandas@2.2.3", "type": "library", "bom-ref": "pkg:pypi/pandas@2.2.3"}, {"name": "pillow", "version": "11.2.1", "purl": "pkg:pypi/pillow@11.2.1", "type": "library", "bom-ref": "pkg:pypi/pillow@11.2.1"}, {"name": "pluggy", "version": "1.6.0", "purl": "pkg:pypi/pluggy@1.6.0", "type": "library", "bom-ref": "pkg:pypi/pluggy@1.6.0"}, {"name": "preshed", "version": "3.0.9", "purl": "pkg:pypi/preshed@3.0.9", "type": "library", "bom-ref": "pkg:pypi/preshed@3.0.9"}, {"name": "propcache", "version": "0.3.1", "purl": "pkg:pypi/propcache@0.3.1", "type": "library", "bom-ref": "pkg:pypi/propcache@0.3.1"}, {"name": "proto-plus", "version": "1.26.1", "purl": "pkg:pypi/proto-plus@1.26.1", "type": "library", "bom-ref": "pkg:pypi/proto-plus@1.26.1"}, {"name": "protobuf", "version": "5.29.4", "purl": "pkg:pypi/protobuf@5.29.4", "type": "library", "bom-ref": "pkg:pypi/protobuf@5.29.4"}, {"name": "psycopg2", "version": "2.9.10", "purl": "pkg:pypi/psycopg2@2.9.10", "type": "library", "bom-ref": "pkg:pypi/psycopg2@2.9.10"}, {"name": "p<PERSON><PERSON>", "version": "20.0.0", "purl": "pkg:pypi/pyarrow@20.0.0", "type": "library", "bom-ref": "pkg:pypi/pyarrow@20.0.0"}, {"name": "pyarrow-hotfix", "version": "0.7", "purl": "pkg:pypi/pyarrow-hotfix@0.7", "type": "library", "bom-ref": "pkg:pypi/pyarrow-hotfix@0.7"}, {"name": "pyasn1", "version": "0.6.1", "purl": "pkg:pypi/pyasn1@0.6.1", "type": "library", "bom-ref": "pkg:pypi/pyasn1@0.6.1"}, {"name": "pyasn1-modules", "version": "0.4.2", "purl": "pkg:pypi/pyasn1-modules@0.4.2", "type": "library", "bom-ref": "pkg:pypi/pyasn1-modules@0.4.2"}, {"name": "pyc<PERSON><PERSON>", "version": "2.22", "purl": "pkg:pypi/pycparser@2.22", "type": "library", "bom-ref": "pkg:pypi/pycparser@2.22"}, {"name": "pydantic", "version": "2.11.4", "purl": "pkg:pypi/pydantic@2.11.4", "type": "library", "bom-ref": "pkg:pypi/pydantic@2.11.4"}, {"name": "pydantic-core", "version": "2.33.2", "purl": "pkg:pypi/pydantic-core@2.33.2", "type": "library", "bom-ref": "pkg:pypi/pydantic-core@2.33.2"}, {"name": "pydantic-settings", "version": "2.6.0", "purl": "pkg:pypi/pydantic-settings@2.6.0", "type": "library", "bom-ref": "pkg:pypi/pydantic-settings@2.6.0"}, {"name": "pygments", "version": "2.19.1", "purl": "pkg:pypi/pygments@2.19.1", "type": "library", "bom-ref": "pkg:pypi/pygments@2.19.1"}, {"name": "pymupdf", "version": "1.25.5", "purl": "pkg:pypi/pymupdf@1.25.5", "type": "library", "bom-ref": "pkg:pypi/pymupdf@1.25.5"}, {"name": "pyparsing", "version": "3.2.3", "purl": "pkg:pypi/pyparsing@3.2.3", "type": "library", "bom-ref": "pkg:pypi/pyparsing@3.2.3"}, {"name": "pytest", "version": "8.3.5", "purl": "pkg:pypi/pytest@8.3.5", "type": "library", "bom-ref": "pkg:pypi/pytest@8.3.5"}, {"name": "pytest-asyncio", "version": "0.25.3", "purl": "pkg:pypi/pytest-asyncio@0.25.3", "type": "library", "bom-ref": "pkg:pypi/pytest-asyncio@0.25.3"}, {"name": "pytest-cov", "version": "6.2.1", "purl": "pkg:pypi/pytest-cov@6.2.1", "type": "library", "bom-ref": "pkg:pypi/pytest-cov@6.2.1"}, {"name": "pytest-mock", "version": "3.14.0", "purl": "pkg:pypi/pytest-mock@3.14.0", "type": "library", "bom-ref": "pkg:pypi/pytest-mock@3.14.0"}, {"name": "python-dateutil", "version": "2.9.0.post0", "purl": "pkg:pypi/python-dateutil@2.9.0.post0", "type": "library", "bom-ref": "pkg:pypi/python-dateutil@2.9.0.post0"}, {"name": "python-dotenv", "version": "1.1.0", "purl": "pkg:pypi/python-dotenv@1.1.0", "type": "library", "bom-ref": "pkg:pypi/python-dotenv@1.1.0"}, {"name": "pytz", "version": "2025.2", "purl": "pkg:pypi/pytz@2025.2", "type": "library", "bom-ref": "pkg:pypi/pytz@2025.2"}, {"name": "pyyaml", "version": "6.0.2", "purl": "pkg:pypi/pyyaml@6.0.2", "type": "library", "bom-ref": "pkg:pypi/pyyaml@6.0.2"}, {"name": "regex", "version": "2024.11.6", "purl": "pkg:pypi/regex@2024.11.6", "type": "library", "bom-ref": "pkg:pypi/regex@2024.11.6"}, {"name": "requests", "version": "2.32.3", "purl": "pkg:pypi/requests@2.32.3", "type": "library", "bom-ref": "pkg:pypi/requests@2.32.3"}, {"name": "rich", "version": "14.0.0", "purl": "pkg:pypi/rich@14.0.0", "type": "library", "bom-ref": "pkg:pypi/rich@14.0.0"}, {"name": "rsa", "version": "4.9.1", "purl": "pkg:pypi/rsa@4.9.1", "type": "library", "bom-ref": "pkg:pypi/rsa@4.9.1"}, {"name": "s3transfer", "version": "0.12.0", "purl": "pkg:pypi/s3transfer@0.12.0", "type": "library", "bom-ref": "pkg:pypi/s3transfer@0.12.0"}, {"name": "setuptools", "version": "80.7.1", "purl": "pkg:pypi/setuptools@80.7.1", "type": "library", "bom-ref": "pkg:pypi/setuptools@80.7.1"}, {"name": "shellingham", "version": "1.5.4", "purl": "pkg:pypi/shellingham@1.5.4", "type": "library", "bom-ref": "pkg:pypi/shellingham@1.5.4"}, {"name": "six", "version": "1.17.0", "purl": "pkg:pypi/six@1.17.0", "type": "library", "bom-ref": "pkg:pypi/six@1.17.0"}, {"name": "smart-open", "version": "7.1.0", "purl": "pkg:pypi/smart-open@7.1.0", "type": "library", "bom-ref": "pkg:pypi/smart-open@7.1.0"}, {"name": "sniffio", "version": "1.3.1", "purl": "pkg:pypi/sniffio@1.3.1", "type": "library", "bom-ref": "pkg:pypi/sniffio@1.3.1"}, {"name": "spacy", "version": "3.7.5", "purl": "pkg:pypi/spacy@3.7.5", "type": "library", "bom-ref": "pkg:pypi/spacy@3.7.5"}, {"name": "spacy-legacy", "version": "3.0.12", "purl": "pkg:pypi/spacy-legacy@3.0.12", "type": "library", "bom-ref": "pkg:pypi/spacy-legacy@3.0.12"}, {"name": "spacy-loggers", "version": "1.0.5", "purl": "pkg:pypi/spacy-loggers@1.0.5", "type": "library", "bom-ref": "pkg:pypi/spacy-loggers@1.0.5"}, {"name": "sqlalchemy", "version": "2.0.41", "purl": "pkg:pypi/sqlalchemy@2.0.41", "type": "library", "bom-ref": "pkg:pypi/sqlalchemy@2.0.41"}, {"name": "srsly", "version": "2.4.8", "purl": "pkg:pypi/srsly@2.4.8", "type": "library", "bom-ref": "pkg:pypi/srsly@2.4.8"}, {"name": "starlette", "version": "0.46.2", "purl": "pkg:pypi/starlette@0.46.2", "type": "library", "bom-ref": "pkg:pypi/starlette@0.46.2"}, {"name": "structlog", "version": "25.3.0", "purl": "pkg:pypi/structlog@25.3.0", "type": "library", "bom-ref": "pkg:pypi/structlog@25.3.0"}, {"name": "tenacity", "version": "9.0.0", "purl": "pkg:pypi/tenacity@9.0.0", "type": "library", "bom-ref": "pkg:pypi/tenacity@9.0.0"}, {"name": "thinc", "version": "8.2.5", "purl": "pkg:pypi/thinc@8.2.5", "type": "library", "bom-ref": "pkg:pypi/thinc@8.2.5"}, {"name": "to<PERSON>li", "version": "2.2.1", "purl": "pkg:pypi/tomli@2.2.1", "type": "library", "bom-ref": "pkg:pypi/tomli@2.2.1"}, {"name": "tqdm", "version": "4.67.1", "purl": "pkg:pypi/tqdm@4.67.1", "type": "library", "bom-ref": "pkg:pypi/tqdm@4.67.1"}, {"name": "typer", "version": "0.15.3", "purl": "pkg:pypi/typer@0.15.3", "type": "library", "bom-ref": "pkg:pypi/typer@0.15.3"}, {"name": "typing-extensions", "version": "4.13.2", "purl": "pkg:pypi/typing-extensions@4.13.2", "type": "library", "bom-ref": "pkg:pypi/typing-extensions@4.13.2"}, {"name": "typing-inspection", "version": "0.4.0", "purl": "pkg:pypi/typing-inspection@0.4.0", "type": "library", "bom-ref": "pkg:pypi/typing-inspection@0.4.0"}, {"name": "tzdata", "version": "2025.2", "purl": "pkg:pypi/tzdata@2025.2", "type": "library", "bom-ref": "pkg:pypi/tzdata@2025.2"}, {"name": "<PERSON><PERSON><PERSON>", "version": "5.10.0", "purl": "pkg:pypi/ujson@5.10.0", "type": "library", "bom-ref": "pkg:pypi/ujson@5.10.0"}, {"name": "uritemplate", "version": "4.1.1", "purl": "pkg:pypi/uritemplate@4.1.1", "type": "library", "bom-ref": "pkg:pypi/uritemplate@4.1.1"}, {"name": "urllib3", "version": "2.4.0", "purl": "pkg:pypi/urllib3@2.4.0", "type": "library", "bom-ref": "pkg:pypi/urllib3@2.4.0"}, {"name": "u<PERSON><PERSON>", "version": "0.34.2", "purl": "pkg:pypi/uvicorn@0.34.2", "type": "library", "bom-ref": "pkg:pypi/uvicorn@0.34.2"}, {"name": "validators", "version": "0.34.0", "purl": "pkg:pypi/validators@0.34.0", "type": "library", "bom-ref": "pkg:pypi/validators@0.34.0"}, {"name": "wasabi", "version": "1.1.3", "purl": "pkg:pypi/wasabi@1.1.3", "type": "library", "bom-ref": "pkg:pypi/wasabi@1.1.3"}, {"name": "weasel", "version": "0.4.1", "purl": "pkg:pypi/weasel@0.4.1", "type": "library", "bom-ref": "pkg:pypi/weasel@0.4.1"}, {"name": "weaviate-client", "version": "4.14.3", "purl": "pkg:pypi/weaviate-client@4.14.3", "type": "library", "bom-ref": "pkg:pypi/weaviate-client@4.14.3"}, {"name": "websockets", "version": "15.0.1", "purl": "pkg:pypi/websockets@15.0.1", "type": "library", "bom-ref": "pkg:pypi/websockets@15.0.1"}, {"name": "win32-setctime", "version": "1.2.0", "purl": "pkg:pypi/win32-setctime@1.2.0", "type": "library", "bom-ref": "pkg:pypi/win32-setctime@1.2.0"}, {"name": "wrapt", "version": "1.16.0", "purl": "pkg:pypi/wrapt@1.16.0", "type": "library", "bom-ref": "pkg:pypi/wrapt@1.16.0"}, {"name": "xxhash", "version": "3.5.0", "purl": "pkg:pypi/xxhash@3.5.0", "type": "library", "bom-ref": "pkg:pypi/xxhash@3.5.0"}, {"name": "yarl", "version": "1.20.0", "purl": "pkg:pypi/yarl@1.20.0", "type": "library", "bom-ref": "pkg:pypi/yarl@1.20.0"}, {"name": "zipp", "version": "3.21.0", "purl": "pkg:pypi/zipp@3.21.0", "type": "library", "bom-ref": "pkg:pypi/zipp@3.21.0"}]}