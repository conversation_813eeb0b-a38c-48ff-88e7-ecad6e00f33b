import os
import warnings
from contextlib import asynccontextmanager

import uvicorn

from comprehend import process_transcript, process_referral, process_link, process_medication, process_qa, \
    process_care_plan, process_sandbox, process_generate, process_compliance, process_correction
from comprehend.utils.monitor import setup_logger
from services.aws.sqs import SQSConsumer, start_consumer, stop_consumer

warnings.filterwarnings("ignore")

import certifi
from loguru import logger
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exception_handlers import RequestValidationError
from fastapi.responses import JSONResponse

os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()

from config import settings
from packages.langfuse import Langfuse
from services.models import ProcessRecording, MedicationTask, LinkTask, ProcessReferral, QATask, ProcessCarePlan, \
    ProcessSandbox, ProcessGenerate, ProcessCompliance, ProcessCorrectionRecording

setup_logger()
langfuse = Langfuse(public_key=settings.LANGFUSE_PUBLIC_KEY, secret_key=settings.LANGFUSE_SECRET_KEY)
consumer = SQSConsumer(queue_url=settings.IN_QUEUE)


@asynccontextmanager
async def lifespan(_: FastAPI):
    # Startup
    logger.info('Server Started')
    start_consumer(consumer)
    yield
    # Shutdown
    logger.info('Shutting Down...')
    langfuse.flush()
    stop_consumer(consumer)


# noinspection PyTypeChecker
app = FastAPI(lifespan=lifespan)
# noinspection PyTypeChecker
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)


@app.get("/health")
def health():
    return {"status": "ok"}


@app.post("/medication")
def medication(body: MedicationTask):
    return process_medication(body)


@app.post("/process")
async def transcript(body: ProcessRecording):
    answers, meta = await process_transcript(body)
    return {"answers": answers, "meta": meta, "pipeline": "transcript"}


@app.post("/sandbox")
async def sandbox(body: ProcessSandbox):
    answers = await process_sandbox(body)
    return answers


@app.post("/generate")
async def generate(body: ProcessGenerate):
    answers = await process_generate(body)
    return answers


@app.post("/referral")
async def referral(body: ProcessReferral):
    return await process_referral(body)


@app.post("/compliance")
async def compliance(body: ProcessCompliance):
    return await process_compliance(body)


@app.post("/care_plan")
async def care_plan(body: ProcessCarePlan):
    return await process_care_plan(body)


@app.post("/link")
async def link(body: LinkTask):
    return await process_link(body)


@app.post("/qa")
async def qa(body: QATask):
    return await process_qa(body)


@app.post("/correction")
async def transcript(body: ProcessCorrectionRecording):
    answers, meta = await process_correction(body)
    return {"answers": answers, "meta": meta, "pipeline": "transcript"}


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors(), "body": exc.body},
    )


async def test_def():
    import json
    import uuid
#
#     from comprehend import generate_pipeline
#
#     with open("raw.json", "r") as f:
#         raw = json.load(f)
#
#     res = await generate_pipeline(
#         ctx={'agency_id': 'v_674744a1b1de0342fa42fb9c'},
#         id=str(uuid.uuid4().hex),
#         message="Add the patients height to the schema",
#         data=raw,
#         context={})
#
#     from comprehend import medication_pipeline
#     res = medication_pipeline(image_urls=[
#         'ag_6717e0b127609d2152dfdcac/medication/67f698605ee14d46c4adddea-48456441-25dc-4f10-b65a-0db84756eec1.jpg'
#     ])
#
#     from comprehend.pipelines.linking import linking_pipeline
#     # res = await process_qa(QATask(visit_id="v_6723dc25b4bbc436d5a3c483"))
#     res = await linking_pipeline(
#         ctx={'agency_id': 'v_674744a1b1de0342fa42fb9c'},
#         id="419899",
#         visit_type="FUC",
#         # visit_type="SOC",
#         visit_id="v_674744a1b1de0342fa42fb9c",
#         episode_id="ep_674744a180869734dec3fd9b",
#         snippets=[
#             # 'Patients name is John Keller, DOB 01/01/1990',
#             # 'gender is female',
#             '98 degrees Fahrenheit for temperature'
#         ],
#         rationale="The clinician states the patient's temperature is 98 degrees Fahrenheit, which is a normal body temperature and an essential vital sign for assessing the patient's overall health."
#     )
#
#     from comprehend.pipelines.referral_ import referral_pipeline
#     res = await referral_pipeline(
#         ctx={'agency_id': 'v_674744a1b1de0342fa42fb9c'},
#         document_refs=[
#             "tmp/Donald Duck Referral.pdf"
#         ],
#         source="local",
#         run_id="419899")
#
#     from services.deepgram.transcribe import transcribe
#     trx = await transcribe(
#         recording_file='./recording.mp3'
#     )
#     with open("transcript.json", "w") as f:
#         f.write(json.dumps(trx, indent=2))
#
    from comprehend.pipelines.corrections import corrections_pipeline
    with open("temp/transcript.json", "r") as f:
        transcript = json.load(f)

    res = await corrections_pipeline(
        ctx={"agency_id": "ag_672919f8e2e01a271b193ac5"},
        transcript=transcript.get('transcript'),
        code="82RS799D",
        visit_id="v_68b6e18ddfb64ea799964b71",
        visit_type="SOC")

    with open("test.json", "w") as f:
        f.write(json.dumps(res, indent=2))


if __name__ == "__main__":
    import asyncio
    asyncio.run(test_def())

# if __name__ == "__main__":
#     uvicorn.run(app, host=settings.HOST, port=settings.PORT, workers=1)
