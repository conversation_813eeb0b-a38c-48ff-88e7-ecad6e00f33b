build_commands: [ ]
environment_variables: { }
model_metadata:
  example_model_input:
    input: text strings
model_name: MedEmbed
python_version: py310
requirements:
  - einops==0.8.1
  - sentence-transformers==3.4.1
resources:
  accelerator: T4
  use_gpu: true
secrets: { }
system_packages: [ ]
#trt_llm:
#  build:
#    base_model: encoder
#    checkpoint_repository:
#      repo: abhinand/MedEmbed-large-v0.1
#      revision: main
#      source: HF
#    max_num_tokens: 16384
#    max_seq_len: 1000001
