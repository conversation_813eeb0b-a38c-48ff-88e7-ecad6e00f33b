from sentence_transformers import SentenceTransformer


class Model:
    def __init__(self, **kwargs) -> None:
        self._secrets = kwargs["secrets"]
        self._model: SentenceTransformer = None

    def load(self):
        # self._model = SentenceTransformer("nomic-ai/nomic-embed-text-v2-moe", trust_remote_code=True)
        self._model = SentenceTransformer("abhinand/MedEmbed-large-v0.1")

    def predict(self, model_input):
        if self._model is None:
            self.load()
        inputs = model_input.get('input')
        # print("Processing ::", inputs)
        embeddings = self._model.encode(inputs)
        # print("Embeddings ::", embeddings.shape)
        return embeddings
