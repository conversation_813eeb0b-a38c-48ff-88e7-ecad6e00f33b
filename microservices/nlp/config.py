import os
from functools import lru_cache
from typing import Optional, Any

from dotenv import load_dotenv
from pydantic import PostgresDsn, field_validator, Field
from pydantic_core.core_schema import ValidationInfo
from pydantic_settings import BaseSettings, SettingsConfigDict

load_dotenv()

PATRIUM_ENV = os.environ.get("PATRIUM_ENV", "local")
CI = os.environ.get("CI", False)

class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=f"config/.env.{PATRIUM_ENV}",
        env_file_encoding="utf-8",
        extra="ignore"
    )

    # SERVER

    HOST: str = Field(default="0.0.0.0")
    PORT: int = Field(default=3200)
    PATRIUM_ENV: str = Field(default=PATRIUM_ENV)
    CI: bool = Field(default=CI)

    @property
    def ENV(self) -> str:
        return 'production' if self.PATRIUM_ENV == 'prod' else 'development'

    # MODEL SETTINGS

    BASETEN_API_KEY: str = Field(default=None)
    BASETEN_EMBEDDING_URL: str = Field(default=None)

    RUNPOD_API_KEY: str = Field(default=None)
    RUNPOD_EMBEDDING_URL: str = Field(default=None)

    GEMINI_API_KEY: str = Field(default=None)
    OPENAI_API_KEY: str = Field(default=None)
    CEREBRAS_API_KEY: str = Field(default=None)
    DEEPGRAM_API_KEY: str = Field(default=None)
    ANTHROPIC_API_KEY: str = Field(default=None)
    FIREWORKS_API_KEY: str = Field(default=None)
    OPENAI_API_BASE: str = "https://api.openai.com/v1/"
    CEREBRAS_API_BASE: str = "https://api.cerebras.ai/v1/"
    FIREWORKS_API_BASE: str = "https://api.fireworks.ai/inference/v1/"

    # QUEUES

    IN_QUEUE: str = Field(default=None)
    OUT_QUEUE: str = Field(default=None)

    # SERVICES

    DIRECTUS_ENDPOINT: str = "https://cms.patrium.health"
    DIRECTUS_TOKEN: str = "flXBcPLBdLbuPjHMGZD0KMvl3cA_yra6"

    AWS_ACCESS_KEY_ID: str = Field(default=None)
    AWS_SECRET_ACCESS_KEY: str = Field(default=None)
    AWS_REGION: str = "us-east-1"
    AWS_S3_BUCKET: str = Field(default=None)

    WEAVIATE_HOST: str = Field(default=None)
    WEAVIATE_PORT: int = Field(default=9010)

    GRAPH_HOST: str = Field(default="bolt://localhost:7687")
    GRAPH_USER: str = Field(default="neo4j")
    GRAPH_PASSWORD: str = Field(default="n30f9.p@55")

    LANGFUSE_HOST: str = Field(default="http://localhost:3333")
    LANGFUSE_PUBLIC_KEY: str = Field(default="pk-lf-397aee4d-ebf4-489f-9e43-4d66616a0daf")
    LANGFUSE_SECRET_KEY: str = Field(default="******************************************")

    # GENERAL

    CONCURRENCY: int = 26
    MAX_RETRIES: int = 3
    RETRY_WAIT_SECS: int = 5
    MAX_TOKENS: int = 8192
    TEMPERATURE: int = 0.1
    NAMESPACE_URL: str = '8ae7fc11-f080-4dd5-b1d0-9a492f01aab4'

    # DATABASE

    DATABASE_HOST: str = Field(default="localhost")
    DATABASE_PORT: int = 5432
    DATABASE_NAME: str = "patrium"
    DATABASE_USER: str = "postgres"
    DATABASE_PASSWORD: str = Field(default="")
    DATABASE_URL: Optional[PostgresDsn] = Field(default=None)

    @field_validator("DATABASE_URL", mode="before")
    def build_postgres_db_url(cls, v: Optional[str], values: ValidationInfo) -> Any:
        if isinstance(v, str):
            return v

        return PostgresDsn.build(
            scheme="postgresql",
            host=values.data.get("DATABASE_HOST"),
            port=values.data.get("DATABASE_PORT"),
            username=values.data.get("DATABASE_USER"),
            password=values.data.get("DATABASE_PASSWORD"),
            path=values.data.get('DATABASE_NAME'),
        )


@lru_cache
def get_app_settings() -> Settings:
    return Settings()


settings = get_app_settings()
