# Patrium NLP

## Requirements to run the project for dev
1. Python 3.12.3 (Latest version to support `srsly`). Installable using `pyenv`. 
    Steps:
        - pyenv install 3.12.3
        - pyenv local 3.12.3
        - poetry env use 3.12.3
2. Install dependencies: `poetry install --verbose --no-root`
3. Create a `.env` file by `cp .env.example .env` and then copy `cp config/.env.dev config/.env.local` and make any edits to account for a local setting
4. Langfuse should be running as part of the base docker installation, so open that, make a project and set up `LANGFUSE_PUBLIC_KEY` & `LANGFUSE_SECRET_KEY`
5. Run the project: `poetry run python main.py`

## Unit Tests

```shell
poetry run pytest --cov=comprehend --cov-report=html --cov-report=term tests/
```