[tool.poetry]
name = "nlp"
version = "0.1.0"
description = ""
authors = ["mecolela <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
requests = "^2.32.3"
loguru = ">=0.7.2"
boto3 = ">=1.26.94"
python-dotenv = "^1.0.1"
fastapi = ">=0.70.1"
uvicorn = ">=0.16"
pydantic = ">=2.7.2"
numpy = "1.26.4"
deepgram-sdk = "2.12.0"
anthropic = ">=0.39.0"
openai = ">=1.61.1"
pydantic-settings = "2.6.0"
spacy = ">=3.7.2"
en-core-web-sm = { url = "https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.7.1/en_core_web_sm-3.7.1-py3-none-any.whl" }
dspy-ai = "2.4.9"
weaviate-client = "4.14.3"
certifi = "2025.4.26"
httpx = "0.28.1"
backoff = "2.2.1"
wrapt = "1.16.0"
packaging = "23.2"
anyio = "4.9.0"
psycopg2 = "^2.9.10"
google-cloud-logging = "^3.11.3"
google-generativeai = "^0.8.5"
amazon-sqs-extended-client = "^1.0.1"
pymupdf = "^1.25.1"
json-repair = "^0.44.1"
pillow = "^11.1.0"
graphiti-core = "^0.6.0"
google-genai = "^1.13.0"
dictdiffer = "^0.9.0"
pytest-cov = "^6.2.1"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.4"
pytest-mock = "^3.14.0"
pytest-asyncio = "^0.25.3"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
