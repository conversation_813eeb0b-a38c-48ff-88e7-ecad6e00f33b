{"openapi": "3.0.3", "info": {"title": "Field Schema API", "version": "1.0.0", "description": "API specification for Field Schema"}, "paths": {}, "components": {"schemas": {"Field": {"type": "object", "required": ["id", "name", "type", "inputType", "model"], "properties": {"id": {"type": "string", "description": "Unique identifier for the field, should be uppercase and underscored", "example": "PATIENT_DETAILS"}, "name": {"type": "string", "description": "Display name for the field", "example": "Patient Details"}, "placeholder": {"type": "string", "description": "Placeholder text for the input field"}, "type": {"type": "string", "description": "Defines the field type", "enum": ["input"]}, "inputType": {"type": "string", "description": "Specifies the input type", "enum": ["json", "text", "number", "float", "select", "multiselect", "date", "calculation", "section"]}, "mandatory": {"type": "boolean", "description": "Indicates whether the field is required", "default": false}, "model": {"type": "object", "description": "Contains instructions for the model when answering this question", "properties": {"question": {"type": "string", "description": "The question to be answered"}, "criteria": {"type": "string", "description": "Detailed outline for how the question should be answered; accounts for the input type, list of select options or schema"}, "notes": {"type": "string", "description": "Additional stylistic and reasoning notes for the model"}, "useValue": {"type": "boolean", "description": "select or multiselect inputs only - flag for whether the model should use the value or the label for the answer", "default": false}, "examples": {"type": "array", "description": "Example contexts / snippets, rationales, and answers", "items": {"type": "object", "properties": {"context": {"type": "array", "description": "Array of transcript snippets / data pointes that are relevant to the question", "items": {"type": "string"}}, "rationale": {"type": "string", "description": "Example rationale for how the model should answer the question based on question, criteria and notes"}, "answer": {"type": "string", "description": "Example answer for the question based on criteria, if input type === \"json\" then the answer should be a json string that matches the schema"}}}}, "enabled": {"type": "boolean", "description": "Indicates if the field is active", "default": true}, "sources": {"type": "array", "description": "Relevant data sources (other fields) that the model should consider when answering the question", "items": {"$ref": "#/components/schemas/FieldSourceRule"}}, "directSource": {"type": "string", "description": "mustache template string for directly sourcing the answer from the patient or episode object", "example": "{{patient.first_name}} {{patient.last_name}}"}}}, "options": {"$ref": "#/components/schemas/Options"}, "conditionals": {"$ref": "#/components/schemas/Conditionals"}, "calculation": {"type": "object", "description": "Contains calculation logic for if inputType == \"calculation\"", "properties": {"items": {"type": "array", "description": "Array of items to calculate", "example": ["A", "B"], "items": {"type": "string"}}, "formula": {"type": "string", "description": "Calculation formula", "example": "A + B"}}}, "schema": {"type": "object", "description": "Defines the target structure of the data", "properties": {"type": {"type": "string", "enum": ["array", "object"]}, "items": {"type": "object", "description": "Defines the structure of array items if type === \"array\"", "properties": {"type": {"type": "string", "description": "Type of items in the array", "enum": ["string", "integer", "float", "date-time", "object", "boolean"]}, "properties": {"type": "array", "description": "if items.type === \"object\" then the properties of the object", "items": {"$ref": "#/components/schemas/SchemaProperty"}}}}, "properties": {"type": "array", "description": "if type === \"object\" then the properties of the object", "items": {"$ref": "#/components/schemas/SchemaProperty"}}}}, "metadata": {"type": "object", "description": "Additional metadata for the field"}}}, "SchemaProperty": {"type": "object", "properties": {"name": {"type": "string", "description": "Property identifier"}, "label": {"type": "string", "description": "Display label"}, "type": {"type": "string", "description": "Property type", "enum": ["string", "integer", "float", "select", "multiselect", "object", "array", "date-time", "boolean"]}, "required": {"type": "boolean", "description": "Indicates if required"}, "params": {"$ref": "#/components/schemas/PropertyParams"}}, "required": ["name", "type"]}, "PropertyParams": {"type": "object", "description": "Same structure as the root with specific fields", "properties": {"placeholder": {"type": "string", "description": "Placeholder text for the input field"}, "readonly": {"type": "boolean", "default": false}, "model": {"$ref": "#/components/schemas/Field/properties/model"}, "conditionals": {"$ref": "#/components/schemas/Conditionals"}, "calculation": {"$ref": "#/components/schemas/Field/properties/calculation"}, "options": {"$ref": "#/components/schemas/Options"}, "schema": {"$ref": "#/components/schemas/Field/properties/schema"}}}, "Conditionals": {"type": "object", "description": "Contains logic for conditional display of the field to the clinician", "properties": {"conditions": {"type": "array", "description": "Array of conditions", "items": {"type": "object", "properties": {"fieldId": {"type": "string"}, "operand": {"type": "string", "enum": ["equals", "not equals", "is null", "not null", "contains"]}, "value": {"type": "string"}}, "required": ["fieldId", "operand"]}}, "order": {"type": "string", "description": "Condition evaluation order", "example": "1 AND 2"}}}, "Options": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string"}, "label": {"type": "string"}, "value": {"type": "string"}}, "required": ["key", "label", "value"]}}, "FieldSourceRule": {"oneOf": [{"$ref": "#/components/schemas/EpisodeSourceRule"}, {"$ref": "#/components/schemas/VisitSourceRule"}]}, "EpisodeSourceRule": {"type": "object", "required": ["level", "fields"], "properties": {"level": {"type": "string", "enum": ["episode"], "description": "Episode/referral level data source"}, "fields": {"type": "array", "description": "Fields to extract from this source", "items": {"$ref": "#/components/schemas/FieldReference"}, "minItems": 1}}, "additionalProperties": false}, "VisitSourceRule": {"type": "object", "required": ["level", "visit", "fields"], "properties": {"level": {"type": "string", "enum": ["visit"], "description": "Visit level data source"}, "visit": {"type": "string", "enum": ["current", "previous", "nth", "timeWindow"], "description": "Visit selection type"}, "direction": {"type": "string", "enum": ["first", "last"], "description": "Direction for visit selection (required for 'previous' and 'nth' visit types)"}, "nthIndex": {"type": "integer", "minimum": 1, "maximum": 10, "description": "Visit number (required for 'nth' visit type)"}, "fields": {"type": "array", "description": "Fields to extract from this source", "items": {"$ref": "#/components/schemas/FieldReference"}, "minItems": 1}, "filters": {"type": "array", "description": "Filters to apply when selecting visits", "items": {"$ref": "#/components/schemas/VisitFilter"}}}, "additionalProperties": false}, "FieldReference": {"type": "object", "required": ["code", "source"], "properties": {"code": {"type": "string", "description": "The field code identifier"}, "source": {"type": "string", "enum": ["referral", "episode", "task", "tasks"], "description": "The source system containing this field"}, "source_key": {"type": "string", "description": "Optional source-specific key for the field"}}}, "VisitFilter": {"type": "object", "description": "Filter to apply when selecting visits", "additionalProperties": false, "minProperties": 1, "maxProperties": 1, "properties": {"visit_type": {"type": "string", "enum": ["SOC", "FUC", "DC", "DAH", "REC"], "description": "Filter by visit type"}, "visit_discipline": {"type": "string", "enum": ["RN", "PT", "OT", "ST/SLP"], "description": "Filter by visit discipline"}, "visit_date": {"oneOf": [{"type": "string", "format": "date", "description": "Absolute date filter"}, {"$ref": "#/components/schemas/RelativeTimeFilter"}]}}}, "RelativeTimeFilter": {"type": "object", "required": ["number", "unit"], "properties": {"number": {"type": "integer", "minimum": 1, "description": "Number of time units"}, "unit": {"type": "string", "enum": ["days", "weeks", "months"], "description": "Time unit"}}}}}}