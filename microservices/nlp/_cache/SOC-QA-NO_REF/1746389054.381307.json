[{"code": "QA_Race_Ethnicity", "name": "QA_Race & Ethnicity", "id": 8254, "types": ["SOC_CHECKLIST"], "sections": [85], "source": "tasks", "source_id": "207", "schema": {}, "mandatory": false, "notes": "Compare:\n\nSOC A1005 (Multiselect) vs. Ref ethnicity (Multiselect): Selections should match exactly.\n\nSOC A1010 (Select) vs. Ref race (Multiselect): The SOC A1010 value should be present within the Ref race values.\nRate '0' only for confirmed mismatches in available data.", "enabled": true, "question": "Review the patient's Ethnicity (A1005) and Race (A1010) entered in the SOC 'Race & Ethnicity' section. Compare these against the corresponding available information (ethnicity, race) in the 'Demographics_REFERRAL' field from the referral documentation. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if both the SOC Ethnicity (A1005) and Race (A1010) selections are consistent with the available Referral ethnicity and race data, OR if referral data is missing preventing comparison, but no mismatches are found in the available data. (See Notes for consistency definition).\n\nAssign '0' (Incorrect/Issue) if there is at least one mismatch between the SOC selections and the corresponding available referral data.\n\nreason (Text String):\n\nIf rating is '1', state that Race and Ethnicity are consistent with available referral data and explicitly note any fields missing from the referral (e.g., \"Race and Ethnicity consistent with referral data.\", \"Race consistent, Ethnicity missing in referral.\").\n\nIf rating is '0', state the specific field(s) with discrepancies (e.g., 'Race mismatch', 'Ethnicity mismatch').\n\nalt_rationale (Text String): If rating is '0' due to a discrepancy, explain the correct information based on the referral documentation (e.g., 'Referral Demographics lists Ethnicity as B (Mexican).', 'Referral race is listed as A (White) and K (Native Hawaiian).'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', provide the expected JSON snippet for the specific field(s) identified as discrepant in the alt_rationale based on referral data (e.g., '{ \"a1005\": \"B\" }', '{ \"a1010\": \"A\" }' - Note: Address how to handle multiselect referral race if SOC is single select). Leave blank if rating is '1'.", "examples": [{"context": ["SOC: { \"a1005\": \"A\", \"a1010\": \"A\" } (Ethnicity: Non-Hispanic, Race: White)", "Referral Demographics: { ..., \"ethnicity\": \"A\", \"race\": \"A\", ... }"], "rationale": "Both Ethnicity and Race match the referral data.", "answer": "{ \"rating\": \"1\", \"reason\": \"Race and Ethnicity match referral data.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC: { \"a1005\": \"A\", \"a1010\": \"B\" } (Race: Black/African American)", "Referral Demographics: { ..., \"ethnicity\": \"A\", \"race\": \"A\", ... } (Race: White)"], "rationale": "Race (A1010) mismatches the referral race. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Race (A1010) mismatch.\", \"alt_rationale\": \"Referral Demographics lists race as A (White).\", \"alt_answer\": \"{ \\\\\"a1010\\\\\": \\\\\"A\\\\\" }\" }"}], "options": [], "description": "Ask the patient or caregiver which category most closely matches the patient's ethnicity.  Verbalize all that apply:     \n Is the patient of Hispanic, Latino/a, or Spanish origin?     \n A  - No, not of Hispanic, Latino/a, or Spanish origin     \n B  - Yes, Mexican, Mexican American, Chicano     \n C  - Yes, Puerto Rican     \n D  - Yes, Cuban     \n E  - Yes, Another Hispanic, Latino, or Spanish origin     \n X  - Patient unable to respond     \n Y  - Patient declines to respond", "dependencies": [{"code": "Demographics_REFERRAL", "source": "referral", "name": "Demographics", "question": "Extract and structure the patient's demographic information (e.g., name, date of birth, gender, race, ethnicity, language, identification numbers) found within the provided referral documentation.", "description": "Demographics"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_VISIT_DETAILS", "name": "QA_Visit Details", "id": 8252, "types": ["SOC_CHECKLIST"], "sections": [85], "source": "tasks", "source_id": "222", "schema": {}, "mandatory": false, "notes": "heck for mismatches:\n\nM0104 vs Ref date_of_referral\n\nM0102 vs Ref date_of_physician_ordered_soc\n\nM0030 timing: Must be ON or AFTER M0102 (if M0102 exists).\n\nM0080 Discipline: Must be among Ref services_ordered.\n\nM0100 Reason: Should be '1' (SOC) if NO recent inpatient discharge, OR '3' (ROC) if recent inpatient discharge exists.\nRate '0' for any confirmed mismatch based on available data.", "enabled": true, "question": "Review the SOC 'Visit Details' (M0104, M0102, M0030, M0080, M0100, M0090). Compare M0104/M0102 against available referral dates. Assess consistency of M0030 timing (vs M0102), M0080 (Discipline vs ordered services), and M0100 (Reason vs inpatient history) using data from 'Referral Overview' and 'Recent Acute Care Summary'. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if no discrepancies are found between SOC fields (M0104, M0102, M0030, M0080, M0100) and their corresponding available referral context (dates, ordered services, inpatient history), as outlined in the Notes.\n\nAssign '0' (Incorrect/Issue) if at least one discrepancy is found in the comparisons mentioned above, based on available data.\n\nreason (Text String):\n\nIf rating is '1', state that fields are consistent with available referral data and explicitly note any key referral data points that were missing for comparison (e.g., \"All fields consistent with available referral data. M0102 referral date missing.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'M0102 mismatch.', 'M0030 SOC Date is before M0102.', 'M0080 Discipline not included in ordered services.', 'M0100 Reason inconsistent with inpatient history.').\n\nalt_rationale (Text String): If rating is '0', explain the correct information or expected value based on the referral documentation for the first or most significant discrepancy noted (e.g., 'Referral Overview lists date_of_physician_ordered_soc as 03/05/2025.', 'Referral ordered SN only.', 'Recent discharge indicates M0100 should be ROC (3).'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', provide the expected JSON snippet for the specific field(s) identified as discrepant in the alt_rationale based on referral data (e.g., '{ \"m0102\": \"03/05/2025\" }', '{ \"m0080\": \"1\" }', '{ \"m0100\": \"3\" }'). Combine if multiple simple corrections apply (e.g., '{ \"m0104\": \"03/01/2025\", \"m0102\": \"03/05/2025\" }'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC: { \"m0104\": \"03/01/25\", \"m0102\": \"03/05/25\", \"m0030\": \"03/06/25\", \"m0080\": \"1\", \"m0100\": \"1\", ... }", "Referral Overview: { \"date_of_referral\": \"03/01/2025\", \"date_of_physician_ordered_soc\": \"03/05/2025\", \"services_ordered\": \"SN, PT\", ... }", "Recent Acute Care Summary: {}"], "rationale": " Dates match. M0030 is after M0102. M0080 (RN=1) is in services ordered. M0100 (SOC=1) is correct as no recent discharge. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"All fields consistent with available referral data.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC: { \"m0104\": \"03/01/25\", \"m0102\": \"03/04/25\", ... }", "Referral Overview: { \"date_of_referral\": \"03/01/2025\", \"date_of_physician_ordered_soc\": \"03/05/2025\", ... }"], "rationale": " M0102 mismatches. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"M0102 mismatch.\", \"alt_rationale\": \"Referral Overview lists date_of_physician_ordered_soc as 03/05/2025.\", \"alt_answer\": \"{ \\\\\"m0102\\\\\": \\\\\"03/05/2025\\\\\" }\" }"}, {"context": ["SOC: { ..., \"m0102\": \"03/05/25\", \"m0030\": \"03/04/25\", ... }", "Referral Overview: { ..., \"date_of_physician_ordered_soc\": \"03/05/2025\", ... }"], "rationale": "M0030 SOC Date is before M0102 Physician Order Date. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"M0030 SOC Date is before M0102 Physician Order Date.\", \"alt_rationale\": \"SOC Date (M0030) must be on or after the Physician Order Date (M0102), which is 03/05/2025.\", \"alt_answer\": \"{ \\\\\"m0030\\\\\": \\\\\"Review and correct SOC date\\\\\" }\" }"}], "options": [], "description": "Confirm the date that the written or verbal referral for initiation of care was received by the HHA.", "dependencies": [{"code": "Referral_Overview_REFERRAL", "source": "referral", "name": "Referral Overview", "question": " Extract key overview information about the home health referral, including primary diagnosis, source details, referral date, reason for referral, services ordered, and physician-ordered start date, from the provided referral documentation. Synthesize a concise narrative summary combining these key elements.", "description": "Referral Overview"}, {"code": "Recent_Acute_Care_Summary_REFERRAL", "source": "referral", "name": "Recent Acute Care Summary", "question": "Extract key details about the patient's most recent acute care episode (e.g., hospitalization, SNF stay, ER visit, significant procedure) that is relevant to the current home health referral, as documented in the referral materials.", "description": "Recent Acute Care Summary"}, {"code": "ELIGIBILITY_DOCUMENTATION_REFERRAL", "source": "referral", "name": "Eligibility Documentation", "question": "Extract details regarding the required Face-to-Face (F2F) encounter documentation (including the performing provider, note content, reason/relation linkage, signature, and timeframe) and the Homebound status justification (criteria met and summary), as found within the provided referral documentation.", "description": "Eligibility Documentation"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_M0080", "name": "Discipline of Person Completing Assessment", "id": 1969, "types": ["SOC_CHECKLIST"], "sections": [85], "source": "tasks", "source_id": "219", "schema": {}, "mandatory": false, "notes": "To verify the discipline of the person completing the assessment, we should check against the ordered disciplines and skilled needs that justified the assessment, as well as documentation of nursing orders since SOC/ROC assessments are typically completed by nurses", "enabled": true, "question": "Based on the OASIS E item code M0080; What is the discipline of the person completing the assessment?", "inputType": "text", "criteria": "1. Confirm that the discipline recorded matches an ordered discipline for the patient\n2. For SOC/ROC assessments, verify it was completed by a registered nurse unless therapy-only case\n3. Cross-reference with skilled needs documentation to ensure appropriate discipline completed assessment based on patient's primary needs\n4. If therapy-only case, verify therapist discipline matches ordered therapy services", "examples": [], "options": [], "description": "State your discipline to confirm     \n     \n 1  - RN     \n 2  - PT     \n 3  - SLP/ST     \n 4  - OT", "dependencies": [{"code": "NURSING_ORDERS", "source": "referral"}, {"code": "PT_ORDERS", "source": "referral"}, {"code": "OT_ORDERS", "source": "referral"}, {"code": "ST_ORDERS", "source": "referral"}, {"code": "MSW_ORDERS", "source": "referral"}, {"code": "HHA_ORDERS", "source": "referral"}, {"code": "SKLD_NEED", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GG0170F1", "name": "Toilet Transfer", "id": 2631, "types": ["SOC_CHECKLIST"], "sections": [35], "source": "tasks", "source_id": "315", "schema": {}, "mandatory": false, "notes": "To QA toilet transfer ability, we should verify consistency between current and discharge goal performance, toilet-related functional assessments, bathroom safety factors, and overall mobility/transfer capabilities. We should also consider continence status as it relates to toilet use frequency.", "enabled": true, "question": "Based on the OASIS E item code GG0170F1; Does the patient have the ability to get on and off a toilet or commode?", "inputType": "text", "criteria": "1. Compare GG0170F1 (current) and GG0170F2 (goal) scores to ensure goal shows same or improved function\n2. Verify toilet transfer ability aligns with other transfer abilities (GG0170E1/E2 bed/chair transfers)\n3. Check that toilet transfer score matches overall functional mobility status\n4. Verify score makes sense given bathroom safety features/barriers noted\n5. Consider if score aligns with documented continence status and frequency of toilet use needed\n6. Ensure documented balance and musculoskeletal status support the assessed transfer ability", "examples": [], "options": [], "description": "The ability to get on and off a toilet or commode.", "dependencies": [{"code": "M1840", "source": "tasks"}, {"code": "M1845", "source": "tasks"}, {"code": "GG0170E1", "source": "tasks"}, {"code": "GG0170E2", "source": "tasks"}, {"code": "HOME_BATHROOM_BARS_SHOWER", "source": "tasks"}, {"code": "HOME_BATHROOM_SLIPPERY_FOOTING", "source": "tasks"}, {"code": "M1610", "source": "tasks"}, {"code": "M1620", "source": "tasks"}, {"code": "MUSCULOSKELETAL_BALANCE", "source": "tasks"}, {"code": "MUSCULOSKELETAL_STATUS", "source": "tasks"}, {"code": "TOI_TRANSF", "source": "tasks"}, {"code": "FUNCTIONAL_STATUS", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_PAYMENT_IDs", "name": "QA_Payment Sources & IDs", "id": 8253, "types": ["SOC_CHECKLIST"], "sections": [85], "source": "tasks", "source_id": "220", "schema": {}, "mandatory": false, "notes": "Compare:\n\nSOC M0150 (ensure it includes Ref payment_sources) vs. Ref payment_sources\n\nSOC M0063 vs. Ref medicare_number (or Ref primary_insurance_information.policy_number if payer=Medicare)\n\nSOC M0065 vs. Ref medicaid_number (or Ref primary_insurance_information.policy_number if payer=Medicaid)\n\nSOC M0064 vs. Ref ssn\nRate '0' only for confirmed mismatches in available data. Mismatch on M0150 means the referral primary source is not selected in the SOC M0150 list.", "enabled": true, "question": "Review the SOC 'Payment Sources & IDs' section (M0150, M0063, M0065, M0064/SSN). Compare M0150 (Payment Sources), M0063 (Medicare #), M0065 (Medicaid #), and M0064 (SSN) against the corresponding available information in the 'Financial Information_REFERRAL' (payment sources, medicare #, medicaid #, primary insurance policy #) and 'Demographics_REFERRAL' (ssn) fields from the referral documentation. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all comparable details (Primary Payment Source from M0150 vs. Referral payment_sources, M0063 vs. Ref medicare_number/Policy#, M0065 vs. Ref medicaid_number/Policy#, M0064 vs. Ref ssn) match the available referral data, OR if referral data is missing for certain points preventing comparison, but no mismatches are found in the available data.\n\nAssign '0' (Incorrect/Issue) if at least one mismatch is found between a SOC data point and the corresponding available referral data point (see Notes for specific comparisons).\n\nreason (Text String):\n\nIf rating is '1', state which pieces of information are consistent and explicitly note any key referral data that was missing for comparison (e.g., \"M0150 primary source, M0063 Medicare # match referral. Medicaid # missing in referral.\", \"All details consistent.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'M0150 primary source mismatch', 'M0063 Medicare # mismatch', 'M0065 Medicaid # mismatch', 'M0064 SSN mismatch').\n\nalt_rationale (Text String): If rating is '0' due to a discrepancy, explain the correct information based on the referral documentation for the first or most significant discrepancy noted (e.g., 'Referral Financial Info lists payment_sources as Medicare FFS (2).', 'Referral lists medicare_number as 1AB2-C3D-EFG4.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', provide the expected JSON snippet for the specific field(s) identified as discrepant in the alt_rationale based on referral data (e.g., '{ \"m0150\": \"2\" }', '{ \"m0063\": \"1AB2-C3D-EFG4\" }', '{ \"m0064\": \"***********\" }'). Combine related fields if appropriate. Leave blank if rating is '1'.", "examples": [{"context": ["SOC: { \"m0150\": \"1\", \"m0063\": \"1EG4-TE5-MK73\", \"m0065\": \"\", \"m0064\": \"***********\" } (M0150 value '1' represents Medicare FFS)", "Referral Financial: { \"primary_insurance_information\": { \"payer\": \"Medicare\", \"plan\": \"Part A & B\", \"policy_number\": \"1EG4-TE5-MK73\", ... }, \"secondary_insurance_information\": [], \"medicare_number\": \"1EG4-TE5-MK73\", \"medicaid_number\": \"\", \"payment_sources\": \"2\" } (Value '2' represents Medicare FFS)", "Referral Demographics: { ..., \"ssn\": \"***********\", ... }"], "rationale": " Primary payment source (Medicare FFS) matches ('2' in referral, '1' selected in SOC - assuming '1' maps to value '2' based on SOC field definition - need to confirm mapping). Medicare # matches. SSN matches. Medicaid # is blank in both.", "answer": "{ \"rating\": \"1\", \"reason\": \"Primary Payment Source (Medicare FFS), Medicare #, and SSN match referral data. Medicaid # not present in either.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC: { \"m0150\": \"1\", \"m0063\": \"1111-222-3333\", ... }", "Referral Financial: { ..., \"medicare_number\": \"1EG4-TE5-MK73\", \"payment_sources\": \"2\" }"], "rationale": "Medicare number (M0063) mismatches the referral medicare_number. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"M0063 Medicare # mismatch.\", \"alt_rationale\": \"Referral Financial Info lists medicare_number as 1EG4-TE5-MK73.\", \"alt_answer\": \"{ \\\\\"m0063\\\\\": \\\\\"1EG4-TE5-MK73\\\\\" }\" }"}], "options": [], "description": "Verbalize all that apply:     \n     \n 0  - None; no charge for current services     \n 1  - Medicare (traditional fee-for-service)     \n 2  - Medicare (HMO/managed care/Advantage plan)     \n 3  - Medicaid (traditional fee-for-service)     \n 4  - Medicaid (HMO/managed care)     \n 5  - Workers' compensation     \n 6  - Title programs (for example, Title III, V, or XX)     \n 7  - Other government (for example, TriCare, VA)     \n 8  - Private insurance     \n 9  - Private HMO/managed care     \n 10  - Self-pay     \n 11  - Other (specify)     \n UK  - Unknown", "dependencies": [{"code": "Demographics_REFERRAL", "source": "referral", "name": "Demographics", "question": "Extract and structure the patient's demographic information (e.g., name, date of birth, gender, race, ethnicity, language, identification numbers) found within the provided referral documentation.", "description": "Demographics"}, {"code": "FINANCIAL_INFORMAITON_REFERRAL", "source": "referral", "name": "Financial Information", "question": " Extract the patient's primary and secondary insurance details (payer, plan, policy/group numbers, effective/expiration dates), Medicare/Medicaid numbers, and identify the primary payment source(s) for home health services, as documented in the provided referral documentation.", "description": "Financial Information"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_PATIENT_MOOD_AND_BEHAVIORAL_HEALTH", "name": "QA_Patient Mood and Behavioral Health", "id": 8275, "types": ["SOC_CHECKLIST"], "sections": [32], "source": "tasks", "source_id": "266", "schema": {}, "mandatory": false, "notes": "Check:\n\nInternal Consistency: D0160 calculation. M1740 vs. M1745 logic.\n\nReferral Context: SOC D0150/M1720/M1740 vs. Ref baseline_cognitive_status.history_of_mood..., Ref active_problems_summary, Ref Dx, Ref Psych Meds.\nRate '0' for calculation errors, logical inconsistencies, or significant contradictions/omissions vs. referral.", "enabled": true, "question": "Review the SOC 'Patient Mood and Behavioral Health' assessment (d0150 PHQ2/9, d0160 PHQ9 Score, m1720 Anxiety, m1740 Behaviors, m1745 Behavior Frequency).\n\nAssess internal consistency: Is d0160 score calculated correctly from d0150 frequencies? Are m1740 and m1745 consistent (e.g., M1740=None implies M1745=Never)?\n\nCompare against relevant available referral context (Referral baseline_cognitive_status for mood/behavior history, active_problems_summary, diagnoses, psychotropic meds). Are findings consistent?\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all of the following are true:\n\nThe D0160 score (if PHQ9 done) correctly sums the D0150 frequencies (0=0, 1=1, 2=2, 3=3).\n\nM1740 and M1745 are logically consistent.\n\nThe overall mood/behavior picture doesn't significantly contradict relevant available referral information (baseline history, active issues, diagnoses, psych meds).\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nIncorrect calculation of the D0160 score.\n\nInconsistency between M1740=None and M1745>0.\n\nSignificant contradiction or omission compared to referral baseline mood/behavior history, active referral issues, or relevant diagnoses/medications without clear rationale.\n\nreason (Text String):\n\nIf rating is '1', state that the Mood/Behavior assessment is internally consistent and aligns with available referral context, noting missing referral context if applicable (e.g., \"Mood/Behavior assessment internally consistent and aligns with referral context.\", \"Internally consistent. Referral baseline mood history missing.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Incorrect D0160 calculation (Sum is X, score is Y).', 'Inconsistency: M1740=None selected but M1745 > 0.', 'Discrepancy: Referral notes history of depression, but D0150 shows no depressive symptoms.', 'Discrepancy: Antipsychotic med listed in referral, but M1740 indicates None.').\n\nalt_rationale (Text String): If rating is '0', explain the expected correction for the first or most significant discrepancy (e.g., 'Recalculated D0160 score is X.', 'If M1740=None (7), then M1745 should be 0.', 'Assessment should reflect or explore referral history of depression.', 'Presence of antipsychotic suggests potential for M1740 symptoms; review selection or med list.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Correct D0160 score to [Calculated Score].\"', '\"Set M1745 to 0.\"', '\"Re-evaluate D0150 considering referral history.\"', '\"Review M1740 selections based on medication profile.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Mood/Beh: { \"d0150...\": { ... }, \"d0160\": 4, \"m1720\": \"2\", \"m1740\": [\"7\"], \"m1745\": \"0\" } (Assume D0160=4 is correct calculation, Anxiety daily, No behavioral symptoms)", "Referral: (Assume no conflicting history/diagnoses/meds)"], "rationale": "D0160 score assumed correct. M1740=None aligns with M1745=Never. No contradictions with (assumed clear) referral context. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Mood/Behavior assessment internally consistent and aligns with available referral context.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Mood/Beh: { \"d0150...\": { D0150A freq=1, D0150B freq=1, all others 0 }, \"d0160\": 3, ...} (Sum of frequencies 1+1=2, but score recorded as 3)", "Referral: (Assume no conflicting info)"], "rationale": "D0160 score is calculated incorrectly. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Incorrect D0160 calculation (documented score is 3, calculated score is 2).\", \"alt_rationale\": \"Recalculated D0160 score based on D0150 frequencies (1+1) is 2.\", \"alt_answer\": \"{ \\\\\"d0160\\\\\": 2 }\" }"}], "options": [], "description": "Ask the patient:  \\\"Over the last 2 weeks, have you been bothered by any of the following problems?\\\"     \n     \n A - Little interest or pleasure in doing things   ,    B - Feeling down, depressed, or hopeless   ,        \n Coding options: 0 - No, 1 - Yes, 9 - No response     \n     \n If relevant, ask:  \\\"About how often have you been bothered by this?\\\"     \n     \n Coding Options:     \n 0  -  Never or 1 day     \n 1 -    2-6 days  (several days)      \n 2 -   7-11 days  (half or more of the days)      \n 3 -   12-14 days  (nearly every day)     \n     \n If either D150A2 or D150B2 is coded 2 or 3, CONTINUE asking D0150 - PHQ 9. If not, END the PHQ 2 interview.     \n     \n C - Trouble falling or staying asleep, or sleeping too much         \n D -  Feeling tired or having little energy         \n E - Poor appetite or overeating         \n F - Feeling bad about yourself – or that you are a failure or have let yourself or your family down      \n    G - Trouble concentrating on things, such as reading the newspaper or watching television         \n H - Moving or speaking so slowly that other people could have noticed. Or the opposite – being so  fidgety or restless that you have been moving around a lot more than usual        \n  I - Thoughts that you would be better off dead, or of hurting yourself in some way", "dependencies": [{"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "diagnoses_REFERRAL", "source": "referral", "name": "Diagnoses", "question": "Extract and structure the patient's primary and other diagnoses, including descriptions, ICD-10 codes, and symptom control ratings if available, as found in the referral documentation.", "description": "Diagnoses"}, {"code": "MEDICATIONS_REFERRAL", "source": "referral", "name": "Medications", "question": "Extract the list of medications the patient is currently taking, including details such as name, dose, strength, route, frequency, start/end dates, status, purpose, and any special instructions, as found in the provided referral documentation.", "description": "Medications & Allergies"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GG0170K1", "name": "Walk 150 Feet", "id": 2611, "types": ["SOC_CHECKLIST"], "sections": [35], "source": "tasks", "source_id": "311", "schema": {}, "mandatory": false, "notes": "To QA walking 150 feet ability (both current and discharge goal), we should look at related mobility measures, medical conditions affecting mobility, assistive devices used, safety risks, and compare with shorter distance walking abilities since 150ft is a longer distance", "enabled": true, "question": "Based on the OASIS E item code GG0170K1; Does the patient have the ability to walk at least 150 feet in a corridor or similar space?", "inputType": "text", "criteria": "1. Compare 150ft walking ability against shorter distances (10ft, 50ft) - ability should not be better for longer distances\n2. If patient uses wheelchair (GG0170Q), verify consistency with wheel distances\n3. Check dyspnea status (M1400) and oxygen levels with activity\n4. Verify mobility status and devices used align with reported walking ability\n5. Discharge goal (GG0170K2) should be realistic based on current status, medical conditions, and other functional measures\n6. Ensure homebound status and reasons align with walking ability", "examples": [], "options": [], "description": "Once standing, the ability to walk at least 150 feet in a corridor or similar space.", "dependencies": [{"code": "HOMEBOUND_CRITERIA_1", "source": "referral"}, {"code": "GG0170J1", "source": "tasks"}, {"code": "GG0170J2", "source": "tasks"}, {"code": "GG0170I1", "source": "tasks"}, {"code": "GG0170I2", "source": "tasks"}, {"code": "M1400", "source": "tasks"}, {"code": "M1033", "source": "tasks"}, {"code": "GG0170Q", "source": "tasks"}, {"code": "GG0170R1", "source": "tasks"}, {"code": "GG0170S1", "source": "tasks"}, {"code": "FUNCTIONAL_AMBULATION", "source": "tasks"}, {"code": "MUSCULOSKELETAL_DEVICE", "source": "tasks"}, {"code": "MUSCULOSKELETAL_BALANCE", "source": "tasks"}, {"code": "MUSCULOSKELETAL_STATUS", "source": "tasks"}, {"code": "OXYGEN_SATURATION_ON_ROOM_AIR_ACTIVE", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GG0170J1", "name": "Walk 50 Feet with 2 Turns", "id": 2619, "types": ["SOC_CHECKLIST"], "sections": [35], "source": "tasks", "source_id": "312", "schema": {}, "mandatory": false, "notes": "To verify walking ability for 50 feet with 2 turns, we need to consider the patient's overall functional mobility status, use of assistive devices, and any physical/medical limitations. Related mobility assessments and homebound status can help validate the scoring.", "enabled": true, "question": "Based on the OASIS E item code GG0170J1: Does the patient have the ability to walk 50 feet and make two turns? ", "inputType": "text", "criteria": "1. Compare walk 50 feet score (GG0170J1/J2) with walk 10 feet score (GG0170I1/I2) - 50 feet score cannot show better function than 10 feet\n2. Compare with walk 150 feet score (GG0170K1/K2) - 50 feet score cannot show worse function than 150 feet\n3. Verify consistency with M1860 ambulation assessment\n4. Check if documented functional ambulation status and assistive devices support the scoring\n5. Validate against homebound status and activity tolerance - severe limitations should align with lower scores\n6. Discharge goal (GG0170J2) should show same or better function than current performance (GG0170J1)", "examples": [], "options": [], "description": "Once standing, the ability to walk 50 feet and make two turns.", "dependencies": [{"code": "HOMEBOUND_CRITERIA_1", "source": "referral"}, {"code": "GG0170I1", "source": "tasks"}, {"code": "GG0170I2", "source": "tasks"}, {"code": "GG0170K1", "source": "tasks"}, {"code": "GG0170K2", "source": "tasks"}, {"code": "M1860", "source": "tasks"}, {"code": "FUNCTIONAL_AMBULATION", "source": "tasks"}, {"code": "MUSCULOSKELETAL_DEVICE", "source": "tasks"}, {"code": "MUSCULOSKELETAL_STATUS", "source": "tasks"}, {"code": "HOMEBOUND_STATUS", "source": "tasks"}, {"code": "M1400", "source": "tasks"}, {"code": "TOLERANCE_LEVEL", "source": "tasks"}, {"code": "ACTIVITY_INTENSITY", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_ADLs/IADLs_M1800s", "name": "QA_(M1800s) ADLs/IADLs", "id": 8283, "types": ["SOC_CHECKLIST"], "sections": [36], "source": "tasks", "source_id": "318", "schema": {}, "mandatory": false, "notes": "Compare SOC M1800-M1870 against:\n\nReferral Context: Ref active_problems_summary.functional_status, Ref reason, Ref Recent_Acute_Care_Summary.\n\nSOC Context: SOC (GG0170) Mobility, SOC (GG0130) Self-Care, SOC Mobility_Safety_Aid.\nFocus on consistency regarding current functional abilities. Rate '0' for significant contradictions based on available data.", "enabled": true, "question": "Review the SOC assessment of current ADL performance (M1800 Grooming, M1810 Dress Upper, M1820 Dress Lower, M1830 Bathing, M1840 Toilet Transfer, M1845 Toilet Hygiene, M1860 Ambulation, M1870 Feeding). Compare these against:\n\nRelevant available referral context describing current functional problems or the reason for referral (Referral active_problems_summary.functional_status, reason, Recent_Acute_Care_Summary).\n\nOther SOC functional assessments detailing current abilities ((GG0170) Mobility, (GG0130) Self-Care, Mobility_Safety_Aid).\nAssess for general consistency. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if the M1800-series selections generally align with the overall picture of the patient's current functional abilities described in the available referral context (active problems, reason for referral) AND are consistent with other detailed SOC functional assessments (GG0170, GG0130, Mobility Safety).\n\nAssign '0' (Incorrect/Issue) if there is a significant contradiction between one or more M1800-series selections and either the available referral context regarding current function OR other SOC functional assessments (e.g., M1860 indicates independence while referral states bedbound or GG0170 shows dependence; M1830 independence contradicts GG0130E dependence).\n\nreason (Text String):\n\nIf rating is '1', state that the M1800s ADL assessment is consistent with available referral context and other SOC functional assessments, noting key missing referral context if applicable (e.g., \"M1800s consistent with referral context and SOC GG assessments.\", \"Consistent; Referral lacks details on current functional problems.\").\n\nIf rating is '0', state all specific significant discrepancies found (e.g., 'M1860 inconsistency: SOC indicates Independent Ambulation (0), but referral active problems note bedbound status.', 'M1830 inconsistency: SOC indicates Independent Bathing (0), contradicts GG0130E Shower/Bathing score indicating Dependence (01).', 'M1810/M1820 inconsistency: SOC indicates dressing independence, contradicts referral reason of 'ADL assist post-stroke'.').\n\nalt_rationale (Text String): If rating is '0', explain the expected status based on the conflicting data source (referral or other SOC assessment) for the first or most significant discrepancy (e.g., 'Referral active problems state patient is bedbound, suggesting M1860 should be 6.', 'GG0130E score of 01 (Dependent) suggests M1830 Bathing should be 6.', 'Referral reason implies need for dressing assistance, suggesting M1810/M1820 require review.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Correct M1860 to 6 (Bedfast).\"', '\"Correct M1830 to 6 (Unable to participate).\"', '\"Re-evaluate M1810/M1820 based on clinical presentation and referral reason.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC M1800s: { \"m1810\": \"2\", \"m1820\": \"2\", \"m1860\": \"3\", ... } (Needs help dressing, walks w/ walker)", "SOC GG0170: { ..., \"gg0170i\": \"03\", \"gg0170j\": \"03\", ...} (Walks 10ft/50ft requires Mod Assist)", "Referral Func/Cog: { \"active_problems_summary\": { \"functional_status\": \"Requires walker, assist w/ dressing\", ... } }"], "rationale": " M1800s selections align with the current functional description in the referral and the detailed GG0170 mobility assessment. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"M1800s ADL assessment consistent with referral context and SOC GG assessments.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC M1800s: { ..., \"m1860\": \"0\", ... } (Independent ambulation)", "Referral Func/Cog: { \"active_problems_summary\": { \"functional_status\": \"Bedbound post-surgery\", ... } }"], "rationale": "SOC M1860 (Independent) significantly contradicts the referral stating the patient is currently bedbound. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"M1860 inconsistency: SOC indicates Independent Ambulation (0), but referral active problems note bedbound status.\", \"alt_rationale\": \"Referral active functional status is bedbound, suggesting M1860 should be 6.\", \"alt_answer\": \"{ \\\\\"m1860\\\\\": \\\\\"6\\\\\" }\" }"}], "options": [], "description": "Specifically: washing face and hands, hair care, shaving or make up, teeth or denture care, or fingernail care.     \n     \n 0  - Able to groom self unaided, with or without the use of assistive devices or adapted methods     \n     \n 1  - Grooming utensils must be placed within reach before able to complete grooming activities     \n     \n 2  - Someone must assist the patient to groom self     \n     \n 3  - Patient depends entirely upon someone else for grooming needs", "dependencies": [{"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "Referral_Overview_REFERRAL", "source": "referral", "name": "Referral Overview", "question": " Extract key overview information about the home health referral, including primary diagnosis, source details, referral date, reason for referral, services ordered, and physician-ordered start date, from the provided referral documentation. Synthesize a concise narrative summary combining these key elements.", "description": "Referral Overview"}, {"code": "Recent_Acute_Care_Summary_REFERRAL", "source": "referral", "name": "Recent Acute Care Summary", "question": "Extract key details about the patient's most recent acute care episode (e.g., hospitalization, SNF stay, ER visit, significant procedure) that is relevant to the current home health referral, as documented in the referral materials.", "description": "Recent Acute Care Summary"}, {"code": "GG0170", "source": "tasks"}, {"code": "GG0130A", "source": "tasks"}, {"code": "Mobility_Safety_Aid", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_Patient_outlook_planning", "name": "QA_Patient Outlook & Planning", "id": 8287, "types": ["SOC_CHECKLIST"], "sections": [41], "source": "tasks", "source_id": "362", "schema": {}, "mandatory": false, "notes": "This QA relies heavily on clinical judgment and consistency checks across multiple SOC fields and potentially referral context. Check:\n\nM1033 vs. specific data points (falls, weight, hospital use, meds, mental status).\n\nPrognosis/Rehab Potential vs. Diagnoses, Function (M1800s/GG), Cognition (BIMS/M1700), Support (M1100).\n\nDischarge Plan vs. Prognosis, Rehab Potential, Function, Support (M1100).\nRate '0' for clear factual contradictions in M1033 or clinically questionable/unrealistic outlook/planning components.", "enabled": true, "question": "Review the SOC 'Patient Outlook & Planning' section (M1033 Hospitalization Risk, POC 20 Prognosis, POC 22 Rehab Potential & Discharge Plan).\n\nAssess consistency: Do the selected M1033 risk factors align with information gathered elsewhere (falls history, weight loss, ED/hospital use, meds, mental status)?\n\nIs the Prognosis (POC 20) and Rehab Potential (POC 22) reasonable given the patient's diagnoses, functional status, cognitive status, and support system?\n\nIs the Discharge Plan (POC 22) realistic and appropriate given the prognosis, rehab potential, and documented living situation/supports?\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if the M1033 risk factors are documented consistently with other assessment data, and the Prognosis, Rehab Potential, and Discharge Plan appear clinically reasonable and logically consistent with the overall patient picture (diagnoses, function, cognition, support).\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nClear contradiction between selected M1033 risk factors and supporting data elsewhere in the assessment (e.g., M1033 says no falls, but fall history documented elsewhere).\n\nPrognosis or Rehab Potential seems grossly inconsistent with the patient's clinical condition (e.g., \"Excellent\" prognosis for terminal illness; \"Good\" rehab potential for bedbound patient with severe dementia and no support).\n\nDischarge Plan is clearly unrealistic or contradicts documented needs/supports (e.g., Discharge to self-care for totally dependent patient living alone; Discharge to facility when patient/family refuse).\n\nreason (Text String):\n\nIf rating is '1', state that the Outlook & Planning components are consistent with the overall clinical assessment (e.g., \"M1033, Prognosis, Rehab Potential, Discharge Plan consistent with overall assessment.\").\n\nIf rating is '0', state all specific inconsistencies or questionable elements found (e.g., 'Inconsistency: M1033 selected No Falls, but fall history documented in Balance Assessment.', 'Questionable Prognosis/Rehab Potential: \"Good\" seems optimistic given severe COPD and limited mobility.', 'Unrealistic Discharge Plan: Discharge to self-care contradicts M1800s showing total dependence and patient living alone.').\n\nalt_rationale (Text String): If rating is '0', explain the expected correction or area needing review for the first or most significant discrepancy (e.g., 'M1033 fall risk selection should reflect documented fall history.', 'Prognosis/Rehab Potential should be reassessed considering severity of illness and functional deficits.', 'Discharge plan requires revision based on patient's dependence level and available support.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Correct M1033 #1 (Falls) to Yes.\"', '\"Re-evaluate Prognosis and Rehab Potential.\"', '\"Revise Discharge Plan to align with patient needs/support.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Outlook: { \"m1033\": { \"1_history_of_falls\": \"1\", \"7_taking_5_or_more_medications\": \"1\", ... }, \"poc_485_20_prognosis\": \"3\", \"poc_485_22...\": { \"rehab_potential\": \"2\", \"discharge_plan\": \"1\" } } (Falls, >5 meds, Fair prog, Good rehab, D/C self-care)", "SOC Assessment: Documents 2 falls, 6 meds, patient is moderately impaired functionally but motivated, lives with supportive spouse (M1100)."], "rationale": " M1033 factors documented elsewhere. Prognosis/Rehab Potential seem reasonable given context. D/C to self-care is plausible with supportive spouse and good rehab potential. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Outlook & Planning components consistent with overall clinical assessment findings.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Outlook: { \"m1033\": { \"1_history_of_falls\": \"2\", ... }, ... } (M1033 indicates No history of falls)", "SOC Balance Assessment: Notes \"Patient reports 3 falls in past 6 months.\""], "rationale": " M1033 selection for fall history directly contradicts information documented in the balance assessment. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Inconsistency: M1033 selected No Falls (2), but fall history documented in Balance Assessment.\", \"alt_rationale\": \"M1033 Fall Risk (#1) selection should be Yes (1) based on documented fall history.\", \"alt_answer\": \"{ \\\\\"m1033\\\\\": { \\\\\"1_history_of_falls\\\\\": \\\\\"1\\\\\" } }\" }"}, {"context": ["SOC Outlook: { ..., \"poc_485_20_prognosis\": \"1\", \"poc_485_22...\": { \"rehab_potential\": \"1\", ... } } (Poor prognosis, Excellent rehab potential)", "SOC Assessment: Pat<PERSON> has terminal cancer diagnosis, is cachectic, bedbound (M1860=6), minimal participation in care."], "rationale": "Excellent rehab potential seems clinically inconsistent with a poor prognosis and the patient's documented condition (terminal illness, bedbound, cachexia). Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Questionable Rehab Potential: 'Excellent' (1) seems inconsistent with Poor prognosis, terminal diagnosis, and bedbound status.\", \"alt_rationale\": \"Rehab Potential should be reassessed considering the poor prognosis and severe functional limitations; likely 'Poor' (4) or 'Guarded' (5).\", \"alt_answer\": \"\\\"Re-evaluate Rehab Potential selection.\\\"\" }"}], "options": [], "description": "Verbalize all that apply:     \n     \n 1  - History of falls (2 or more falls – or any fall with an injury – in the past 12 months)     \n     \n 2  - Unintentional weight loss of a total of 10 pounds or more in the past 12 months     \n     \n 3  - Multiple hospitalizations (2 or more) in the past 6 months     \n     \n 4  - Multiple emergency department visits (2 or more) in the past 6 months     \n     \n 5  - Decline in mental, emotional, or behavioral status in the past 3 months     \n     \n 6  - Reported or observed history of difficulty complying with any medical instructions (for example, medications, diet, exercise) in the past 3 months     \n     \n 7  - Currently taking 5 or more medications     \n     \n 8  - Currently reports exhaustion     \n     \n 9  - Other risk(s) not listed in 1 - 8     \n     \n 10  - None of the above", "dependencies": [{"code": "diagnoses_REFERRAL", "source": "referral", "name": "Diagnoses", "question": "Extract and structure the patient's primary and other diagnoses, including descriptions, ICD-10 codes, and symptom control ratings if available, as found in the referral documentation.", "description": "Diagnoses"}, {"code": "Recent_Acute_Care_Summary_REFERRAL", "source": "referral", "name": "Recent Acute Care Summary", "question": "Extract key details about the patient's most recent acute care episode (e.g., hospitalization, SNF stay, ER visit, significant procedure) that is relevant to the current home health referral, as documented in the referral materials.", "description": "Recent Acute Care Summary"}, {"code": "Balance_Assessments", "source": "tasks"}, {"code": "NUTRITION_ASSMNT", "source": "tasks"}, {"code": "COGNITIVE_STATUS_DETAILS", "source": "tasks"}, {"code": "MEDICATIONS_SOC", "source": "tasks"}, {"code": "ADLs/IADLs_M1800s", "source": "tasks"}, {"code": "GG0130A", "source": "tasks"}, {"code": "GG0170", "source": "tasks"}, {"code": "patient_living_situation", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_BIMS_ASSESSMENT", "name": "QA_BIMS Assessment", "id": 8272, "types": ["SOC_CHECKLIST"], "sections": [32], "source": "tasks", "source_id": "253", "schema": {}, "mandatory": false, "notes": "Check:\n\nC0100 vs C0200-C0500 completion status.\n\nC0500 calculation accuracy (Points: C0200:0-3; C0300A:0-3; C0300B:0-2; C0300C:0-1; C0400A:0-2; C0400B:0-2; C0400C:0-2).\n\nGeneral consistency check: BIMS score vs M1700/M1710.\nRate '0' for inconsistencies in documentation logic, calculation errors, or major contradictions with overall cognitive assessment.", "enabled": true, "question": "Review the SOC 'BIMS Assessment' documentation (C0100-C0500).\n\nCheck if C0100 (Should interview be conducted?) is answered and if subsequent fields (C0200-C0500) are completed appropriately based on the C0100 response.\n\nIf C0100=1 (Yes), verify if the C0500 Summary Score correctly calculates the points awarded in C0200, C0300, and C0400 based on OASIS scoring rules.\n\nAssess if the overall BIMS result (score, completion status) is generally consistent with the broader cognitive assessment (e.g., M1700, M1710).\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all of the following are true:\n\nC0100 is documented.\n\nIf C0100='0', C0200-C0500 are appropriately blank/skipped.\n\nIf C0100='1', C0200-C0500 are documented.\n\nIf C0100='1', the C0500 score accurately reflects the sum of points from C0200-C0400 based on standard BIMS scoring.\n\nThe BIMS outcome doesn't grossly contradict the overall cognitive status documented elsewhere (e.g., M1700).\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nInconsistency between C0100 and completion status of C0200-C0500.\n\nIncorrect calculation of the C0500 score based on documented C0200-C0400 responses.\n\nSignificant contradiction between BIMS score/completion and the overall documented cognitive status (M1700/M1710).\n\nreason (Text String):\n\nIf rating is '1', state that the BIMS documentation and score calculation are correct and consistent (e.g., \"BIMS documentation consistent, score calculation correct.\", \"BIMS appropriately not conducted (C0100=0).\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Inconsistency: C0100=1 but C0200-C0500 are blank.', 'Incorrect C0500 calculation (sum is X, documented score is Y).', 'Inconsistency: BIMS score of 2 contradicts M1700=0 (Alert/Oriented).', 'Inconsistency: C0100=0 but C0500 has score documented.').\n\nalt_rationale (Text String): If rating is '0', explain the expected correction for the first or most significant discrepancy (e.g., 'C0200-C0500 should be completed if C0100=1.', 'Recalculated C0500 score based on C0200-C0400 responses is X.', 'Low BIMS score suggests M1700 requires review/update.', 'C0200-C0500 should be blank if C0100=0.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Complete BIMS fields C0200-C0500.\"', '\"Correct C0500 score to [Calculated Score].\"', '\"Review/update M1700 based on BIMS results.\"', '\"Clear C0200-C0500 fields.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC BIMS: { \"c0100\": \"1\", \"c0200\": \"3\", \"c0300\": { \"a_\": \"3\", \"b_\": \"2\", \"c_\": \"0\" }, \"c0400_recall\": { \"a_\": \"2\", \"b_\": \"0\", \"c_\": \"2\" }, \"c0500\": 12 } (Points: 3+3+2+0+2+0+2 = 12)", "SOC Cognitive: { \"m1700\": \"1\", ... } (Requires prompting - consistent with score 12)"], "rationale": "C0100=1, fields C0200-C0500 completed. C0500 score (12) correctly calculated. Score 12 is plausible with M1700=1. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"BIMS documentation consistent, score calculation correct and aligns with overall cognitive status.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC BIMS: { \"c0100\": \"1\", \"c0200\": \"2\", \"c0300\": { \"a_\": \"3\", \"b_\": \"1\", \"c_\": \"1\" }, \"c0400_recall\": { \"a_\": \"1\", \"b_\": \"1\", \"c_\": \"0\" }, \"c0500\": 10 } (Actual Points: 2+3+1+1+1+1+0 = 9)"], "rationale": "C0500 score documented as 10, but calculation based on C0200-C0400 is 9. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Incorrect C0500 calculation (documented score is 10, calculated score is 9).\", \"alt_rationale\": \"Recalculated C0500 score based on C0200-C0400 responses is 9.\", \"alt_answer\": \"{ \\\\\"c0500\\\\\": 9 }\" }"}], "options": [], "description": "Should Brief Interview for Mental Status (C0200-C0500) be Conducted?      \n     \n 0 - No  (patient is rarely/never understood)     \n 1 - Yes     \n     \n Attempt to conduct interview with all patients.", "dependencies": [{"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "COGNITIVE_STATUS_DETAILS", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GASTRO_ASSMNT", "name": "QA_Gastrointestinal", "id": 8278, "types": ["SOC_CHECKLIST"], "sections": [33], "source": "tasks", "source_id": "273", "schema": {}, "mandatory": false, "notes": "Check:\n\nInternal Consistency: SOC gastro_summary vs. SOC detailed GI findings (bowel_sounds, palpation, M1620, M1630, gi_problems). WNL->Problem->Need linkage. Ostomy consistency. Consistency with SOC Meds/Nutrition.\n\nReferral Context: SOC findings vs. Ref active_problems_summary.gastrointestinal, Ref History/Orders (Ostomy).\nRate '0' for internal inconsistencies OR significant contradictions with available referral context.", "enabled": true, "question": "Review the SOC 'Gastrointestinal' assessment findings (bowel_sounds, palpation, m1620 Bowel Incontinence, m1630 Ostomy, gi_problems) and the gastro_summary object (gastro_wnl, active_problems, care_plan_needs).\n\nAssess internal consistency: Does gastro_wnl reflect detailed findings (abnormal sounds/palpation, incontinence, ostomy, problems)? If gastro_wnl=No, are active_problems and care_plan_needs documented? Are M1630/gi_problems consistent regarding ostomy? Are findings consistent with relevant SOC MEDICATIONS (e.g., laxatives, anti-emetics) and SOC NUTRITION (nausea/vomiting)?\n\nCompare against relevant available referral context (Referral active_problems_summary.gastrointestinal, ostomy history/orders).\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all of the following are true:\n\nThe SOC gastro_summary.gastro_wnl status is consistent with the detailed GI findings.\n\nIf gastro_wnl is '2' (No), then active_problems and care_plan_needs are appropriately documented.\n\nOstomy status (M1630/gi_problems) is internally consistent and aligns with SOC meds/supplies if applicable.\n\nFindings align with relevant SOC Medications/Nutrition context.\n\nThe documented findings do not significantly contradict relevant GI information noted in the available referral context.\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nInternal inconsistency in the gastro_summary (WNL status vs. findings, missing problem, or missing care need).\n\nInternal inconsistency regarding ostomy status or GI meds/symptoms.\n\nA significant GI issue (like ostomy, severe incontinence, active N/V) noted in the referral context is seemingly ignored or contradicted in the SOC GI assessment without clear rationale.\n\nreason (Text String):\n\nIf rating is '1', state that the GI assessment and summary are internally consistent and align with available referral context, noting missing referral context if applicable (e.g., \"GI assessment and summary internally consistent and align with referral context.\", \"Internally consistent. Referral context for GI status missing.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Inconsistency: GI marked WNL despite documented Ostomy (M1630=2).', 'Inconsistency: GI not WNL, but no active problem documented.', 'Inconsistency: Active problem (Constipation) documented, but no care plan need listed.', 'Discrepancy: Referral notes Colostomy, but M1630 is 0.', 'Inconsistency: Nausea listed as gi_problem, but no anti-emetic in SOC Meds or related care plan need.').\n\nalt_rationale (Text String): If rating is '0', explain the expected action or correction for the first or most significant discrepancy (e.g., 'Presence of Ostomy (M1630=2) means gastro_wnl should be 2.', 'Active problem needs to be specified for abnormal bowel sounds.', 'Care plan need required for Constipation.', 'Referral indicates Colostomy; M1630 should reflect this.', 'Document anti-emetic if used or care plan need for nausea.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Set gastro_wnl to 2 (No).\"', '\"Add specific active problem for GI.\"', '\"Add care plan need for Constipation management.\"', '\"Set M1630 to 1 or 2 based on details.\"', '\"Update gi_problems, care plan needs, or verify SOC Meds related to Nausea.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC GI: { \"bowel_sounds\": [\"1\"], \"palpation\": \"4\", \"m1620\": \"0\", \"m1630\": \"0\", \"gi_problems\": [], \"gastro_summary\": { \"gastro_wnl\": \"1\", \"active_problems\": \"\", \"care_plan_needs\": \"\" } } (All WNL)", "SOC Meds/Nutrition: (No relevant GI meds/issues)", "Referral: (Assume no active GI problems, no ostomy history/orders)"], "rationale": "SOC findings WNL, summary consistent (WNL=1). No relevant meds/nutrition issues. Aligns with lack of issues in referral. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"GI assessment and summary internally consistent and align with available referral context.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}], "options": [], "description": "Interview/Examine/Verbalize:     \n - Appetite     \n - Date and Time of Last Bowel Movement     \n - Stool Consistency     \n If abnormal gastrointestinal findings, describe:     \n - Abdominal Pain     \n - Distention     \n - Scars     \n - Bowel Sounds      \n - Palpation      \n - Vomiting     \n - Blood in stool     \n Active problems and skilled needs?", "dependencies": [{"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "diagnoses_REFERRAL", "source": "referral", "name": "Diagnoses", "question": "Extract and structure the patient's primary and other diagnoses, including descriptions, ICD-10 codes, and symptom control ratings if available, as found in the referral documentation.", "description": "Diagnoses"}, {"code": "ORDERS_REFERRAL", "source": "referral", "name": "Orders", "question": "Extract the specific services ordered, including the frequency and duration if specified, for Nursing, Physical Therapy, Occupational Therapy, Speech Therapy, Medical Social Work, and Home Health Aide. Also, capture any specified clinical parameters to monitor (e.g., vital sign ranges to report).", "description": "Specific Service Orders"}, {"code": "MEDICATIONS_SOC", "source": "tasks"}, {"code": "NUTRITION_ASSMNT", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_SKIN_WOUNDS_ASSMNT", "name": "QA_Skin & Wounds", "id": 8288, "types": ["SOC_CHECKLIST", "SOC-DEV"], "sections": [38, 681], "source": "tasks", "source_id": "1966", "schema": {}, "mandatory": false, "notes": "Check:\n\nInternal Consistency: SOC skin_summary vs. SOC detailed ulcer assessments. WNL->Problem->Need linkage. Consistency within M13xx items.\n\nReferral Context: SOC findings vs. Ref active_problems_summary.integumentary, Ref Orders (wound care), Ref History (surgery).\nRate '0' for internal inconsistencies OR significant contradictions with available referral context.", "enabled": true, "question": "Review the SOC 'Skin & Wounds' assessment findings (skin_breakdown_risk_assessment, pressure_ucler_assessment, stasis_ulcer_assessment, surgical_wound_assessment) and the skin_summary object (skin_wnl, skin_active_problems, skin_care_plan_needs).\n\nAssess internal consistency: Does skin_wnl reflect detailed findings (presence of ulcers)? Are OASIS items (M1306/M1311/M1324, M1330/M1332/M1334, M1340/M1342) internally consistent? If skin_wnl=No, are active_problems and care_plan_needs documented?\n\nCompare against relevant available referral context (Referral active_problems_summary.integumentary, diagnoses, surgical history, wound care orders).\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all of the following are true:\n\nThe SOC skin_wnl status is consistent with the detailed findings regarding pressure, stasis, or surgical ulcers.\n\nRelevant OASIS items are internally consistent (e.g., M1306=Yes corresponds to counts in M1311).\n\nIf skin_wnl is '2' (No), then skin_active_problems is not empty/blank.\n\nIf skin_active_problems is not empty, then skin_care_plan_needs is not empty/blank.\n\nThe documented findings do not significantly contradict relevant skin/wound information noted in the available referral context.\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nInternal inconsistency in the skin_summary (WNL status vs. findings, missing problem, or missing care need).\n\nInternal inconsistency within the detailed OASIS ulcer items.\n\nA significant wound/skin issue noted in the referral context (active problem summary, orders, recent surgery) is seemingly ignored or contradicted in the SOC assessment without clear rationale.\n\nreason (Text String):\n\nIf rating is '1', state that the Skin & Wounds assessment and summary are internally consistent and align with available referral context, noting missing referral context if applicable (e.g., \"Skin/Wound assessment and summary internally consistent and align with referral context.\", \"Internally consistent. Referral context for integumentary status missing.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Inconsistency: skin_wnl marked Yes despite documented Stage 2 pressure ulcer (M1306=1).', 'Inconsistency: M1306=Yes but M1311 ulcer counts are all zero.', 'Inconsistency: Active problem (Sacral Ulcer) documented, but no care plan need listed.', 'Discrepancy: Referral notes order for LLE wound care, but no stasis or surgical wound documented in SOC assessment.').\n\nalt_rationale (Text String): If rating is '0', explain the expected action or correction for the first or most significant discrepancy (e.g., 'Presence of Stage 2 ulcer means skin_wnl should be 2.', 'M1311 counts need to reflect the ulcer indicated by M1306.', 'Care plan need required for Sacral Ulcer.', 'Assessment should include documentation of LLE wound mentioned in referral orders.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Set skin_wnl to 2 (No).\"', '\"Update M1311 counts.\"', '\"Add care plan need for Sacral Ulcer.\"', '\"Assess and document LLE wound.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Skin: { \"pressure_ucler_assessment\": { \"m1306\": \"0\", ... }, \"stasis_ulcer_assessment\": { \"m1330\": \"0\", ... }, \"surgical_wound_assessment\": { \"m1340\": \"0\", ... }, \"skin_summary\": { \"skin_wnl\": \"1\", \"skin_active_problems\": \"\", \"skin_care_plan_needs\": \"\" } }", "Referral: (Assume no active integumentary problems, no wound care orders)"], "rationale": "No ulcers documented, summary consistent (WNL=1). Aligns with lack of issues in referral. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Skin & Wounds assessment and summary internally consistent and align with available referral context.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Skin: { \"pressure_ucler_assessment\": { \"m1306\": \"1\", \"m1311\": { \"a1_stage_2\": 1, ... }, \"m1324\": \"2\" }, ..., \"skin_summary\": { \"skin_wnl\": \"1\", ... } } (Stage 2 ulcer present, but summary WNL=1)", "Referral: (Assume consistent or no data)"], "rationale": " Internal inconsistency: skin_wnl is '1' despite documented pressure ulcer (M1306=1). Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Inconsistency: skin_wnl marked Yes despite documented Stage 2 pressure ulcer (M1306=1).\", \"alt_rationale\": \"Presence of Stage 2 ulcer (M1306=1) means skin_wnl should be 2 (No).\", \"alt_answer\": \"\\\"Set skin_wnl to 2 (No) and document active problem/care plan need.\\\"\" }"}], "options": [], "description": "Interview/examine/verbalize     \n - Inspect bony prominence, feet, and other high-risk areas of patient     \n     \n Any signs of redness, breakdown?  If Yes:     \n     \n - Note and describe all redness, breakdown/injury by location, type, stage, measurement      \n - Describe patients skin breakdown prevention measures      \n Add and label a photograph", "dependencies": [{"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "diagnoses_REFERRAL", "source": "referral", "name": "Diagnoses", "question": "Extract and structure the patient's primary and other diagnoses, including descriptions, ICD-10 codes, and symptom control ratings if available, as found in the referral documentation.", "description": "Diagnoses"}, {"code": "diagnoses_treatments", "source": "referral", "name": "Medical/Surgical History", "question": "Extract details about the patient's past surgical history and any relevant ongoing special treatments, procedures, or programs (like IV therapy, dialysis, oxygen, etc.) mentioned in the provided referral documentation.", "description": "Medical/Surgical History"}, {"code": "ORDERS_REFERRAL", "source": "referral", "name": "Orders", "question": "Extract the specific services ordered, including the frequency and duration if specified, for Nursing, Physical Therapy, Occupational Therapy, Speech Therapy, Medical Social Work, and Home Health Aide. Also, capture any specified clinical parameters to monitor (e.g., vital sign ranges to report).", "description": "Specific Service Orders"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_PATIENT_DETAILS", "name": "QA_Patient Details", "id": 8271, "types": ["SOC_CHECKLIST"], "sections": [85], "source": "tasks", "source_id": "216", "schema": {}, "mandatory": false, "notes": "Compare SOC fields M0040, M0066, M0069 against available Referral fields first_name, middle_name, last_name, date_of_birth, gender. Do not rate '0' simply because referral data is missing. Rate '0' only for confirmed mismatches.\n", "enabled": true, "question": "Review the patient's Name (M0040), Date of Birth (M0066), and Gender (M0069) entered in the 'Patient Details' section of the Start of Care assessment. Compare these details against the corresponding information (first_name, middle_name, last_name, date_of_birth, gender) that is available in the 'Demographics_REFERRAL' field from the referral documentation. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all available corresponding data points in the referral match the SOC data, OR if there is insufficient referral data to make a comparison for any specific field (i.e., no mismatches found).\n\nAssign '0' (Incorrect/Issue) only if there is at least one mismatch between a SOC data point and the corresponding available referral data point.\n\nreason (Text String):\n\nIf rating is '1', state which fields matched and explicitly note any fields that could not be verified due to missing referral data (e.g., \"Name and DOB match referral data. Referral gender missing.\", or \"Name, DOB, and Gender match referral data.\", or \"No discrepancies found based on available referral data. Name and DOB were missing in referral.\").\n\nIf rating is '0', state the specific field(s) with discrepancies (e.g., 'Name mismatch', 'DOB mismatch').\n\nalt_rationale (Text String): If rating is '0' due to a discrepancy, explain the correct information based on the referral documentation (e.g., 'Referral document lists DOB as 01/02/1950'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0' due to a discrepancy, provide the expected JSON object for the original 'Patient Details' field based only on the referral data for name, DOB, and gender (e.g., '{ \"m0040\": \"<PERSON>\", \"m0066\": \"01/02/1950\", \"m0069\": \"1\" }'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Patient Details: { \"m0040\": \"Donald Duck\", \"m0066\": \"06/09/1934\", \"m0069\": \"1\" }", "Referral Demographics: { \"first_name\": \"<PERSON>\", \"middle_name\": \"\", \"last_name\": \"<PERSON>\", \"date_of_birth\": \"06/09/1934\", \"gender\": \"1\" }"], "rationale": " All comparable fields (Name, DOB, Gender) match.", "answer": "{ \"rating\": \"1\", \"reason\": \"Name, DOB, and Gender match referral data.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Patient Details: { \"m0040\": \"Mickey Mouse\", \"m0066\": \"11/18/1928\", \"m0069\": \"1\" }", "Referral Demographics: { \"first_name\": \"<PERSON>\", \"middle_name\": \"<PERSON><PERSON>\", \"last_name\": \"<PERSON>\", \"date_of_birth\": \"11/18/1929\", \"gender\": \"1\" }"], "rationale": "Name and DOB mismatch between SOC and available referral data. Rating is '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Name and DOB mismatch.\", \"alt_rationale\": \"Referral lists name as <PERSON> and DOB as 11/18/1929.\", \"alt_answer\": \"{ \\\\\"m0040\\\\\": \\\\\"<PERSON>\\\\\", \\\\\"m0066\\\\\": \\\\\"11/18/1929\\\\\", \\\\\"m0069\\\\\": \\\\\"1\\\\\" }\" }"}, {"context": ["SOC Patient Details: { \"m0040\": \"Daisy Duck\", \"m0066\": \"01/01/1940\", \"m0069\": \"2\" }", "Referral Demographics: { \"first_name\": \"<PERSON>\", \"middle_name\": \"\", \"last_name\": \"<PERSON>\", \"date_of_birth\": \"01/01/1940\", \"gender\": \"\" }"], "rationale": "Name and DOB match the available referral data. Gender is missing in the referral. Since there are no mismatches in the comparable data, the rating is '1'. The reason notes the missing data.", "answer": "{ \"rating\": \"1\", \"reason\": \"Name and DOB match referral data. Referral gender missing, unable to verify.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Patient Details: { \"m0040\": \"Goofy Goof\", \"m0066\": \"05/25/1932\", \"m0069\": \"1\" }", "Referral Demographics: { \"first_name\": \"\", \"middle_name\": \"\", \"last_name\": \"\", \"date_of_birth\": \"\", \"gender\": \"\" }"], "rationale": "No referral data is available to compare against. Therefore, no mismatches can be confirmed. The rating is '1', but the reason clarifies the lack of verification.", "answer": "{ \"rating\": \"1\", \"reason\": \"No comparable Name, DOB, or Gender data available in referral to verify SOC entry.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}], "options": [], "description": "Introduce yourself, your discipline, your agency.  Let them know you will be recording the visit to help you create your notes.", "dependencies": [], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_PATIENT_LIVING_SITUATION", "name": "QA_Patient Living Situation", "id": 8260, "types": ["SOC_CHECKLIST"], "sections": [31], "source": "tasks", "source_id": "249", "schema": {}, "mandatory": false, "notes": "Compare:\n\nSOC M1100 (A vs B vs C) vs. Ref living_situation description.\n\nSOC M2102F vs. Ref supports description (Presence/absence and nature of caregiver).\nFocus on alignment between SOC selections and referral narrative social context. Rate '0' for clear contradictions based on available referral info.", "enabled": true, "question": "Review the SOC 'Patient Living Situation' section (M1100 Living Situation - Alone/With Others/Congregate, and level of assistance; M2102F Type/Source of Assistance). Compare this against the available information documented in the referral's 'SOCIAL_ENVIRONMENTAL_CONTEXT_REFERRAL' field (living_situation, supports). Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if the SOC M1100 selection (Alone/With Others/Congregate) and M2102F (reflecting caregiver presence/ability) are generally consistent with the referral's description of living_situation and supports, OR if referral social/environmental data is missing preventing comparison, but no mismatches are found in the available data.\n\nAssign '0' (Incorrect/Issue) if there is a significant conflict between the SOC M1100/M2102F selections and the available referral description of the living situation or support system (e.g., SOC M1100 indicates patient lives alone, referral says lives with spouse; SOC M2102F indicates no caregiver available, referral identifies spouse as caregiver).\n\nreason (Text String):\n\nIf rating is '1', state that the living situation and support source are consistent with available referral data, noting any key referral context missing (e.g., \"M1100 and M2102F consistent with referral social context.\", \"Consistent; referral missing details on caregiver availability.\").\n\nIf rating is '0', state all specific significant discrepancies found (e.g., 'M1100 mismatch: SOC indicates living alone, referral states lives with spouse.', 'M2102F inconsistency: SOC indicates no caregiver, referral identifies daughter as support.').\n\nalt_rationale (Text String): If rating is '0' due to a discrepancy, explain the expected status based on the referral documentation for the first or most significant discrepancy (e.g., 'Referral Social & Env Context states patient lives with spouse.', 'Referral identifies spouse as primary caregiver, suggesting M2102F should be 1.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', provide the expected JSON snippet for the specific field(s) identified as discrepant in the alt_rationale based on referral data (e.g., '{ \"m1100\": { \"m1100b\": \"...\" } }' (indicate \"lives with others\"), '{ \"m2102f\": \"1\" }'). Combine fields if appropriate. Leave blank if rating is '1'.", "examples": [{"context": ["SOC: { \"m1100\": { \"m1100a\": \"04\" }, \"m2102f\": \"1\" } (Lives Alone, Occasional Assist; Non-agency CG provides assist)", "Referral Social Env: { \"living_situation\": \"Lives alone in single-family home\", \"supports\": \"Daughter visits couple times a week\", ... }"], "rationale": "SOC selections (Lives Alone, Non-agency CG provides assist) align with referral description.", "answer": "{ \"rating\": \"1\", \"reason\": \"Living Situation (M1100) and Support Source (M2102F) consistent with referral social context.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC: { \"m1100\": { \"m1100a\": \"05\" }, ... } (Lives Alone, No Assist Available)", "Referral Social Env: { \"living_situation\": \"Lives with wife in condo\", \"supports\": \"Wife available for support\", ... }"], "rationale": "SOC M1100 indicates patient lives alone, which conflicts with referral stating patient lives with wife. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"M1100 mismatch: SOC indicates living alone, referral states lives with spouse.\", \"alt_rationale\": \"Referral Social & Env Context states patient lives with wife, suggesting M1100B should be selected.\", \"alt_answer\": \"{ \\\\\"m1100\\\\\": { \\\\\"m1100b\\\\\": \\\\\"Specify Assistance Level\\\\\" } }\" }"}], "options": [], "description": "Verbalize patient living situation     \n <PERSON><PERSON> Lives Alone     \n <PERSON><PERSON>  Patient Lives with other person(s) in the home     \n C<PERSON>  Patient lives in Congregate Situation (for example assisted living, residential care home.)", "dependencies": [{"code": "SOCIAL_ENVIRONMENTAL_CONTEXT_REFERRAL", "source": "referral", "name": "Social & Environmental Context", "question": "Extract information regarding the patient's social and environmental context, including their living situation, available support systems or caregivers, any known home safety concerns, and transportation needs or limitations, as described in the provided referral documentation.", "description": " Social & Environmental Context"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_MEDICATIONS", "name": "QA_Medications", "id": 8294, "types": ["SOC_CHECKLIST"], "sections": [29], "source": "tasks", "source_id": "234", "schema": {"type": "object", "properties": []}, "mandatory": false, "notes": "Compare SOC MEDICATIONS list against Ref MEDICATIONS_REFERRAL list. Focus on significant discrepancies:\n\nMissing critical meds (anticoagulants, insulin, cardiac, psychotropics, etc.).\n\nUnexpected new potent meds.\n\nMajor dose/route/frequency changes for key meds.\nMinor updates (e.g., adding OTC Tylenol discussed during visit) or clearly documented D/C orders are generally acceptable for a '1' rating. Rate '0' for unexplained major inconsistencies requiring follow-up.", "enabled": true, "question": "Review the list of medications documented during the SOC visit ('MEDICATIONS'). Compare this list against the medication list provided in the referral documentation ('MEDICATIONS_REFERRAL'). Identify any significant discrepancies, such as added medications, omitted medications, or major changes in dose, route, or frequency for key medications. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if the SOC medication list appears reasonably consistent with the referral list, allowing for expected minor updates or PRN changes documented during the visit. This means no critical medications seem to be omitted without explanation, and no unexpected potent medications were added without context.\n\nAssign '0' (Incorrect/Issue) if there are significant discrepancies that require clarification or correction, such as:\n\nOmission of a critical medication listed on the referral (e.g., anticoagulant, insulin, key cardiac med) without documented reason (like D/C order).\n\nAddition of a potent or unexpected medication on the SOC list not mentioned on the referral without clear context.\n\nMajor, unexplained discrepancies in dose, route, or frequency for critical medications.\n\nreason (Text String):\n\nIf rating is '1', state that the SOC medication list is reasonably reconciled with the referral list, noting any minor, explained changes or if referral list was unavailable (e.g., \"SOC med list consistent with referral, includes new PRN Tylenol documented.\", \"Reasonable reconciliation. Referral med list was missing.\").\n\nIf rating is '0', state all specific significant discrepancies found (e.g., 'Missing Warfarin from SOC list (present on referral).', 'New medication (Oxycodone) added on SOC list, not on referral.', 'Significant dose discrepancy for Metoprolol (SOC 100mg BID, Ref 25mg BID).').\n\nalt_rationale (Text String): If rating is '0', explain the expected medication or correction based on the referral documentation or standard reconciliation principles for the first or most significant discrepancy (e.g., 'Referral lists Warfarin 5mg daily.', 'Addition of Oxycodone needs clinical justification or confirmation.', 'Referral dose for Metoprolol was 25mg BID.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', provide the expected correction or query needed for the medication list. This might be descriptive rather than a JSON snippet due to the complexity (e.g., '\"Add Warfarin 5mg PO Daily to SOC list.\"', '\"Clarify order/reason for new Oxycodone.\"', '\"Verify Metoprolol dose; referral shows 25mg BID.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Meds: [ { \"name\": \"Lisinopril\", \"dose\": \"20\", \"strength\": \"mg\", ... }, { \"name\": \"Metformin\", \"dose\": \"1000\", \"strength\": \"mg\", ... }, { \"name\": \"Tylenol\", \"dose\": \"650\", \"strength\": \"mg\", \"frequency\": \"Q6H PRN pain\", ... } ] (Tylenol added during visit discussion)", "Referral Meds: [ { \"name\": \"Lisinopril\", \"dose\": \"20\", \"strength\": \"mg\", ... }, { \"name\": \"Metformin\", \"dose\": \"1000\", \"strength\": \"mg\", ... } ]"], "rationale": "SOC list includes all referral meds and adds a reasonable PRN OTC med discussed during the visit. No significant unexplained discrepancies. Rating '1'.", "answer": "", "assessment": "{ \"rating\": \"1\", \"reason\": \"SOC med list consistent with referral, includes PRN Tylenol documented during visit.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Meds: [ { \"name\": \"Lisinop<PERSON>\", ... } ]", "Referral Meds: [ { \"name\": \"Lisinopril\", ... }, { \"name\": \"Warfarin\", \"dose\": \"5\", \"strength\": \"mg\", ... } ]"], "rationale": "Critical medication (Warfarin) listed on referral is missing from the SOC list without explanation. Rating '0'.", "answer": "", "assessment": "{ \"rating\": \"0\", \"reason\": \"Missing Warfarin from SOC list (present on referral).\", \"alt_rationale\": \"Referral lists Warfarin 5mg daily. This omission needs clarification.\", \"alt_answer\": \"\\\"Add Warfarin 5mg PO Daily to SOC list or document reason for omission.\\\"\" }"}], "options": [], "description": "Verbalize the patient's allergies:     \n     \n - Do they have any?     \n - If so, what are they?", "dependencies": [{"code": "MEDICATIONS_REFERRAL", "source": "referral", "name": "Medications", "question": "Extract the list of medications the patient is currently taking, including details such as name, dose, strength, route, frequency, start/end dates, status, purpose, and any special instructions, as found in the provided referral documentation.", "description": "Medications & Allergies"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_Balance_Assessments", "name": "QA_Balance Assessments ", "id": 8322, "types": ["SOC_CHECKLIST"], "sections": [39], "source": "tasks", "source_id": "346", "schema": {"type": "object", "properties": [null, null, null, null, null, null, null, null, null, {"name": "rating", "label": "Rating", "type": "select", "required": false, "params": {"placeholder": "", "model": {"question": "", "criteria": "Respond with one of the following options (option value only, for example - '1'):\n1 for 1\n2 for 0", "notes": "", "useValue": false, "examples": [], "enabled": false, "sources": [], "directSource": ""}, "conditionals": {"conditions": [], "order": ""}, "sources": [], "options": [{"key": "rating-1", "label": "1", "value": "1"}, {"key": "rating-2", "label": "0", "value": "0"}]}}, {"name": "reason", "label": "Reason", "type": "string", "required": false, "params": {"placeholder": "", "model": {"question": "", "criteria": "", "notes": "", "useValue": false, "examples": [], "enabled": false, "sources": [], "directSource": ""}, "conditionals": {"conditions": [], "order": ""}, "sources": []}}, {"name": "alt_rationale", "label": "Alternative Rationale", "type": "string", "required": false, "params": {"placeholder": "", "model": {"question": "", "criteria": "", "notes": "", "useValue": false, "examples": [], "enabled": false, "sources": [], "directSource": ""}, "conditionals": {"conditions": [], "order": ""}, "sources": []}}, {"name": "alt_answer", "label": "Alternative Answer", "type": "string", "required": false, "params": {"placeholder": "", "model": {"question": "", "criteria": "", "notes": "", "useValue": false, "examples": [], "enabled": false, "sources": [], "directSource": ""}, "conditionals": {"conditions": [], "order": ""}, "sources": []}}]}, "mandatory": false, "notes": "Focus on:\n\nPresence of any standardized balance test if fall risk indicated elsewhere.\n\nCompleteness of documentation if a specific test (MAHC 10, <PERSON>etti, BERG) is claimed to be done.\nThis QA does not typically validate the accuracy of the scores themselves, just the documentation practice.", "enabled": true, "question": "Review the SOC 'Balance Assessments' section, checking the documentation for TUG, MAHC 10, Tinetti, and/or BERG tests.\n\nIs at least one standardized balance assessment documented if fall risk factors are present (e.g., from M1033 Hospitalization Risk, history of falls, mobility issues)?\n\nIf MAHC 10 is documented, are the individual risk factors completed?\n\nIf Tinetti or BERG scores are documented, are the component scores present? (Full calculation validation is complex and likely outside scope here).\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if balance assessment documentation appears appropriate for the patient context. This means either:\n\nNo significant fall risk identified elsewhere, and balance assessments may be minimal or skipped.\n\nFall risk is identified, and at least one standardized assessment (TUG, MAHC 10, Tinetti, BERG) is documented with reasonable completeness.\n\nAssign '0' (Incorrect/Issue) if:\n\nSignificant fall risk factors are identified elsewhere (e.g., M1033, M1860 indicating impairment, history), but no standardized balance assessment (TUG, MAHC 10, Tinetti, BERG) is documented.\n\nMAHC 10, Tinetti, or BERG is indicated as performed, but the necessary component fields are largely empty without explanation.\n\nreason (Text String):\n\nIf rating is '1', state that balance assessment documentation is appropriate/present given the clinical context (e.g., \"Appropriate balance assessment (TUG) documented given fall risk.\", \"No significant fall risk identified; minimal balance assessment documented.\").\n\nIf rating is '0', state the specific issue (e.g., 'Missing standardized balance assessment despite identified fall risk factors (history of falls, gait instability).', 'MAHC 10 documented but component risk factors not completed.', 'BERG total score documented, but component scores missing.').\n\nalt_rationale (Text String): If rating is '0', explain the expected documentation (e.g., 'Standardized balance test (e.g., TUG, Berg) needed due to identified fall risk.', 'MAHC 10 requires completion of individual risk factor questions.', 'If BERG performed, component scores should be documented.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Perform and document a standardized balance assessment.\"', '\"Complete MAHC 10 component questions.\"', '\"Document BERG component scores or remove total score if not performed.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Balance: { \"tug\": 14, \"mahc_10_assessment\": { \"age_65_plus\": \"1\", \"prior_history_of_falls_within_3_months\": \"1\", ... } } (TUG and MAHC 10 documented)", "SOC Outlook: { \"m1033\": { \"1_history_of_falls\": \"1\", ... } } (History of falls noted)"], "rationale": " Fall risk identified (M1033), and standardized tests (TUG, MAHC 10) are documented. Assuming MAHC 10 components are filled. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Appropriate balance assessments (TUG, MAHC 10) documented given identified fall risk.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Balance: { \"tug\": null, \"mahc_10_assessment\": {}, \"tinetti_balance_assessment\": {}, \"berg\": {} } (No standardized tests documented)", "SOC M1800s: { \"m1860\": \"4\", ... } (Walks only with supervision)", "SOC Outlook: { \"m1033\": { \"1_history_of_falls\": \"1\", ... } } (History of falls)"], "rationale": "Significant fall risk factors present (history of falls, impaired ambulation), but no standardized balance assessment documented. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Missing standardized balance assessment despite identified fall risk factors (history of falls, impaired ambulation).\", \"alt_rationale\": \"Standardized balance test (e.g., TUG, Berg, MAHC 10) should be performed and documented due to identified fall risk.\", \"alt_answer\": \"\\\"Perform and document a standardized balance assessment.\\\"\" }"}], "options": [], "description": "When I say “Go,” I want you to:\n\n1. Stand up from the chair.\n2. Walk to the line on the floor at your normal pace.\n3. Turn.\n4. Walk back to the chair at your normal pace.\n5. Sit down again.\n\nAn older adult who takes 12 seconds to complete the TUG is at risk for falling.", "dependencies": [{"code": "Patient_outlook_planning", "source": "tasks"}, {"code": "ADLs/IADLs_M1800s", "source": "tasks"}, {"code": "GG0170", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_CARDIOVASCULAR_ASSMNT", "name": "QA_Cardiovascular", "id": 8323, "types": ["SOC_CHECKLIST", "SOC-DEV"], "sections": [681, 33], "source": "tasks", "source_id": "1978", "schema": {"type": "object", "properties": [null, null, null, null, null, null, null, null, null, {"name": "rating", "label": "Rating", "type": "select", "required": false, "params": {"placeholder": "", "model": {"question": "", "criteria": "Respond with one of the following options (option value only, for example - '1'):\n1 for 1\n2 for 0", "notes": "", "useValue": false, "examples": [], "enabled": false, "sources": [], "directSource": ""}, "conditionals": {"conditions": [], "order": ""}, "sources": [], "options": [{"key": "rating-1", "label": "1", "value": "1"}, {"key": "rating-2", "label": "0", "value": "0"}]}}, {"name": "reason", "label": "Reason", "type": "string", "required": false, "params": {"placeholder": "", "model": {"question": "", "criteria": "", "notes": "", "useValue": false, "examples": [], "enabled": false, "sources": [], "directSource": ""}, "conditionals": {"conditions": [], "order": ""}, "sources": []}}, {"name": "alt_rationale", "label": "Alternative Rationale", "type": "string", "required": false, "params": {"placeholder": "", "model": {"question": "", "criteria": "", "notes": "", "useValue": false, "examples": [], "enabled": false, "sources": [], "directSource": ""}, "conditionals": {"conditions": [], "order": ""}, "sources": []}}, {"name": "alt_answer", "label": "Alternative Answer", "type": "string", "required": false, "params": {"placeholder": "", "model": {"question": "", "criteria": "", "notes": "", "useValue": false, "examples": [], "enabled": false, "sources": [], "directSource": ""}, "conditionals": {"conditions": [], "order": ""}, "sources": []}}]}, "mandatory": false, "notes": "Check:\n\nInternal Consistency: SOC summary vs. SOC detailed CV findings. WNL->Problem->Need linkage. anticoagulants_antiplatelets vs. SOC MEDICATIONS. pulses vs. SOC VITALS_ASSMNT.\n\nReferral Context: SOC findings vs. Ref active_problems_summary.cardiovascular, Ref Dx, Ref Meds (anticoagulants), Ref History (devices).\nRate '0' for internal inconsistencies OR significant contradictions with available referral context.", "enabled": true, "question": "Review the SOC 'Cardiovascular' assessment findings (pulses, implanted_device, anticoagulants_antiplatelets, chest_pain, edema, skin, calf_tenderness) and the summary object (wnl, active_problems, care_plan_needs).\n\nAssess internal consistency: Does summary.wnl reflect detailed findings? If not WNL, are active_problems and care_plan_needs documented? Is anticoagulants_antiplatelets consistent with SOC MEDICATIONS? Are pulse findings consistent with SOC VITALS_ASSMNT?\n\nCompare against relevant available referral context (Referral active_problems_summary.cardiovascular, diagnoses, medications esp. anticoagulants, history for devices).\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all of the following are true:\n\nThe SOC summary.wnl status is consistent with the detailed cardiovascular findings (e.g., not '1' if significant edema, chest pain, irregular pulse, abnormal skin documented).\n\nIf summary.wnl is '2' (No), then summary.active_problems is not empty/blank.\n\nIf summary.active_problems is not empty, then summary.care_plan_needs is not empty/blank.\n\nThe documented anticoagulants_antiplatelets aligns with relevant meds in the SOC MEDICATIONS list.\n\nThe documented findings and summary do not significantly contradict relevant cardiovascular information noted in the available referral context.\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nInternal inconsistency in the summary (WNL status vs. findings, missing problem, or missing care need).\n\nInternal inconsistency between anticoagulants_antiplatelets and SOC MEDICATIONS.\n\nSignificant discrepancy between pulses.apical_pulse rhythm and VITALS_ASSMNT pulse rhythm.\n\nA significant cardiovascular issue or device noted in the referral is seemingly ignored or contradicted in the SOC Cardiovascular assessment without clear rationale.\n\nreason (Text String):\n\nIf rating is '1', state that the Cardiovascular assessment and summary are internally consistent and align with available referral context, noting missing referral context if applicable (e.g., \"CV assessment and summary internally consistent and align with referral context.\", \"Internally consistent. Referral context for CV status missing.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Inconsistency: CV marked WNL despite documented edema.', 'Inconsistency: CV not WNL, but no active problem documented.', 'Inconsistency: Active problem (Edema) documented, but no care plan need listed.', 'Discrepancy: Anticoagulant (Warfarin) listed in SOC Meds but not mentioned in anticoagulants_antiplatelets field.', 'Discrepancy: Referral notes history of pacemaker, but SOC implanted_device is None.').\n\nalt_rationale (Text String): If rating is '0', explain the expected action or correction for the first or most significant discrepancy (e.g., 'Documented edema indicates summary.wnl should be 2.', 'Active problem needs to be specified for edema.', 'Care plan need required for Edema.', 'Anticoagulant Warfarin should be documented.', 'Pacemaker noted in referral history; implanted_device should be 2.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Set summary.wnl to 2 (No).\"', '\"Add specific active problem for CV.\"', '\"Add care plan need for Edema management.\"', '\"Update anticoagulants_antiplatelets field to include Warfarin.\"', '\"Set implanted_device to 2 (Pacemaker).\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC CV WNL, No relevant SOC meds, SOC Vitals regular, Referral clear"], "rationale": "SOC findings WNL, summary consistent. No anticoagulants. Apical pulse matches Vitals. Aligns with referral. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Cardiovascular assessment and summary internally consistent and align with available referral context.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC CV has edema, but summary marked WNL"], "rationale": " Internal inconsistency: summary.wnl is '1' despite documented edema. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Inconsistency: CV marked WNL despite documented edema.\", \"alt_rationale\": \"Documented edema indicates summary.wnl should be 2 (No).\", \"alt_answer\": \"\\\"Set summary.wnl to 2 (No) and document active problem/care plan need for edema.\\\"\" }"}], "options": [], "description": "Interview/examine/verbalize:     \n     \n Are findings WNL?      \n (Consider  symptoms, medications, skilled needs, care plan.)     \n If not, describe…       \n - Apical / Peripheral Pulses      \n - Implanted devices      \n - Anticoagulant / Antiplatelets     \n - Chest pain     \n - Edema and fluid retention      \n - Skin color     \n - Skin temperature     \n - Calf tenderness     \n - Home blood pressure, weight monitoring     \n - Active problems and skilled needs", "dependencies": [{"code": "ORDERS_REFERRAL", "source": "referral", "name": "Orders", "question": "Extract the specific services ordered, including the frequency and duration if specified, for Nursing, Physical Therapy, Occupational Therapy, Speech Therapy, Medical Social Work, and Home Health Aide. Also, capture any specified clinical parameters to monitor (e.g., vital sign ranges to report).", "description": "Specific Service Orders"}, {"code": "diagnoses_treatments", "source": "referral", "name": "Medical/Surgical History", "question": "Extract details about the patient's past surgical history and any relevant ongoing special treatments, procedures, or programs (like IV therapy, dialysis, oxygen, etc.) mentioned in the provided referral documentation.", "description": "Medical/Surgical History"}, {"code": "MEDICATIONS_SOC", "source": "tasks"}, {"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "VITALS_ASSMNT", "source": "tasks"}, {"code": "diagnoses_REFERRAL", "source": "referral", "name": "Diagnoses", "question": "Extract and structure the patient's primary and other diagnoses, including descriptions, ICD-10 codes, and symptom control ratings if available, as found in the referral documentation.", "description": "Diagnoses"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GG0170N1", "name": "4 Steps", "id": 2571, "types": ["SOC_CHECKLIST"], "sections": [35], "source": "tasks", "source_id": "296", "schema": {}, "mandatory": false, "notes": "To QA the patient's ability with 4 steps (current and discharge goal), we should look at related mobility/functional assessments, homebound status details, assistive devices used, fall risk, and overall musculoskeletal status since these would impact stair climbing ability", "enabled": true, "question": "Based on the OASIS E item code GG0170N1; Does the patient have the ability to go up and down four steps with or without a rail?", "inputType": "text", "criteria": "1. Compare 4-step ability (GG0170N1/N2) against 1-step ability (GG0170M1/M2) and 12-step ability (GG0170O1/O2) - scores should be logically consistent (4-step score cannot be better than 1-step score)\n2. Verify alignment with homebound status and criteria - severe mobility limitations noted in homebound documentation should align with poor stair performance\n3. Check musculoskeletal status and devices used - limitations and required devices should match reported stair ability\n4. Review environmental factors like home entry steps and handrails - ability should align with home setup\n5. Validate against other functional mobility scores (M1850, M1860) for overall consistency in reported mobility levels\n6. Current performance (N1) should not be better than baseline status reported in other assessments\n7. Discharge goal (N2) should be realistic given current status and barriers noted", "examples": [], "options": [], "description": "The ability to go up and down four steps with or without a rail. If SOC/ROC performance is coded 07, 09, 10 or 88, skip to GG0170P.", "dependencies": [{"code": "HOMEBOUND_CRITERIA_1", "source": "referral"}, {"code": "GG0170M1", "source": "tasks"}, {"code": "GG0170M2", "source": "tasks"}, {"code": "GG0170O1", "source": "tasks"}, {"code": "GG0170O2", "source": "tasks"}, {"code": "HOMEBOUND_STATUS", "source": "tasks"}, {"code": "MUSCULOSKELETAL_STATUS", "source": "tasks"}, {"code": "MUSCULOSKELETAL_DEVICE", "source": "tasks"}, {"code": "FUNCTIONAL_STAIRS", "source": "tasks"}, {"code": "HOME_ENTRY_STEPS", "source": "tasks"}, {"code": "HOME_ENTRY_HANDRAIL", "source": "tasks"}, {"code": "MAHC_10_ASSESSMENT", "source": "tasks"}, {"code": "GG0110", "source": "tasks"}, {"code": "M1800", "source": "tasks"}, {"code": "M1850", "source": "tasks"}, {"code": "M1860", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GG0170R1", "name": "Wheel 50 Feet with 2 Turns", "id": 2587, "types": ["SOC_CHECKLIST"], "sections": [35], "source": "tasks", "source_id": "300", "schema": {}, "mandatory": false, "notes": "If GG0170R is coded for partial or extensive assistance, M1860 should reflect similar limitations, especially if the patient is unable to ambulate independently in the home or community. Assistance with wheeling 50 feet and making turns often indicates mobility limitations.\r\n\r\nIf GG0170Q is coded 0 - No, skip to M1600. the rest of the mobility items assessing use of wheelchair / scooter should not have a response.\r\n\r\nCorrelate the level of performance coded in  M1860 with GG0170R i.e if M1860 is coded 6 or 7 for unable to ambulate, then GG0170R should be coded 1 for patient is dependent.", "enabled": true, "question": "Based on the OASIS E item code GG0170R1; Once seated in wheelchair/scooter, the ability to wheel at least 50 feet and make two turns?", "inputType": "select", "criteria": "Respond with one of the following options (option value only, for example - '1'):\n06 for Independent – Patient completes the activity by him/herself with no assistance from a helper.\n05 for Setup or clean-up assistance – Helper sets up or cleans up; patient completes activity. Helper assists only prior to or following the  activity.\n04 for Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient  completes activity. Assistance may be provided throughout the activity or intermittently.\n03 for Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less  than half the effort.\n02 for Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than  half the effort.\n01 for Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more  helpers is required for the patient to complete the activity.\n07 for Patient refused\n09 for Not applicable – Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury.\n10 for Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)\n88 for Not attempted due to medical conditions or safety concerns", "examples": [{"context": ["Wheeling 50 feet with 2 turns is currently at a 5"], "answer": "05"}, {"context": ["Wheeling 50 feet with 2 turns currently requires set up or clean up assistance"], "answer": "05"}], "options": [{"id": "GG0170R1-0", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by him/herself with no assistance from a helper. "}, {"id": "GG0170R1-1", "value": "05", "label": "05 - Setup or clean-up assistance – Helper sets up or cleans up; patient completes activity. Helper assists only prior to or following the  activity.  "}, {"id": "GG0170R1-2", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient  completes activity. Assistance may be provided throughout the activity or intermittently.  "}, {"id": "GG0170R1-3", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less  than half the effort. "}, {"id": "GG0170R1-4", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than  half the effort.  "}, {"id": "GG0170R1-5", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more  helpers is required for the patient to complete the activity. "}, {"id": "GG0170R1-6", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "GG0170R1-7", "value": "09", "label": "09 - Not applicable – Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury. "}, {"id": "GG0170R1-8", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "GG0170R1-9", "value": "88", "label": "88 - Not attempted due to medical conditions or safety concerns"}], "description": "Once seated in wheelchair/scooter, the ability to wheel at least 50 feet and make two turns.", "dependencies": [{"code": "DME", "source": "tasks"}, {"code": "M1850", "source": "tasks"}, {"code": "M1860", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GG0170O1", "name": "12 Steps", "id": 2575, "types": ["SOC_CHECKLIST"], "sections": [35], "source": "tasks", "source_id": "297", "schema": {}, "mandatory": false, "notes": "For assessing 12 step capability and goals, we need to look at related mobility assessments, fall risks, current functional status, and other stair-related assessments to ensure consistency and clinical reasonableness. The current performance (O1) and discharge goal (O2) should align with the patient's overall functional trajectory.", "enabled": true, "question": "Based on the OASIS E item code GG0170O1; Does the patient have the ability to go up and down 12 steps with or without a rail?", "inputType": "text", "criteria": "1. Compare 12 step ability (O1) with 4 step ability (N1) - 12 step score should not be better than 4 step score\n2. Compare discharge goals (O2 vs N2) - goals should align with progression\n3. Check single step/curb ability (M1/M2) as baseline\n4. Verify consistency with overall mobility/ambulation status (M1850, M1860)\n5. Cross-reference fall risk assessments and balance/gait evaluations\n6. Confirm goals align with documented functional limitations and permitted activities\n7. Verify assistive devices needed align with stair capabilities\n8. Check homebound status rationale matches mobility capabilities", "examples": [], "options": [], "description": "The ability to go up and down 12 steps with or without a rail.", "dependencies": [{"code": "GG0170N1", "source": "tasks"}, {"code": "GG0170N2", "source": "tasks"}, {"code": "GG0170M1", "source": "tasks"}, {"code": "GG0170M2", "source": "tasks"}, {"code": "M1850", "source": "tasks"}, {"code": "M1860", "source": "tasks"}, {"code": "FUNCTIONAL_STAIRS", "source": "tasks"}, {"code": "MAHC_10_ASSESSMENT", "source": "tasks"}, {"code": "HOMEBOUND_STATUS", "source": "tasks"}, {"code": "MUSCULOSKELETAL_BALANCE", "source": "tasks"}, {"code": "MUSCULOSKELETAL_GAIT", "source": "tasks"}, {"code": "DME", "source": "tasks"}, {"code": "485_POC_18_A", "source": "tasks"}, {"code": "485_POC_18_B", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_HOME_SAFETY", "name": "QA_Home Safety Assessment", "id": 3401, "types": ["SOC_CHECKLIST"], "sections": [39], "source": "tasks", "source_id": "351", "schema": {}, "mandatory": false, "notes": "Check:\n\nInternal Consistency: summary vs. documented hazards. WNL->Problem->Need linkage.\n\nContextual Consistency: SOC findings vs. Ref SOCIAL_ENVIRONMENTAL_CONTEXT_REFERRAL.home_safety. SOC findings vs. patient functional status/DME from Mobility_Safety_Aid, M1860, GG0170.\nRate '0' for internal inconsistencies OR significant contradictions/omissions based on available context.", "enabled": true, "question": "Review the SOC 'Home Safety Assessment' findings (hazards identified across categories: Structural, Electrical, Fire, Bathroom, Communication, Infection Control) and the summary object (home_safety_wnl, home_safety_active_problems, home_safety_care_plan_needs).\n\nAssess internal consistency: Does summary.home_safety_wnl reflect the presence/absence of identified hazards? If home_safety_wnl=No, are active_problems and care_plan_needs documented and relevant to the identified hazards?\n\nCompare against relevant available referral context (Referral SOCIAL_ENVIRONMENTAL_CONTEXT_REFERRAL.home_safety) and SOC functional/DME context (Mobility_Safety_Aid). Are findings consistent?\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all of the following are true:\n\nThe SOC summary.home_safety_wnl status is consistent with the specific hazards documented (i.e., not '1' if significant hazards like throw rugs, poor lighting, lack of grab bars for an unsteady patient are noted).\n\nIf home_safety_wnl is '2' (No), then home_safety_active_problems and home_safety_care_plan_needs are appropriately documented and relate to identified hazards.\n\nThe documented assessment doesn't significantly contradict major safety concerns noted in the available referral context or implied by the patient's functional status/DME use.\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nInternal inconsistency in the summary (WNL status vs. identified hazards, missing problem, or missing care need for identified hazard).\n\nA significant safety concern noted in the referral context is seemingly ignored or contradicted in the SOC assessment.\n\nObvious safety needs suggested by functional status/DME (e.g., walker use but no check for throw rugs/clear paths) are not addressed.\n\nreason (Text String):\n\nIf rating is '1', state that the Home Safety assessment and summary are internally consistent and align with available referral/functional context, noting missing referral context if applicable (e.g., \"Home Safety assessment and summary internally consistent and align with context.\", \"Internally consistent. Referral context for home safety missing.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Inconsistency: WNL=Yes despite documented throw rugs and poor lighting.', 'Inconsistency: Hazards identified (throw rugs), but no active problem or care plan need documented.', 'Discrepancy: Referral noted lack of grab bars as safety concern, but not addressed in SOC assessment.', 'Potential Omission: Patient uses walker, but assessment doesn't address pathway clearance/throw rugs.').\n\nalt_rationale (Text String): If rating is '0', explain the expected correction for the first or most significant discrepancy (e.g., 'Presence of throw rugs/poor lighting means WNL should be No.', 'Active problem/care need required for identified throw rug hazard.', 'Assessment should address grab bar status noted in referral.', 'Assessment should include evaluation of fall hazards like rugs for walker user.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Set home_safety_wnl to 2 (No).\"', '\"Add active problem/care plan need related to throw rugs.\"', '\"Update assessment to include grab bar status.\"', '\"Assess for and document pathway clearance/throw rugs.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Home Safety: { \"structural_hazards\": [], \"electrical_hazards\": [], \"fire_safety\": [\"1\"], \"bathroom\": [\"4\",\"5\"], \"communication_sensory\": [\"2\"], \"infection_control_public_health\": [\"1\",\"2\",\"3\"], \"summary\": { \"home_safety_wnl\": \"1\", \"home_safety_active_problems\": \"\", \"home_safety_care_plan_needs\": \"\" } } (No hazards identified, summary WNL=1)", "Referral Social Env: { \"home_safety\": \"No known concerns\", ... }", "SOC Function: (Patient independent)"], "rationale": " No hazards identified in SOC assessment, summary consistent (WNL=1). Aligns with referral context and patient's independence. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Home Safety assessment and summary internally consistent and align with available referral/functional context.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Home Safety: { \"structural_hazards\": [\"6\"], \"electrical_hazards\": [\"2\"], ..., \"summary\": { \"home_safety_wnl\": \"1\", ... } } (Throw rugs, cord hazard identified, but summary WNL=1)", "Referral: (Assume no conflicting info)"], "rationale": " Internal inconsistency: home_safety_wnl is '1' despite documented hazards (throw rugs, cords). Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Inconsistency: WNL=Yes despite documented throw rugs and electrical cord hazards.\", \"alt_rationale\": \"Presence of identified hazards means home_safety_wnl should be 2 (No).\", \"alt_answer\": \"\\\"Set home_safety_wnl to 2 (No) and document active problems/care plan needs related to hazards.\\\"\" }"}], "options": [], "description": "Observe carefully throughout the visit and esp on OASIS WALK. Any YES should be addressed through asking patient to make temporary changes for their safety OR providing adapted instructions.       \n     \n Home Entry, Access to Community     \n - How many steps?     \n - Missing handrail?      \n - Uneven footing?     \n     \n Mobility     \n - Narrow halls and passageways, doorways?     \n - Cluttered halls and rooms?     \n - Throw rugs?     \n - Slippery footing?     \n - Change in flooring type/level?     \n - Insufficient number of lights/light bulbs too dim?     \n - Access to bedroom? Bathroom?      \n - Pets?     \n     \n Bathroom     \n - Night light in bathroom?     \n - Grab bars in bath/shower?     \n - Slippery footing, throw rugs?     \n - Hand held shower?     \n - Bench/seat in shower?     \n     \n Electrical & Fire     \n - Extension cord/overloaded outlets?     \n - Cords or barriers in walkways?     \n - Smoke detectors? Fire extinguisher?     \n - Smoking in home safely?     \n - Oxygen precautions aware?     \n     \n Communication & Sensory     \n - Has/keeps mobile/cell phone in pocket?     \n - Severe hearing or vision limitations esp. New?     \n - Hear doorbell, telephone?     \n     \n Public Health     \n - Infestations?     \n - Lack of refrigeration for IV medication?     \n - Lack of running water, hot water, flush toilet?", "dependencies": [{"code": "SOCIAL_ENVIRONMENTAL_CONTEXT_REFERRAL", "source": "referral", "name": "Social & Environmental Context", "question": "Extract information regarding the patient's social and environmental context, including their living situation, available support systems or caregivers, any known home safety concerns, and transportation needs or limitations, as described in the provided referral documentation.", "description": " Social & Environmental Context"}, {"code": "Mobility_Safety_Aid", "source": "tasks"}, {"code": "ADLs/IADLs_M1800s", "source": "tasks"}, {"code": "GG0170", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GASTRO_ASSMNT", "name": "QA_Gastrointestinal", "id": 8279, "types": ["SOC_CHECKLIST"], "sections": [33], "source": "tasks", "source_id": "284", "schema": {}, "mandatory": false, "notes": "", "enabled": true, "question": "", "inputType": "json", "criteria": "", "examples": [], "options": [], "description": "Assess:     \n - Food intake (incl. appetite and dietary restrictions)     \n - Fluid intake (incl. dietary restrictions)", "dependencies": [], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_C0500", "name": "score", "id": 2055, "types": ["SOC_CHECKLIST"], "sections": [32], "source": "tasks", "source_id": "259", "schema": {}, "mandatory": false, "notes": "", "enabled": true, "question": "Based on the OASIS E item code C0500; Add scores for questions C0200-C0400 and fill in total score (00-15)", "inputType": "calculation", "criteria": "Respond with the following options: A number in the format XX;\n 99 for the patient was unable to complete the interview;\n 13-15 for cognitively intact;\n8-12 for moderately impaired;\n0-7 for severe impairment", "examples": [{"context": ["C0500 score is 14"], "answer": "14"}], "options": [], "description": "Add scores for questions C0200-C0400 and fill in total score (00-15).      \n     \n Enter 99 if the patient was unable to complete the interview.     \n     \n 13-15:  Cognitively intact     \n 8-12:  Moderately impaired     \n 0-7:  Severe impairment", "dependencies": [], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_Mobility_Safety_Aid", "name": "QA_Mobility Safety & Aid", "id": 8281, "types": ["SOC_CHECKLIST"], "sections": [35], "source": "tasks", "source_id": "293", "schema": {}, "mandatory": false, "notes": "This QA focuses on internal consistency within the SOC assessment. Check alignment between:\n\nPOC 14 (DME) vs. POC 18B (Activities), M1860, GG0170.\n\nPOC 18A (Limitations) vs. POC 18B (Activities), M1860, GG0170.\n\nPOC 15 (Safety) vs. functional status/DME.\n\nSummary vs. detailed POC 18A/POC 14 findings.\nRate '0' for clear internal contradictions. Referral context is less directly applicable here unless clarifying a contradiction.", "enabled": true, "question": "Review the SOC 'Mobility Safety & Aid' section (485 POC 18A Functional Limitations, 485 POC 18B Activities Permitted, 485 POC 15 Safety Measures, 485 POC 14 DME/Supplies, and the Summary). Assess for internal consistency and alignment with other SOC functional assessments (ADLs/IADLs_M1800s, (GG0170) Mobility).\n\nDoes the DME listed (POC 14) align with the mobility level described in M1860/GG0170 and the Activities Permitted (POC 18B)?\n\nDo the Functional Limitations (POC 18A) and Activities Permitted (POC 18B) align with M1860/GG0170 mobility levels?\n\nAre Safety Measures (POC 15) appropriate given the functional status and DME?\n\nDoes the Summary object (wnl, active_problems, care_plan_needs) reflect the detailed findings?\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all components (POC 18A, POC 18B, POC 15, POC 14, Summary) are internally consistent with each other AND with the overall functional picture presented in M1800s/GG0170.\n\nAssign '0' (Incorrect/Issue) if there is at least one significant internal contradiction (e.g., POC 18B allows ambulation but M1860=Bedfast; POC 14 lists walker but POC 18B doesn't permit walker use; Summary WNL despite significant limitations/DME listed).\n\nreason (Text String):\n\nIf rating is '1', state that the Mobility Safety & Aid details are internally consistent and align with other functional assessments (e.g., \"Mobility Safety & Aid details internally consistent and align with M1800s/GG0170.\").\n\nIf rating is '0', state all specific significant inconsistencies found (e.g., 'Inconsistency: POC 18B (Activities Permitted) includes <PERSON>, but POC 14 (DME) does not list <PERSON>.', 'Inconsistency: POC 18A lists Ambulation limitation, but M1860 indicates Independent.', 'Inconsistency: Summary WNL=Yes, but POC 18A lists Paralysis and POC 14 lists Hospital Bed.').\n\nalt_rationale (Text String): If rating is '0', explain the expected correction for the first or most significant discrepancy based on the conflicting internal data (e.g., 'If <PERSON> is permitted (POC 18B), it should be listed in DME (POC 14).', 'Ambulation limitation in POC 18A contradicts M1860=0; clarify functional status.', 'Presence of significant limitations/DME means Summary WNL should be No.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Add Walker to POC 14 DME list.\"', '\"Review/correct M1860 and POC 18A based on patient status.\"', '\"Set Summary WNL to 2 (No) and document active problems/needs.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Mobility Safety Aid: { \"485_poc_18_a\": \"7\", \"485_poc_18_b\": \"B\", \"485_poc_15\": \"Fall precautions\", \"485_poc_14\": { \"dme\": { \"mobility_transfer\": \"6\" } }, \"mobility_safety_aid_suumary\": { \"mobility_safety_wnl\": \"2\", \"mobility_saftey_aid_active_problems\": \"Ambulation deficit, Fall risk\", \"mobility_safety_aid_care_plan_needs\": \"Use walker, fall px\" } } (Limitation=Ambulation, Activity=Walker, Safety=Fall Px, DME=Walker, Summary=Not WNL w/ problems/needs)", "SOC M1800s: { \"m1860\": \"3\", ... } (Walks w/ 2-handed device)", "SOC GG0170: { \"gg0170i\": \"03\", ... } (Walks w/ mod assist)"], "rationale": "All components are consistent: Limitation (Ambulation) matches Activity (Walker) and DME (Walker). M1860/GG0170 scores align with walker use. Safety measure is appropriate. Summary reflects the issues. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Mobility Safety & Aid details internally consistent and align with M1800s/GG0170.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Mobility Safety Aid: { ..., \"485_poc_18_b\": \"C\", \"485_poc_14\": { \"dme\": { \"mobility_transfer\": \"6\" } }, ... } (Activity=No Restrictions, DME=Walker)", "SOC M1800s/GG0170: (Assume they show independence)"], "rationale": " Internal inconsistency: Activities Permitted indicates No Restrictions, but DME lists a <PERSON>. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Inconsistency: POC 18B (Activities Permitted) is No Restrictions, but POC 14 (DME) lists <PERSON>.\", \"alt_rationale\": \"If a walker is required/used (POC 14), Activities Permitted (POC 18B) should reflect its use (e.g., select 'B') not 'No Restrictions'.\", \"alt_answer\": \"\\\"Correct POC 18B to reflect walker use, or remove walker from POC 14 if not used/needed.\\\"\" }"}], "options": [], "description": "Verbalize all that apply:     \n     \n 1  - Amputation     \n 2  - <PERSON><PERSON><PERSON> (Incontinence)     \n 3  - Contracture     \n 4  - Hearing     \n 5  - Paralysis     \n 6  - Endurance     \n 7  - Ambulation     \n 8  - Speech     \n 9  - Legally Blind     \n A  - Dyspnea With Minimal Exertion     \n B  - Other (Specify)", "dependencies": [{"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "ADLs/IADLs_M1800s", "source": "tasks"}, {"code": "GG0170", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GG0100", "name": "QA_(GG0100) Prior Functioning, Device Use & Transportation", "id": 8259, "types": ["SOC_CHECKLIST"], "sections": [30], "source": "tasks", "source_id": "244", "schema": {}, "mandatory": false, "notes": "Compare:\n\nSOC GG0100A-D vs. Ref baseline_functional_status (ADLs, mobility, assistance) & baseline_cognitive_status.\n\nSOC GG0110 vs. Ref baseline_functional_status.assistive_devices_used.\n\nSOC A1250 vs. Ref SOCIAL_ENVIRONMENTAL_CONTEXT_REFERRAL.transportation_needs_limitations & baseline_functional_status.summary.\nFocus on alignment between SOC categorical selections and referral narrative baseline description. Rate '0' for clear contradictions based on available referral baseline info.\n\n", "enabled": true, "question": "Review the SOC assessment of prior functioning (GG0100A Selfcare, GG0100B Indoor Mobility, GG0100C Stairs, GG0100D Functional Cognition), prior device use (GG0110), and transportation issues (A1250). Compare these against the available information documented in the referral's baseline_functional_status (mobility, ADLs, devices, assistance level, summary) and baseline_cognitive_status (summary, mental status) sections within the 'Functional, Cognitive & Body System Context_REFERRAL' field. Also consider referral's SOCIAL_ENVIRONMENTAL_CONTEXT_REFERRAL for transportation context. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if the SOC GG0100/GG0110/A1250 selections are generally consistent with the description of the patient's baseline functional, cognitive, and transportation status described in the referral documentation, OR if referral baseline data is missing preventing comparison, but no mismatches are found in the available data. Minor variations in interpretation between categorical selections (GG items) and narrative descriptions (referral) may be acceptable if the overall picture aligns.\n\nAssign '0' (Incorrect/Issue) if there is a significant conflict between the SOC GG0100/GG0110/A1250 selections and the available referral description of the patient's baseline status (e.g., SOC GG0100A shows Independent, referral baseline describes needing help with ADLs; SOC GG0110 shows No Device, referral baseline mentions prior walker use).\n\nreason (Text String):\n\nIf rating is '1', state that the prior function/device/transportation assessment is consistent with available referral baseline data, noting any key referral baseline sections missing (e.g., \"GG0100/GG0110 consistent with referral baseline functional status.\", \"Consistent; referral baseline cognitive status missing.\").\n\nIf rating is '0', state all specific significant discrepancies found (e.g., 'GG0100B (Indoor Mobility) inconsistency: SOC says Independent, referral baseline states needed help ambulating.', 'GG0110 mismatch: SOC says None, referral baseline mentions prior walker use.', 'A1250 inconsistency: SOC indicates No transport issues, referral baseline notes reliance on others.').\n\nalt_rationale (Text String): If rating is '0' due to a discrepancy, explain the expected status based on the referral documentation for the first or most significant discrepancy (e.g., 'Referral baseline function describes needing supervision for ambulation, suggesting GG0100B should be 2 (Needed Some Help).', 'Referral baseline mentions prior walker use, suggesting GG0110 should be D.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', provide the expected JSON snippet for the specific field(s) identified as discrepant in the alt_rationale based on referral data (e.g., '{ \"gg0100b\": \"2\" }', '{ \"gg0110\": \"D\" }', '{ \"a1250\": \"A or B\" }'). Combine fields if appropriate. Leave blank if rating is '1'.", "examples": [{"context": ["SOC: { \"gg0100a\": \"3\", \"gg0100b\": \"3\", \"gg0100c\": \"3\", \"gg0100d\": \"1\", \"gg0110\": \"Z\", \"a1250\": \"C\" } (All Independent, No device, No transport issue)", "Referral Func/Cog Context: { \"baseline_functional_status\": { \"mobility_level\": \"Independent\", \"adl_performance_(selfcare)\": \"Independent\", \"assistive_devices_used\": \"None\", \"summary\": \"Fully independent prior.\" }, \"baseline_cognitive_status\": { \"mental_status\": \"A&O\", ... } }", "Referral Social Env: { \"transportation_needs_limitations\": \"Drives self\", ... }"], "rationale": " SOC selections align well with the referral description of baseline independence, no device use, and self-driving. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Prior Function (GG0100), Device Use (GG0110), and Transportation (A1250) consistent with referral baseline data.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC: { ..., \"gg0110\": \"Z\", ... } (No device)", "Referral Func/Cog Context: { \"baseline_functional_status\": { \"assistive_devices_used\": \"Walker PRN for long distances\", \"summary\": \"Used walker occasionally outside\", ... }, ... }"], "rationale": "SOC GG0110 (None) conflicts with referral baseline mentioning prior walker use. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"GG0110 mismatch: SOC indicates no prior device, referral baseline mentions walker use.\", \"alt_rationale\": \"Referral baseline functional status mentions prior use of a walker (D).\", \"alt_answer\": \"{ \\\\\"gg0110\\\\\": \\\\\"D\\\\\" }\" }"}], "options": [], "description": "3 - Independent  – Patient completed the activities by him/herself, with or without an assistive device, with no assistance from a helper.     \n 2 - Needed Some Help  – Patient needed partial assistance from another person to complete activities.      \n 1 - Dependent  – A helper completed the activities for the patient.      \n 8 - Unknown     \n 9 - Not Applicable", "dependencies": [{"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "SOCIAL_ENVIRONMENTAL_CONTEXT_REFERRAL", "source": "referral", "name": "Social & Environmental Context", "question": "Extract information regarding the patient's social and environmental context, including their living situation, available support systems or caregivers, any known home safety concerns, and transportation needs or limitations, as described in the provided referral documentation.", "description": " Social & Environmental Context"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_diagnoses_treatments", "name": "QA_Diagnoses & Treatments", "id": 8257, "types": ["SOC_CHECKLIST"], "sections": [28], "source": "tasks", "source_id": "229", "schema": {"type": "object", "properties": []}, "mandatory": false, "notes": "Compare:\n\nSOC M1021 vs. Ref Diagnoses_REFERRAL.primary_dx / Ref Referral_Overview_REFERRAL.primary_dx.\n\nSOC M1023 vs. Ref Diagnoses_REFERRAL.other_dx.\n\nSOC M1028 vs. Dx lists (SOC & Ref).\n\nSOC O0110 vs. Ref Medical/Surgical History_REFERRAL.special_treatments....\n\nInternal Check: SOC M1021/M1023/M1028/O0110 consistency vs. SOC MEDICATIONS (e.g., Insulin -> DM Dx, O2 med -> O2 in O0110).\nRate '0' for significant mismatches/inconsistencies based on available data.", "enabled": true, "question": "Review the SOC 'Diagnoses & Treatments' section (M1021, M1023, M1028, O0110). Compare against available information in 'Diagnoses_REFERRAL' and 'Medical/Surgical History_REFERRAL'. Also, check for consistency between documented SOC 'Medications' and the listed SOC Diagnoses (M1021/M1023/M1028) and Special Treatments (O0110). Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if the Primary Diagnosis (M1021) matches the referral, there are no significant discrepancies/omissions in Other Dx (M1023), Active Dx (M1028), or Special Treatments (O0110) compared to referral data, AND if diagnoses/treatments are consistent with significant medications documented in the SOC MEDICATIONS field (e.g., DM listed if insulin present, O2 therapy in O0110 if O2 med listed). Minor wording differences or lack of referral data are acceptable if no contradictions exist.\n\nAssign '0' (Incorrect/Issue) if at least one significant discrepancy is found:\n\nMismatch between SOC M1021 and referral primary Dx.\n\nMajor omission/contradiction in M1023 or O0110 compared to referral.\n\nClear inconsistency in M1028 based on listed Dx.\n\nSignificant diagnosis/treatment implied by SOC MEDICATIONS is missing from SOC M1021/M1023/M1028/O0110.\n\nreason (Text String):\n\nIf rating is '1', state that diagnoses and treatments are consistent with available referral data and SOC medications, noting any key referral data missing (e.g., \"Primary Dx consistent. Other Dx and Treatments align with referral and SOC meds.\").\n\nIf rating is '0', state all specific significant discrepancies found (e.g., 'Primary diagnosis mismatch.', 'Missing Diabetes diagnosis implied by Insulin on SOC medication list.', 'Omission of Oxygen Therapy noted in referral.', 'M1028 inconsistent with listed diagnoses.').\n\nalt_rationale (Text String): If rating is '0', explain the correct information based on referral or SOC medication list for the first/most significant discrepancy (e.g., 'Referral primary diagnosis is Pneumonia.', 'SOC medication list includes Insulin, implying Diabetes diagnosis.', 'Referral indicates continuous oxygen therapy.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', provide the expected JSON snippet for the specific field(s) needing correction based on the alt_rationale (e.g., '{ \"M1021\": { ... } }', '{ \"M1023\": \"[Add Diabetes Dx]\" }', '{ \"O0110\": { \"respiratory_therapies\": [\"C2\"] } }'). Combine if appropriate. Leave blank if rating is '1'.", "examples": [{"context": ["SOC Dx/Tx: { \"M1021\": { \"description\": \"Heart Failure\", ... }, \"M1023\": [ { \"description\": \"Hypertension\", ... } ], \"M1028\": \"3\", \"O0110\": { \"none_of_the_above\": \"Z1\" } }", "SOC Meds: [ { \"name\": \"Lisinopril\", ... }, { \"name\": \"Furosemide\", ... } ] (Meds align with listed Dx)", "Referral Dx: { \"primary_dx\": { \"description\": \"Heart Failure\", ... }, \"other_dx\": [ { \"description\": \"Hypertension\", ... } ] }", "Referral Tx: { ..., \"special_treatments,_procedures_programs\": { \"none_of_the_above\": \"Z1\" } }"], "rationale": "Dx match referral. No special treatments in Ref or SOC. SOC Meds are consistent with listed SOC Dx. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Diagnoses & Treatments consistent with referral data and SOC medications.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Dx/Tx: { \"M1021\": { \"description\": \"Heart Failure\", ... }, \"M1023\": [], \"M1028\": \"3\", \"O0110\": { \"none_of_the_above\": \"Z1\" } }", "SOC Meds: [ { \"name\": \"Lisinopril\", ... }, { \"name\": \"Insulin Lispro\", \"purpose\": \"Diabetes\", ... } ]", "Referral Dx: { \"primary_dx\": { \"description\": \"Heart Failure\", ... }, \"other_dx\": [] }", "Referral Tx: { ..., \"special_treatments,_procedures_programs\": { \"none_of_the_above\": \"Z1\" } }"], "rationale": "While consistent with the (limited) referral Dx, the SOC assessment is internally inconsistent. Insulin is listed in SOC Meds, but Diabetes (DM) is missing from M1023 and M1028. Rating '0'.", "answer": "", "assessment": "{ \"rating\": \"0\", \"reason\": \"Missing Diabetes diagnosis implied by <PERSON><PERSON><PERSON> on SOC medication list.\", \"alt_rationale\": \"SOC medication list includes Insulin; Diabetes Mellitus should be added to M1023 and M1028 updated.\", \"alt_answer\": \"{ \\\\\"M1023\\\\\": \\\\\"[Add { \\\\\\\\\\\\\\\"description\\\\\\\\\\\\\\\": \\\\\\\\\\\\\\\"Diabetes Mellitus\\\\\\\\\\\\\\\", \\\\\\\\\\\\\\\"icd10_code\\\\\\\\\\\\\\\": \\\\\\\\\\\\\\\"E11.9\\\\\\\\\\\\\\\" }]\\\\\", \\\\\"M1028\\\\\": \\\\\"2\\\\\" }\" }"}, {"context": ["SOC Dx/Tx: { ..., \"M1028\": \"3\", \"O0110\": { \"none_of_the_above\": \"Z1\" } }", "SOC Meds: [ ..., { \"name\": \"Oxygen\", \"route\": \"Nasal Cannula\", \"frequency\": \"2L continuous\", ... } ]", "Referral Dx/Tx: (Consistent or N/A for this point)"], "rationale": "SOC Meds list Oxygen, but O0110 incorrectly indicates no special treatments (missing C2 - Continuous Oxygen). Rating '0'.", "answer": "", "assessment": "{ \"rating\": \"0\", \"reason\": \"O0110 missing Oxygen Therapy indicated by SOC medication list.\", \"alt_rationale\": \"SOC medication list includes Oxygen; O0110 should include C2.\", \"alt_answer\": \"{ \\\\\"O0110\\\\\": { \\\\\"respiratory_therapies\\\\\": [\\\\\"C2\\\\\"], \\\\\"none_of_the_above\\\\\": \\\\\"\\\\\" } }\" }"}], "options": [], "description": "Name diagnosis which is the primary reason for home health.  Include ICD-10. Verbalize symptom control rating for each condition:     \n 0  - Asymptomatic, no treatment needed at this time     \n 1  - Symptoms well controlled with current therapy     \n 2  - Symptoms controlled with difficulty, affecting daily functioning; patient needs ongoing monitoring     \n 3  - Symptoms poorly controlled; patient needs frequent adjustment in treatment and dose  monitoring     \n 4 -  Symptoms poorly controlled; history of re-hospitalisation", "dependencies": [{"code": "diagnoses_REFERRAL", "source": "referral", "name": "Diagnoses", "question": "Extract and structure the patient's primary and other diagnoses, including descriptions, ICD-10 codes, and symptom control ratings if available, as found in the referral documentation.", "description": "Diagnoses"}, {"code": "diagnoses_treatments", "source": "referral", "name": "Medical/Surgical History", "question": "Extract details about the patient's past surgical history and any relevant ongoing special treatments, procedures, or programs (like IV therapy, dialysis, oxygen, etc.) mentioned in the provided referral documentation.", "description": "Medical/Surgical History"}, {"code": "MEDICATIONS_SOC", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GG0170RR1", "name": "Type of Wheelchair or Scooter Used (50 feet)", "id": 2591, "types": ["SOC_CHECKLIST"], "sections": [35], "source": "tasks", "source_id": "301", "schema": {}, "mandatory": false, "notes": "To verify the type of wheelchair/scooter used for 50 feet mobility, we need to check several related factors: whether the patient uses a wheelchair at all, their mobility status, what assistive devices they use, and if they can actually complete the 50 feet wheelchair assessment", "enabled": true, "question": "Based on the OASIS E item code GG0170RR1; Indicate the type of wheelchair or scooter used?", "inputType": "text", "criteria": "1. First confirm patient uses wheelchair/scooter (GG0170Q)\n2. Verify patient can complete 50 feet wheelchair assessment (GG0170R1)\n3. Cross-reference with documented mobility devices (MUSCULOSKELETAL_DEVICE, DME)\n4. Check overall mobility status (MUSCULOSKELETAL_STATUS)\n5. Validate consistency with functional wheelchair capability (FUNCTIONAL_WHEELCHAIR_AMBULATION)\n6. Type of wheelchair/scooter selected should align with all above data points", "examples": [], "options": [], "description": "1  - Manual      \n 2  - Motorized", "dependencies": [{"code": "GG0170Q", "source": "tasks"}, {"code": "GG0170R1", "source": "tasks"}, {"code": "MUSCULOSKELETAL_DEVICE", "source": "tasks"}, {"code": "MUSCULOSKELETAL_STATUS", "source": "tasks"}, {"code": "FUNCTIONAL_WHEELCHAIR_AMBULATION", "source": "tasks"}, {"code": "DME", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_ADDRESS_CONTACT_DETAILS_LANGUAGE", "name": "QA_Address, Contact Details & Preferred Language", "id": 8255, "types": ["SOC_CHECKLIST"], "sections": [85], "source": "tasks", "source_id": "211", "schema": {}, "mandatory": false, "notes": "Compare:\n\nSOC Address/M0050/M0060 vs. Ref patient.address (street/state/zip)\n\nSOC phone_number vs. Ref patient.primary_phone_number\n\nSOC a1110a vs. Ref preferred_language\n\nSOC a1110b vs. Ref interpreter_need\n\nSOC emergency_contact (name/phone) vs. Ref emergency_contacts[0] (name/phone)\nRate '0' only for confirmed mismatches in available data.", "enabled": true, "question": "Review the patient's Address (Street, State M0050, Zip M0060), Phone Number, Preferred Language (A1110a), Interpreter Need (A1110b), and primary Emergency Contact details (name, phone number) entered in the SOC 'Address, Contact Details & Preferred Language' section. Compare these against the corresponding available information in the 'Demographics_REFERRAL' and 'Contact_Information_REFERRAL' fields from the referral documentation. Assign a rating and provide a reason.", "inputType": "json", "criteria": "espond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all comparable details (Address, Phone, Language, Interpreter Need, Emergency Contact Name/Phone) match the available referral data, OR if referral data is missing for certain points preventing comparison, but no mismatches are found in the available data.\n\nAssign '0' (Incorrect/Issue) if at least one mismatch is found between a SOC data point and the corresponding available referral data point.\n\nreason (Text String):\n\nIf rating is '1', state which categories of information are consistent with available referral data and explicitly note any key information that was missing from the referral (e.g., \"Address, Phone, Language match referral. Emergency contact details missing in referral.\", \"All details consistent with available referral data.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Address mismatch (Zip Code)', 'Phone number mismatch', 'Interpreter Need mismatch', 'Emergency contact name mismatch').\n\nalt_rationale (Text String): If rating is '0' due to a discrepancy, explain the correct information based on the referral documentation for the first or most significant discrepancy noted (e.g., 'Referral lists patient zip code as 90210.', 'Referral indicates interpreter needed.', 'Referral lists emergency contact as <PERSON>.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', provide the expected JSON snippet for the specific field(s) identified as discrepant in the alt_rationale based on referral data (e.g., '{ \"m0060\": \"90210\" }', '{ \"a1110\": { \"a1110b\": \"1\" } }', '{ \"emergency_contact\": { \"name\": \"Jane Doe\" } }'). Combine related fields if appropriate (e.g., for address: '{ \"address\": \"123 Referral St\", \"m0050\": \"CA\", \"m0060\": \"90210\" }'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC: { \"address\": \"123 Main St\", \"m0050\": \"CA\", \"m0060\": \"91234\", \"phone_number\": \"(*************\", \"a1110\": { \"a1110a\": \"English\", \"a1110b\": \"0\" }, \"emergency_contact\": { \"name\": \"<PERSON>\", \"phone_number\": \"(*************\", ... } }", "Referral Contact: { \"patient\": { \"address\": { \"street\": \"123 Main St\", \"state\": \"CA\", \"zip\": \"91234\" }, \"primary_phone_number\": \"(*************\", ... }, \"emergency_contacts\": [ { \"name\": \"<PERSON>\", \"phone\": \"(*************\", ... } ] }", "Referral Demographics: { \"preferred_language\": \"English\", \"interpreter_need\": \"0\", ... }"], "rationale": "Address, phone, language, interpreter need, and emergency contact name/phone all match available referral data.", "answer": "{ \"rating\": \"1\", \"reason\": \"Address, phone, language/interpreter need, and emergency contact details match referral data.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC: { \"address\": \"123 Main St\", \"m0050\": \"CA\", \"m0060\": \"91234\", ... }", "Referral Contact: { \"patient\": { \"address\": { \"street\": \"456 Oak Ave\", \"state\": \"CA\", \"zip\": \"91234\" }, ... } }"], "rationale": "Street address mismatches. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Address mismatch (Street).\", \"alt_rationale\": \"Referral Contact Information lists street address as 456 Oak Ave.\", \"alt_answer\": \"{ \\\\\"address\\\\\": \\\\\"456 Oak Ave\\\\\" }\" }"}], "options": [], "description": "", "dependencies": [{"code": "Demographics_REFERRAL", "source": "referral", "name": "Demographics", "question": "Extract and structure the patient's demographic information (e.g., name, date of birth, gender, race, ethnicity, language, identification numbers) found within the provided referral documentation.", "description": "Demographics"}, {"code": "Contact_Information_REFERRAL", "source": "referral", "name": "Contact Information", "question": " Extract the patient's contact information (address, phone numbers, email) and details for any listed emergency contacts (name, relationship, phone) from the provided referral documentation.", "description": "Contact Information"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_SOCIAL_LITERACY_CONTEXT", "name": "QA_Social & Literacy Context", "id": 8276, "types": ["SOC_CHECKLIST"], "sections": [32], "source": "tasks", "source_id": "269", "schema": {}, "mandatory": false, "notes": "Compare:\n\nSOC D0700/Psychosocial Status vs. Ref living_situation/supports.\n\nSOC B1300 vs. Ref preferred_language/interpreter_need.\n\nSOC Other Placement vs. Referral notes on placement.\nFocus on general alignment and significant contradictions. Subjective items allow some leeway unless clearly conflicting. Rate '0' for clear contradictions based on available referral context.", "enabled": true, "question": "Review the SOC 'Social & Literacy Context' assessment (D0700 Social Isolation, Psychosocial Status including support/care level, Other placement considerations, B1300 Health Literacy). Compare these selections against the relevant available information in the 'SOCIAL_ENVIRONMENTAL_CONTEXT_REFERRAL' field (living_situation, supports) and 'Demographics_REFERRAL' (preferred_language, interpreter_need). Assess for general consistency. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if the SOC selections for social isolation, psychosocial status, placement, and health literacy appear reasonably consistent with the available referral descriptions of living situation, support systems, and language needs. Minor variations in subjective assessments are acceptable if the overall picture aligns.\n\nAssign '0' (Incorrect/Issue) if there is a significant contradiction between the SOC assessment and the available referral context (e.g., D0700=Never Isolated vs. Referral stating \"lives alone, no support\"; B1300=Never difficulty vs. Referral indicating need for interpreter).\n\nreason (Text String):\n\nIf rating is '1', state that the Social & Literacy context assessment is consistent with available referral data, noting missing referral context if applicable (e.g., \"Social/Literacy context consistent with referral.\", \"Consistent; referral social context missing.\").\n\nIf rating is '0', state all specific significant discrepancies found (e.g., 'D0700 inconsistency: SOC indicates Never Isolated, referral describes patient living alone with no supports.', 'B1300 inconsistency: SOC indicates Never has difficulty, referral notes interpreter needed.', 'Psychosocial Level of Care mismatch: SOC states Excellent, referral describes support as marginal.').\n\nalt_rationale (Text String): If rating is '0', explain the expected status or area needing review based on the referral documentation for the first or most significant discrepancy (e.g., 'Referral context suggests potential for social isolation (D0700).', 'Need for interpreter suggests potential health literacy challenges (B1300).', 'Referral description of support aligns more closely with Adequate/Marginal level of care.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Re-evaluate D0700 based on referral context.\"', '\"Re-evaluate B1300 considering language needs.\"', '\"Review Psychosocial Level of Care selection.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Social/Lit: { \"d0700\": \"2\", \"pyschosocial_status\": { \"level_of_care_at_home\": \"3\" }, \"b1300_health_literacy\": \"1\" } (Sometimes isolated, Adequate care, Rarely literacy issues)", "Referral Social Env: { \"living_situation\": \"Lives alone\", \"supports\": \"Daughter checks in weekly\", ... }", "Referral Demographics: { \"preferred_language\": \"English\", \"interpreter_need\": \"0\", ... }"], "rationale": "SOC selections (Sometimes isolated, Adequate care, Rarely literacy issues) seem reasonably consistent with living alone, having weekly support, and being an English speaker with no interpreter need. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Social & Literacy context consistent with available referral data.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Social/Lit: { \"d0700\": \"0\", ... } (Never isolated)", "Referral Social Env: { \"living_situation\": \"Lives alone in remote area\", \"supports\": \"No family nearby, relies on neighbors occasionally\", ... }"], "rationale": "SOC D0700=Never Isolated seems inconsistent with the referral description implying significant potential for isolation. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"D0700 inconsistency: SOC indicates Never Isolated, referral context suggests high potential for isolation.\", \"alt_rationale\": \"Referral context (lives alone, remote, limited support) suggests D0700 should be re-evaluated, likely higher than 0.\", \"alt_answer\": \"\\\"Re-evaluate D0700 based on referral context and patient interview.\\\"\" }"}], "options": [], "description": "How often do you feel lonely or isolated from those around you?     \n     \n 0 -  Never     \n 1 -  <PERSON>ly     \n 2 -  Sometimes     \n 3 -  Often     \n 4 -  Always     \n 7 -  Patient declines to respond     \n 8 -  Patient unable to respond", "dependencies": [{"code": "SOCIAL_ENVIRONMENTAL_CONTEXT_REFERRAL", "source": "referral", "name": "Social & Environmental Context", "question": "Extract information regarding the patient's social and environmental context, including their living situation, available support systems or caregivers, any known home safety concerns, and transportation needs or limitations, as described in the provided referral documentation.", "description": " Social & Environmental Context"}, {"code": "Demographics_REFERRAL", "source": "referral", "name": "Demographics", "question": "Extract and structure the patient's demographic information (e.g., name, date of birth, gender, race, ethnicity, language, identification numbers) found within the provided referral documentation.", "description": "Demographics"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GU_ASSESSMENT", "name": "QA_Genitourinary", "id": 8280, "types": ["SOC_CHECKLIST"], "sections": [33], "source": "tasks", "source_id": "289", "schema": {}, "mandatory": false, "notes": "Check:\n\nInternal Consistency: SOC gu_summary vs. SOC detailed GU findings (urine_character, M1610, M1600, catheter_details, gu_data). WNL->Problem->Need linkage. M1610 vs. catheter_details. M1600 vs. gu_data/SOC Meds.\n\nReferral Context: SOC findings vs. Ref active_problems_summary.genitourinary, Ref History/Orders (Catheter, UTI, Dialysis).\nRate '0' for internal inconsistencies OR significant contradictions with available referral context.", "enabled": true, "question": "Review the SOC 'Genitourinary' assessment findings (urine_character, catheter_details, gu_data, M1600 UTI Tx, M1610 Incontinence/Cath) and the gu_summary object (gu_wnl, gu_active_problems, gu_care_plan_needs).\n\nAssess internal consistency: Does gu_wnl reflect detailed findings (abnormal urine, catheter, incontinence, UTI)? If gu_wnl=No, are active_problems and care_plan_needs documented? Are M1610/catheter details/gu_data consistent regarding catheter/incontinence? Is M1600 consistent with symptoms/meds?\n\nCompare against relevant available referral context (Referral active_problems_summary.genitourinary, history/orders for catheter/UTI/dialysis).\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all of the following are true:\n\nThe SOC gu_summary.gu_wnl status is consistent with the detailed GU findings (urine, M1610 status, M1600 status, presence of catheter/dialysis/problematic symptoms).\n\nIf gu_wnl is '2' (No), then gu_active_problems and gu_care_plan_needs are appropriately documented.\n\nCatheter/Incontinence status (M1610, catheter_details, gu_data) is internally consistent.\n\nUTI status (M1600, gu_data, SOC Meds) is internally consistent.\n\nThe documented findings do not significantly contradict relevant GU information noted in the available referral context.\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nInternal inconsistency in the gu_summary (WNL status vs. findings, missing problem, or missing care need).\n\nInternal inconsistency regarding catheter/incontinence or UTI status/treatment.\n\nA significant GU issue (like catheter, dialysis, recent UTI) noted in the referral context is seemingly ignored or contradicted in the SOC GU assessment without clear rationale.\n\nreason (Text String):\n\nIf rating is '1', state that the GU assessment and summary are internally consistent and align with available referral context, noting missing referral context if applicable (e.g., \"GU assessment and summary internally consistent and align with referral context.\", \"Internally consistent. Referral context for GU status missing.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Inconsistency: GU marked WNL despite M1610=2 (Catheter).', 'Inconsistency: GU not WNL, but no active problem documented.', 'Inconsistency: Active problem (UTI) documented, but no care plan need listed.', 'Discrepancy: M1610=0 but catheter details are documented.', 'Inconsistency: M1600=1 (UTI Tx) but no antibiotic on SOC Meds list.', 'Discrepancy: Referral notes Foley, but M1610=0.').\n\nalt_rationale (Text String): If rating is '0', explain the expected action or correction for the first or most significant discrepancy (e.g., 'Presence of catheter (M1610=2) means gu_wnl should be 2.', 'Active problem needs to be specified.', 'Care plan need required for UTI.', 'M1610 should be 2 if catheter present.', 'Verify antibiotic for UTI Tx or correct M1600.', 'Referral indicates Foley; M1610 should be 2.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Set gu_wnl to 2 (No).\"', '\"Add specific active problem for GU.\"', '\"Add care plan need for UTI management.\"', '\"Set M1610 to 2.\"', '\"Verify SOC Meds or update M1600.\"', '\"Update M1610 to 2 and document catheter details.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC GU: { \"urine_character\": [\"1\", \"7\"], \"catheter_details\": {}, \"gu_data\": [\"4\", \"13\"], \"M1600\": \"0\", \"M1610\": \"0\", \"gu_summary\": { \"gu_wnl\": \"1\", \"active_problems\": \"\", \"care_plan_needs\": \"\" } } (WNL, denies issues, no cath/UTI)", "SOC Meds: (No relevant GU meds)", "Referral: (Assume no active GU problems, no cath/UTI history/orders)"], "rationale": " SOC findings WNL, summary consistent (WNL=1). No catheter/UTI/incontinence. Aligns with lack of issues in referral. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"GU assessment and summary internally consistent and align with available referral context.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC GU: { ..., \"catheter_details\": { \"catheter_type\": \"2\", ... }, ..., \"M1610\": \"2\", \"gu_summary\": { \"gu_wnl\": \"1\", ... } } (Catheter present M1610=2, but summary WNL=1)", "Referral: (Assume consistent catheter)"], "rationale": "Internal inconsistency: gu_wnl is '1' despite documented catheter (M1610=2). Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Inconsistency: GU marked WNL despite M1610=2 (Catheter).\", \"alt_rationale\": \"Presence of catheter (M1610=2) means gu_wnl should be 2 (No).\", \"alt_answer\": \"\\\"Set gu_wnl to 2 (No) and document active problem/care plan need for catheter.\\\"\" }"}], "options": [], "description": "Interview/assess/verbalize     \n     \n       Are findings WNL?     \n           (Consider  symptoms, medications, skilled needs, care plan.)     \n       If not, describe…     \n -  Urine Character     \n - Urinary Tract Infection     \n - Urinary Catheter Presence     \n - Catheter Type with Date Last Changed     \n - Catheter Related Details     \n - General Genitourinary Data     \n - Medical / Medication Changes Related to GU Status     \n Active problems and skilled needs?", "dependencies": [{"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "diagnoses_treatments", "source": "referral", "name": "Medical/Surgical History", "question": "Extract details about the patient's past surgical history and any relevant ongoing special treatments, procedures, or programs (like IV therapy, dialysis, oxygen, etc.) mentioned in the provided referral documentation.", "description": "Medical/Surgical History"}, {"code": "ORDERS_REFERRAL", "source": "referral", "name": "Orders", "question": "Extract the specific services ordered, including the frequency and duration if specified, for Nursing, Physical Therapy, Occupational Therapy, Speech Therapy, Medical Social Work, and Home Health Aide. Also, capture any specified clinical parameters to monitor (e.g., vital sign ranges to report).", "description": "Specific Service Orders"}, {"code": "MEDICATIONS_SOC", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_Advanced_Care_Planning", "name": "QA_Advanced Care Planning", "id": 8251, "types": ["SOC_CHECKLIST"], "sections": [85], "source": "tasks", "source_id": "218", "schema": {}, "mandatory": false, "notes": "Compare:\n\nSOC advanced_directives vs. Ref advanced_directives (exact match of selected values)\n\nSOC dnr_state_forms vs. Ref dnr_state_forms\n\nSOC power_of_attorney (name/phone) vs. Ref power_of_attorney (name/phone)\nRate '0' only for confirmed mismatches in available data.", "enabled": true, "question": "Review the SOC 'Advanced Care Planning' section (advanced_directives, dnr_state_forms, power_of_attorney name/phone). Compare these against the available corresponding information in the 'ADVANCED_CARE_PLANNING_REFERRAL' field from the referral documentation. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all comparable structured details (advanced_directives, dnr_state_forms, power_of_attorney name/phone) match the available referral data, OR if referral data is missing for certain points preventing comparison, but no mismatches are found in the available data.\n\nAssign '0' (Incorrect/Issue) if at least one mismatch is found between a SOC data point and the corresponding available referral data point.\n\nreason (Text String):\n\nIf rating is '1', state which categories of information are consistent with available referral data and explicitly note any key information that was missing from the referral (e.g., \"Advanced Directives, DNR form status match referral. POA details missing in referral.\", \"All details consistent.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Advanced directives mismatch (SOC has DNR, Ref has Living Will)', 'DNR State Forms status mismatch', 'Power of Attorney name mismatch').\n\nalt_rationale (Text String): If rating is '0' due to a discrepancy, explain the correct information based on the referral documentation for the first or most significant discrepancy noted (e.g., 'Referral lists advanced directives as DNR (1) and POA (2).', 'Referral DNR state form status is Not Completed (2).'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', provide the expected JSON snippet for the specific field(s) identified as discrepant in the alt_rationale based on referral data (e.g., '{ \"advanced_directives\": \"1,2\" }', '{ \"dnr_state_forms\": \"2\" }', '{ \"power_of_attorney\": { \"name\": \"Jane Smith\", \"phone_number\": \"555-1111\" } }'). Combine fields if appropriate. Leave blank if rating is '1'.", "examples": [{"context": ["SOC: { \"advanced_directives\": \"1,2\", \"dnr_state_forms\": \"\", \"power_of_attorney\": { \"name\": \"<PERSON>\", \"phone_number\": \"************\" }, ... }", "Referral: { \"advanced_directives\": \"1,2\", \"dnr_state_forms\": \"\", \"power_of_attorney\": { \"name\": \"<PERSON>\", \"phone_number\": \"************\" }, ... }"], "rationale": "All comparable fields (directives, POA name/phone) match. DNR form status is missing in both.", "answer": "{ \"rating\": \"1\", \"reason\": \"Advanced directives and POA details match referral data. DNR form status not available in referral for verification.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC: { \"advanced_directives\": \"1\", ... } (Only DNR)", "Referral: { \"advanced_directives\": \"1,2\", ... } (DNR and POA)"], "rationale": "The set of advanced directives mismatches (SOC is missing POA). Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Advanced directives mismatch (Missing POA).\", \"alt_rationale\": \"Referral lists both DNR (1) and POA (2) as advanced directives.\", \"alt_answer\": \"{ \\\\\"advanced_directives\\\\\": \\\\\"1,2\\\\\" }\" }"}, {"context": ["SOC: { ..., \"dnr_state_forms\": \"1\", ... } (Completed)", "Referral: { ..., \"dnr_state_forms\": \"2\", ... } (Not completed)"], "rationale": "DNR State Form status mismatches. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"DNR State Forms status mismatch.\", \"alt_rationale\": \"Referral indicates DNR state forms are Not Completed (2).\", \"alt_answer\": \"{ \\\\\"dnr_state_forms\\\\\": \\\\\"2\\\\\" }\" }"}], "options": [], "description": "Verbalize all that apply:      \n     \n -  DNR / CPR  (incl. if state forms completed)     \n -  PoA  (incl. name & phone number)     \n -  Living Will     \n -  None", "dependencies": [{"code": "ADVANCED_CARE_PLANNING_REFERRAL", "source": "referral", "name": "Advanced Care Planning", "question": " Document the patient's advanced care planning, including advanced directives, DNR state forms, power of attorney details, and any other relevant details.", "description": "Advanced Care Planning"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GG0130A", "name": "QA_(GG0130) Self-Care", "id": 8284, "types": ["SOC_CHECKLIST"], "sections": [37], "source": "tasks", "source_id": "333", "schema": {}, "mandatory": false, "notes": "Compare SOC GG0130 A,B,C,E,F,G,H against:\n\nSOC M1800s: M1870, M1800, M1845, M1830, M1810, M1820 respectively. Look for directional consistency (Independent vs Assist vs Dependent).\n\nReferral Context: Ref active_problems_summary.functional_status, Ref reason.\nFocus on consistency regarding current self-care abilities. Rate '0' for significant contradictions based on available data.", "enabled": true, "question": "Review the SOC assessment of current self-care performance (GG0130A Eating, GG0130B Oral Hygiene, GG0130C Toileting Hygiene, GG0130E Shower/Bathing, GG0130F Upper Body Dressing, GG0130G Lower Body Dressing, GG0130H Footwear). Compare these against:\n\nThe corresponding SOC ADL assessments (M1870 Feeding, M1800 Grooming, M1845 Toilet Hygiene, M1830 Bathing, M1810 Dress Upper, M1820 Dress Lower).\n\nRelevant available referral context describing current functional problems or reason for referral (Referral active_problems_summary.functional_status, reason).\nAssess for general consistency. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if the GG0130 selections generally align with the corresponding M1800s assessments AND with the overall picture of the patient's current self-care abilities described in the available referral context. Minor differences in granularity between GG (6 levels) and M (fewer levels) are acceptable if directionally consistent.\n\nAssign '0' (Incorrect/Issue) if there is a significant contradiction between one or more GG0130 selections and either the corresponding M1800s assessment OR the available referral context regarding current self-care function (e.g., GG0130E shows Independence, M1830 shows Total Bath; GG0130F shows Dependence, referral notes requires setup only).\n\nreason (Text String):\n\nIf rating is '1', state that the GG0130 Self-Care assessment is consistent with M1800s ADLs and available referral context, noting key missing referral context if applicable (e.g., \"GG0130 consistent with M1800s and referral context.\", \"Consistent; Referral lacks details on current self-care problems.\").\n\nIf rating is '0', state all specific significant discrepancies found (e.g., 'GG0130E inconsistency: GG indicates Independence (06), M1830 indicates Total Bath (6).', 'GG0130F inconsistency: GG indicates Dependent (01), referral active problems state needs setup assist only.', 'GG0130A inconsistency: GG indicates Eating Independence (06), M1870 indicates Needs Setup (1).').\n\nalt_rationale (Text String): If rating is '0', explain the expected status based on the conflicting data source (M1800s or referral) for the first or most significant discrepancy (e.g., 'M1830 score of 6 suggests GG0130E should be 01 (Dependent).', 'Referral context implies need for setup assist, suggesting GG0130F should be 05.', 'M1870 score of 1 suggests GG0130A should be 05 (Setup Assist).'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Correct GG0130E to 01.\"', '\"Review GG0130F based on referral context.\"', '\"Correct GG0130A to 05.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC GG0130: { \"gg0130e\": \"03\", \"gg0130f\": \"03\", ... } (Bathing/Upper Dress = Partial/Mod Assist)", "SOC M1800s: { \"m1830\": \"2\", \"m1810\": \"2\", ... } (Bathing/Upper Dress = Needs Help)", "Referral Func/Cog: { \"active_problems_summary\": { \"functional_status\": \"Needs some help with bathing/dressing\", ... } }"], "rationale": "GG0130 scores indicating partial/moderate assist align reasonably well with M1800s indicating needing help, and with the referral context. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"GG0130 Self-Care consistent with M1800s ADLs and referral context.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC GG0130: { ..., \"gg0130e\": \"06\", ... } (Independent Shower/Bathing)", "SOC M1800s: { ..., \"m1830\": \"6\", ... } (Total Bath)", "Referral: (Assume no conflicting info)"], "rationale": "Significant internal contradiction: GG0130E indicates independence, while M1830 indicates total dependence for bathing. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"GG0130E inconsistency: GG indicates Independence (06), M1830 indicates Total Bath (6).\", \"alt_rationale\": \"M1830 score of 6 (Unable to participate) suggests GG0130E should be 01 (Dependent).\", \"alt_answer\": \"{ \\\\\"gg0130e\\\\\": \\\\\"01\\\\\" }\" }"}], "options": [], "description": "The ability to use suitable utensils to bring food and/or liquid to the mouth and swallow food and/or liquid once the meal is placed before the patient.", "dependencies": [{"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "Referral_Overview_REFERRAL", "source": "referral", "name": "Referral Overview", "question": " Extract key overview information about the home health referral, including primary diagnosis, source details, referral date, reason for referral, services ordered, and physician-ordered start date, from the provided referral documentation. Synthesize a concise narrative summary combining these key elements.", "description": "Referral Overview"}, {"code": "ADLs/IADLs_M1800s", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_HOME_BOUND_STATUS", "name": "QA_Homebound Status", "id": 8286, "types": ["SOC_CHECKLIST"], "sections": [39], "source": "tasks", "source_id": "348", "schema": {}, "mandatory": false, "notes": "Check:\n\nSOC Criteria (1, 2a, 2b) vs. Ref Eligibility_Documentation_REFERRAL.home_bound_requirement criteria (if available).\n\nConsistency: SOC Criteria & Summary vs. SOC current functional status (M1860, GG0170, DME) & Ref active_problems_summary.functional_status.\n\nRequirement Check: Ensure Criteria 1 AND (Criteria 2a OR Criteria 2b) are logically supported by the overall assessment.\nRate '0' for mismatches with referral eligibility, internal functional contradictions, or failure to meet basic requirements.", "enabled": true, "question": "Review the SOC 'Homebound Status' documentation (Criteria 1, Criteria 2a, Criteria 2b, Summary).\n\nCompare the selected criteria against the home_bound_requirement criteria noted in the 'Eligibility Documentation_REFERRAL' (if available).\n\nAssess if the combination of selected criteria and the Summary justification are consistent with the patient's current functional status documented in SOC (M1860, GG0170, DME use) and referral (active_problems_summary.functional_status).\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if the selected Homebound Criteria (1, 2a, 2b) and the summary justification are logically consistent with the patient's overall documented current functional status (from SOC M1860/GG0170/DME and referral active problems) AND do not significantly contradict the available homebound justification provided in the referral eligibility documentation.\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nSignificant mismatch between SOC selected criteria and referral selected criteria (if available).\n\nThe selected criteria or summary justification clearly contradicts the patient's documented current functional abilities (e.g., claims bedbound but M1860 shows ambulation; claims taxing effort needed but GG0170 shows independence).\n\nFailure to meet the basic homebound requirements (i.e., Criteria 1 AND at least one of Criteria 2a/2b are not appropriately selected based on the overall assessment).\n\nreason (Text String):\n\nIf rating is '1', state that the Homebound Status justification is consistent with the overall functional assessment and available referral documentation, noting missing referral justification if applicable (e.g., \"Homebound criteria consistent with functional status and referral eligibility docs.\", \"Consistent; Referral eligibility documentation missing.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Inconsistency: Criteria 1 selected requires device, but M1860/GG0170 show independence without device.', 'Inconsistency: Claims taxing effort (Criteria 2a) but GG0170 mobility scores indicate high independence.', 'Discrepancy: Referral eligibility criteria differ from SOC selections.', 'Failure to meet requirements: Criteria 1 met, but neither 2a nor 2b selected despite functional limitations.').\n\nalt_rationale (Text String): If rating is '0', explain the expected criteria selection or justification based on the conflicting functional data or referral eligibility info (e.g., 'Patient's independent ambulation per M1860 contradicts Criteria 1 requiring device/assist.', 'Referral criteria selections were X, Y, Z.', 'Functional dependence suggests Criteria 2a/2b should be selected.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Review Criteria 1 selection based on M1860/GG0170.\"', '\"Align SOC criteria selections with referral eligibility documentation.\"', '\"Select appropriate Criteria 2a/2b based on documented functional limitations.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Homebound: { \"homebound_criteria_1\": \"2\", \"homebound_criteria_2a\": \"5\", \"hombound_criteria_2b\": \"2\", \"homebound_summary\": \"<PERSON><PERSON> walker, unable to ambulate long distance to exit home.\" } (Crit 1=Need<PERSON> Device, Crit 2a=Unable long distance)", "SOC M1860/GG0170: Indicate walker use, difficulty with longer distances.", "Referral Eligibility: (Assume matches or is missing)", "Referral Func Context: { \"active_problems_summary\": { \"functional_status\": \"Requires walker, limited endurance\", ... } }"], "rationale": "Selected criteria (Needs Device, Unable long distance) and summary align with SOC functional assessments and referral context. Requirements met. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Homebound Status criteria and summary consistent with functional assessments and referral context.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Homebound: { \"homebound_criteria_1\": \"4\", ... } (Requires Assistance of Another)", "SOC M1860: { \"m1860\": \"1\", ... } (Independent w/ 1-handed device)", "SOC GG0170: (Shows independence or setup assist for mobility/transfers)"], "rationale": "Homebound Criteria 1 claims need for assistance of another person, contradicting M1860/GG0170 indicating independence with a device. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Inconsistency: Criteria 1 (Requires Assist of Another) contradicts M1860/GG0170 indicating independence with device.\", \"alt_rationale\": \"Patient's independence with device per M1860/GG0170 suggests Criteria 1 selection should be 2 (Requires Assistive Device) rather than 4.\", \"alt_answer\": \"{ \\\\\"homebound_criteria_1\\\\\": \\\\\"2\\\\\" }\" }"}], "options": [], "description": "Verbalize all that apply:     \n     \n - Because of Illness or Injury     \n     \n - Requires Assistive Device for Mobility     \n     \n - Requires Special Transportation     \n     \n - Requires the Assistance of Another Person to Leave home     \n     \n - Leaving Home is Medically Contraindicated      \n     \n - State/Local Mandated Shelter-in-Place / Stay-at-Home Orders", "dependencies": [{"code": "ELIGIBILITY_DOCUMENTATION_REFERRAL", "source": "referral", "name": "Eligibility Documentation", "question": "Extract details regarding the required Face-to-Face (F2F) encounter documentation (including the performing provider, note content, reason/relation linkage, signature, and timeframe) and the Homebound status justification (criteria met and summary), as found within the provided referral documentation.", "description": "Eligibility Documentation"}, {"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "ADLs/IADLs_M1800s", "source": "tasks"}, {"code": "GG0170", "source": "tasks"}, {"code": "Mobility_Safety_Aid", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_VISIT_SUMMARY", "name": "QA_Visit Summary", "id": 8285, "types": ["SOC_CHECKLIST"], "sections": [40], "source": "tasks", "source_id": "356", "schema": {"type": "object", "properties": [{"name": "summary", "label": "Summary", "type": "string", "required": false, "params": {"placeholder": "", "readonly": false, "model": {"question": "", "criteria": "", "notes": "", "useValue": false, "examples": [], "enabled": false, "sources": [], "directSource": ""}, "conditionals": {"conditions": [], "order": ""}, "sources": []}}]}, "mandatory": false, "notes": "Check:\n\nCompleteness: Does the summary cover key expected areas (Hx, Assessment, Plan, Edu, Follow-up)?\n\nAccuracy: Does the summary content match the detailed findings in other SOC sections?\n\nConciseness vs. Comprehensiveness: Is it a useful overview without excessive detail but capturing critical points?\nRate '0' for blank, significantly incomplete, or inaccurate summaries.", "enabled": true, "question": "Review the narrative 'Visit Summary' generated for the Start of Care visit. Assess if it accurately and comprehensively reflects the key findings and plans documented throughout the entire SOC assessment, including relevant medical history, major assessment findings (vitals, physical exam, functional status, risks), the core care plan (goals, major interventions), patient/caregiver education provided, and follow-up plans. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if the Visit Summary is present, reasonably comprehensive, and accurately reflects the most important information documented in the detailed SOC assessment sections without significant omissions or misrepresentations.\n\nAssign '0' (Incorrect/Issue) if the Visit Summary is blank, significantly incomplete (missing key sections like major problems or core plan elements), or contains information that clearly contradicts details documented elsewhere in the SOC assessment.\n\nreason (Text String):\n\nIf rating is '1', state that the Visit Summary provides an accurate and reasonably comprehensive overview of the SOC assessment (e.g., \"Summary accurately reflects key assessment findings, care plan, and follow-up.\", \"Comprehensive summary provided.\").\n\nIf rating is '0', state the specific deficiencies (e.g., 'Summary is blank.', 'Summary incomplete: Missing mention of significant functional deficits and PT orders.', 'Summary inaccurate: States vitals stable, but significant hypertension documented.', 'Summary fails to mention primary diagnosis or key interventions.').\n\nalt_rationale (Text String): If rating is '0', explain what key information is missing or needs correction in the summary based on the full assessment (e.g., 'Summary should include the newly identified Stage 2 pressure ulcer and related wound care plan.', 'Summary needs to accurately reflect the documented unstable vital signs.', 'Summary should mention the planned caregiver education regarding medication management.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Generate visit summary.\"', '\"Revise summary to include [Missing Element, e.g., functional deficits, wound care plan].\"', '\"Correct summary statement regarding [Inaccurate Element, e.g., vital signs] to align with detailed assessment.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Visit Summary: \"75 y/o male admitted post-hospitalization for CHF exacerbation. Assessment revealed bilateral LE edema, crackles LLL, M1860=4 (needs supervision amb). Care plan includes SN for disease management ed, monitoring VS/weight, PT eval for strengthening/balance. Educated pt/wife on low sodium diet, med compliance. F/U SN 2W4, PT 3W4.\"", "SOC Assessment: (Assume details align with summary - edema, crackles, M1860=4, SN/PT orders, education provided)"], "rationale": "Summary covers history context, key assessment findings (edema, lung sounds, ambulation), core plan (SN/PT, education), and follow-up frequency. Accurately reflects a plausible assessment. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Summary provides an accurate and reasonably comprehensive overview of the SOC assessment.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Visit Summary: \"<PERSON><PERSON> admitted for SN follow-up. Vitals stable. Plan to monitor.\"", "SOC Assessment: (Detailed assessment documents a new, non-healing Stage 3 pressure ulcer on sacrum requiring complex wound care orders, patient is confused M1700=2)"], "rationale": "Summary is overly brief and omits critical findings (Stage 3 ulcer, confusion) and related care plan elements documented elsewhere. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Summary incomplete: Missing mention of significant Stage 3 pressure ulcer, cognitive status, and related care plan.\", \"alt_rationale\": \"Summary should include key findings like the Stage 3 pressure ulcer, patient confusion, and the wound care/cognitive interventions planned.\", \"alt_answer\": \"\\\"Revise summary to include Stage 3 ulcer, cognitive status, wound care, and related interventions.\\\"\" }"}], "options": [], "description": "Summarize pertinent details, including:     \n     \n - Focus of care     \n     \n - Patient Goals", "dependencies": [], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_PAIN_ASSESSMENT", "name": "QA_Pain Assessment", "id": 8292, "types": ["SOC_CHECKLIST", "SOC-DEV"], "sections": [681, 33], "source": "tasks", "source_id": "1975", "schema": {}, "mandatory": false, "notes": "Check:\n\nInternal Consistency: SOC summary vs. SOC detailed pain findings (j0905, pain_description, PAINAD). WNL->Problem->Need linkage. pain_description.medication vs. SOC MEDICATIONS. PAINAD use.\n\nReferral Context: SOC findings vs. Ref active_problems_summary.pain, Ref MEDICATIONS_REFERRAL (pain meds).\nRate '0' for internal inconsistencies OR significant contradictions with available referral context/SOC meds.", "enabled": true, "question": "Review the SOC 'Pain Assessment' findings (j0900 screening, j0905 active problem?, j0910 comprehensive assessment, pain_description details, painad_assessment, summary).\n\nAssess internal consistency: Does summary.pain_wnl reflect detailed findings (j0905=1, pain descriptions, PAINAD)? If pain_wnl=No, are active_problems and care_plan_needs documented? Are medications in pain_description consistent with SOC MEDICATIONS? Is PAINAD used appropriately?\n\nCompare against relevant available referral context (Referral active_problems_summary.pain, pain meds in referral MEDICATIONS_REFERRAL).\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all of the following are true:\n\nThe SOC summary.pain_wnl status is consistent with the detailed pain findings (screening, descriptions, PAINAD if applicable).\n\nIf pain_wnl is '2' (No), then summary.pain_active_problems and summary.pain_care_plan_needs are appropriately documented.\n\nMedications mentioned in pain_description generally align with the SOC MEDICATIONS list (allowing for PRN usage).\n\nPAINAD assessment use (or non-use) is appropriate for the patient's communication ability.\n\nThe documented assessment doesn't significantly contradict major pain issues noted in the available referral context.\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nInternal inconsistency in the summary (WNL status vs. findings, missing problem, or missing care need).\n\nClear inconsistency between pain meds mentioned in pain_description and the SOC MEDICATIONS list without explanation.\n\nInappropriate use or non-use of PAINAD.\n\nA significant pain issue noted in the referral context is seemingly ignored or contradicted in the SOC assessment without clear rationale.\n\nreason (Text String):\n\nIf rating is '1', state that the Pain assessment and summary are internally consistent and align with available referral context/SOC meds, noting missing referral context if applicable (e.g., \"Pain assessment and summary internally consistent and align with referral/SOC meds.\", \"Internally consistent. Referral context for pain missing.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Inconsistency: Pain marked WNL despite j0905=1 and reported pain 5/10.', 'Inconsistency: Active pain problem documented, but no care plan need listed.', 'Inconsistency: Tylenol mentioned in pain_description, but not found on SOC medication list.', 'Discrepancy: Referral notes chronic back pain, but SOC assessment indicates no pain (j0900c=0).').\n\nalt_rationale (Text String): If rating is '0', explain the expected action or correction for the first or most significant discrepancy (e.g., 'Reported pain 5/10 means pain_wnl should be 2.', 'Care plan need required for documented pain.', 'Verify Tylenol is on medication list or remove from pain description.', 'SOC assessment should address chronic back pain noted in referral.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Set pain_wnl to 2 (No).\"', '\"Add care plan need for pain management.\"', '\"Verify/update SOC medication list regarding Tylenol.\"', '\"Re-assess pain considering referral history.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Pain: { \"j0900\": {\"j0900a\":\"1\", \"j0900c\":\"0\", \"j0905\":\"0\"}, \"j0910\": {\"j0910a\":\"0\"}, \"pain_description\": [], \"painad_assessment\": {}, \"summary\": {\"pain_wnl\":\"1\", \"pain_active_problems\":\"\", \"pain_care_plan_needs\":\"\"} }", "SOC Meds: (No pain meds)", "Referral: (Assume no active pain problems)"], "rationale": "SOC indicates no pain (j0900c=0, j0905=0), summary consistent (WNL=1). Aligns with lack of issues in referral/meds. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Pain assessment (No Pain Reported) and summary internally consistent and align with available referral context/SOC meds.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}], "options": [], "description": "Interview/examine/verbalize     \n     \n Does the patient have pain?     \n     \n If yes, describe...     \n - Pain assessment     \n -Scale 1-10, location, duration, what helps     \n - Non-verbal Pain Assessment (PAINAD)     \n Active problem and skilled need?", "dependencies": [{"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "MEDICATIONS_REFERRAL", "source": "referral", "name": "Medications", "question": "Extract the list of medications the patient is currently taking, including details such as name, dose, strength, route, frequency, start/end dates, status, purpose, and any special instructions, as found in the provided referral documentation.", "description": "Medications & Allergies"}, {"code": "MEDICATIONS_SOC", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_VITALS_ASSMNT", "name": "QA_Vitals", "id": 8290, "types": ["SOC_CHECKLIST", "SOC-DEV"], "sections": [681, 33], "source": "tasks", "source_id": "1973", "schema": {}, "mandatory": false, "notes": "Check:\n\nCompliance: SOC Vitals documentation vs. Ref ORDERS_REFERRAL.clinical_parameters.\n\nInternal Consistency: SOC vitals_summary vs. SOC vital readings.\n\nWNL status matches readings?\n\nWNL=No -> Active Problem documented?\n\nActive Problem -> Care Plan Need documented?\nRate '0' for missing ordered vitals OR internal inconsistencies.", "enabled": true, "question": "Review the SOC 'Vitals' section.\n\nCheck if vitals specifically ordered in the referral's 'Orders_REFERRAL' (clinical_parameters) were documented.\n\nAssess the internal consistency of the vitals_summary object: Does vitals_wnl reflect the documented readings? If not WNL, are vitals_active_problems and vitals_care_plan_needs appropriately documented?\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all of the following are true:\n\nAll vital signs specifically ordered in the referral clinical_parameters (if any) are documented in SOC Vitals.\n\nThe SOC vitals_wnl status is consistent with the documented vital sign readings (i.e., not '1' if clearly abnormal vitals exist).\n\nIf vitals_wnl is '2' (No), then vitals_active_problems is not empty.\n\nIf vitals_active_problems is not empty, then vitals_care_plan_needs is not empty.\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nAn ordered vital sign from referral clinical_parameters appears missing from SOC documentation.\n\nSOC vitals_wnl is '1' (Yes) despite clearly abnormal documented vital signs (e.g., significantly high/low BP, very low SpO2, high fever).\n\nSOC vitals_wnl is '2' (No), but vitals_active_problems is empty/blank.\n\nSOC vitals_active_problems identifies an issue, but vitals_care_plan_needs is empty/blank.\n\nreason (Text String):\n\nIf rating is '1', state that vitals documentation and summary are consistent and compliant with referral orders (if any), noting missing referral orders if applicable (e.g., \"Vitals documented and summary consistent. Compliant with referral orders.\", \"Vitals summary internally consistent. No specific clinical parameters ordered in referral.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Missing ordered SpO2.', 'Inconsistency: Vitals marked WNL despite documented BP 180/100.', 'Inconsistency: Vitals not WNL, but no active problem documented.', 'Inconsistency: Active problem (High BP) documented, but no care plan need listed.').\n\nalt_rationale (Text String): If rating is '0', explain the expected action or correction for the first or most significant discrepancy (e.g., 'Referral ordered SpO2 monitoring.', 'Documented BP of 180/100 is not Within Normal Limits.', 'Active problem needs to be specified when vitals are not WNL.', 'Care plan need required to address documented High BP.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Document missing SpO2.\"', '\"Set vitals_wnl to 2 (No).\"', '\"Add specific active problem related to abnormal vital.\"', '\"Add care plan need for managing High BP.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Vitals: { ..., \"blood_pressure\": { \"reading\": 130/80, ... }, \"spo2\": { \"reading\": 97, ... }, \"vitals_summary\": { \"vitals_wnl\": \"1\", \"vitals_active_problems\": \"\", \"vitals_care_plan_needs\": \"\", ... } }", "Referral Orders: { ..., \"clinical_parameters\": { \"spo2\": \"Monitor O2 sats\", ... }, ...}"], "rationale": "Ordered SpO2 is documented. Documented vitals (BP 130/80, SpO2 97) are WNL. vitals_wnl is '1', and active_problems/care_plan_needs are appropriately blank. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Vitals documentation compliant with referral orders and summary is internally consistent.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Vitals: { ..., \"blood_pressure\": { \"reading\": 180/100, ... }, ..., \"vitals_summary\": { \"vitals_wnl\": \"1\", ... } } (Marked WNL despite high BP)", "Referral Orders: (Assume orders met or none specified)"], "rationale": "Internal inconsistency: vitals_wnl is '1' (Yes) despite clearly abnormal documented BP. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Inconsistency: Vitals marked WNL despite documented BP 180/100.\", \"alt_rationale\": \"Documented BP of 180/100 is not Within Normal Limits.\", \"alt_answer\": \"\\\"Set vitals_wnl to 2 (No) and document active problem/care plan need.\\\"\" }"}, {"context": ["SOC Vitals: { ..., \"pulse\": { \"rate\": 110, \"rhythm\": \"1\", ... }, ..., \"vitals_summary\": { \"vitals_wnl\": \"2\", \"vitals_active_problems\": \"\", \"vitals_care_plan_needs\": \"\", ... } } (Marked Not WNL, but no problem listed)", "Referral Orders: (Assume orders met or none specified)"], "rationale": "Internal inconsistency: vitals_wnl is '2' (No), but vitals_active_problems is empty. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Inconsistency: Vitals not WNL, but no active problem documented.\", \"alt_rationale\": \"Active problem (e.g., Tachycardia) needs to be specified when vitals are not WNL.\", \"alt_answer\": \"\\\"Add specific active problem related to abnormal vital (e.g., Tachycardia).\\\"\" }"}], "options": [], "description": "Verbalize your findings for:     \n -  Temperature     \n -  Pulse     \n -  Blood Pressure     \n -  Respiratory Rate     \n -  SpO2 (Oxygen Use if present)     \n -  Blood Sugar", "dependencies": [{"code": "ORDERS_REFERRAL", "source": "referral", "name": "Orders", "question": "Extract the specific services ordered, including the frequency and duration if specified, for Nursing, Physical Therapy, Occupational Therapy, Speech Therapy, Medical Social Work, and Home Health Aide. Also, capture any specified clinical parameters to monitor (e.g., vital sign ranges to report).", "description": "Specific Service Orders"}, {"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_HEENT_ASSMNT", "name": "QA_Head, Ears, Eyes, Neck & Throat (HEENT)", "id": 8291, "types": ["SOC_CHECKLIST", "SOC-DEV"], "sections": [681, 33], "source": "tasks", "source_id": "1977", "schema": {}, "mandatory": false, "notes": "Internal Consistency: SOC heent_summary vs. SOC detailed HEENT findings (headaches, B0200, B1000, swallowing, etc.).\n\nWNL status matches findings?\n\nWNL=No -> Active Problem documented?\n\nActive Problem -> Care Plan Need documented?\n\nReferral Context: SOC findings/summary vs. Ref active_problems_summary.heent & Ref baseline_cognitive_status (vision/hearing).\nRate '0' for internal inconsistencies OR significant contradictions with available referral context.", "enabled": true, "question": "Review the SOC 'Head, E<PERSON>, Eyes, Neck & Throat (HEENT)' assessment findings (headaches, hearing, vision, swallowing, etc.) and the corresponding heent_summary object (heent_wnl, heent_active_problems, heent_care_plan_needs).\n\nAssess the internal consistency: Does heent_wnl reflect the detailed findings? If not WNL, are heent_active_problems and heent_care_plan_needs appropriately documented?\n\nCompare against relevant available referral context ('Functional, Cognitive & Body System Context_REFERRAL' -> active_problems_summary.heent, baseline_cognitive_status for vision/hearing history).\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all of the following are true:\n\nThe SOC heent_wnl status is consistent with the detailed HEENT findings documented (e.g., not '1' if significant issues like major hearing/vision loss, new swallowing difficulty, severe headache are noted).\n\nIf heent_wnl is '2' (No), then heent_active_problems is not empty/blank.\n\nIf heent_active_problems is not empty, then heent_care_plan_needs is not empty/blank.\n\nThe documented findings and summary do not significantly contradict relevant HEENT information noted in the available referral context.\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nInternal inconsistency in the heent_summary (WNL status vs. findings, missing problem, or missing care need when indicated).\n\nA significant HEENT issue noted in the referral's active_problems_summary.heent or baseline status is seemingly ignored or contradicted in the SOC HEENT assessment without clear rationale.\n\nreason (Text String):\n\nIf rating is '1', state that the HEENT assessment and summary are internally consistent and align with available referral context, noting missing referral context if applicable (e.g., \"HEENT assessment and summary internally consistent and align with referral context.\", \"Internally consistent. Referral HEENT context missing.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Inconsistency: HEENT marked WNL despite documented vision impairment B1000=1.', 'Inconsistency: HEENT not WNL, but no active problem documented.', 'Inconsistency: Active problem (Hearing Loss) documented, but no care plan need listed.', 'Discrepancy: Referral noted active vertigo, but not addressed in SOC HEENT assessment.').\n\nalt_rationale (Text String): If rating is '0', explain the expected action or correction for the first or most significant discrepancy (e.g., 'Documented vision impairment means heent_wnl should be 2.', 'Active problem needs to be specified.', 'Care plan need required for Hearing Loss.', 'SOC assessment should address vertigo noted in referral.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Set heent_wnl to 2 (No).\"', '\"Add specific active problem for HEENT.\"', '\"Add care plan need for Hearing Loss.\"', '\"Update HEENT assessment to include evaluation of vertigo.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC HEENT: { ..., \"hearing\": { \"b0200_hearing\": \"0\", ... }, \"vision\": { \"b1000_vision\": \"0\", ... }, \"swallowing\": { \"difficulty\": \"1\", ... }, \"heent_summary\": { \"heent_wnl\": \"1\", \"heent_active_problems\": \"\", \"heent_care_plan_needs\": \"\" } } (All WNL)", "Referral Func/Cog: { ..., \"active_problems_summary\": { \"heent\": \"WNL\", ... }, \"baseline_cognitive_status\": { ... } } (No relevant baseline issues)"], "rationale": " SOC findings are WNL, summary is consistent (WNL=1, no problems/needs). Referral context aligns. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"HEENT assessment and summary internally consistent and align with referral context.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC HEENT: { ..., \"hearing\": { \"b0200_hearing\": \"2\", \"symptoms\": \"1\", ... }, ..., \"heent_summary\": { \"heent_wnl\": \"1\", ... } } (Hearing moderately impaired, but summary WNL=1)", "Referral: (Assume no conflicting info)"], "rationale": " Internal inconsistency: heent_wnl is '1' despite documented hearing impairment (B0200=2). Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Inconsistency: HEENT marked WNL despite documented hearing impairment (B0200=2).\", \"alt_rationale\": \"Documented hearing impairment means heent_wnl should be 2 (No).\", \"alt_answer\": \"\\\"Set heent_wnl to 2 (No) and document active problem/care plan need.\\\"\" }"}], "options": [], "description": "Interview/assess/verbalize      \n Are findings WNL?      \n (Consider  symptoms, medications, skilled needs, care plan.)     \n If not, describe…       \n     \n - Headache(s)     \n - Vision     \n - Hearing     \n - Swallowing     \n - Vocalizing     \n - Facial asymmetry      \n - Active problems and skilled needs", "dependencies": [{"code": "ORDERS_REFERRAL", "source": "referral", "name": "Orders", "question": "Extract the specific services ordered, including the frequency and duration if specified, for Nursing, Physical Therapy, Occupational Therapy, Speech Therapy, Medical Social Work, and Home Health Aide. Also, capture any specified clinical parameters to monitor (e.g., vital sign ranges to report).", "description": "Specific Service Orders"}, {"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_M1324", "name": "Stage 1 / Stage 2...", "id": 3270, "types": ["SOC_CHECKLIST"], "sections": [38], "source": "tasks", "source_id": "334", "schema": {}, "mandatory": false, "notes": "To verify the staging of the most problematic pressure ulcer/injury, we need to look at multiple pressure injury/wound related data points to ensure consistency. This includes the presence of pressure ulcers, counts of different stages, and overall wound assessment data", "enabled": true, "question": "Based on the OASIS E item code M1324; What is the stage of the most problematic unhealed pressure ulcer / injury that is visible or can be shown (Excludes pressure ulcer / injury that cannot be shown due to non-removable dressing/device coverage of wound)?", "inputType": "text", "criteria": "1. First verify M1306 indicates presence of unhealed pressure ulcer/injury at Stage 2 or higher\n2. Compare M1324 staging with individual stage counts in M1311A-F to ensure consistency\n3. Cross-reference with SKIN_INJURY_TYPE and PRESSURE_ULCER_STAGING to verify wound classification\n4. Review wound measurements to help validate staging assessment (deeper wounds typically indicate higher stages)\n5. The most problematic ulcer should align with the highest stage reported in M1311A-F\n6. Ensure staging follows standard pressure injury staging guidelines", "examples": [], "options": [], "description": "Inspect, assess, observe.      \n     \n Excludes pressure ulcer/injury that cannot be staged due to a non-removable dressing/device, coverage of wound bed by slough and/or eschar, or deep tissue injury.     \n 1  - Stage 1     \n 2  - Stage 2     \n 3  - Stage 3     \n 4  - Stage 4      \n NA  - Patient has no pressure ulcers/injuries or no stageable pressure ulcers/injuries", "dependencies": [{"code": "OT_ORDERS", "source": "referral"}, {"code": "F2F_ENCOUNTER_NOTE", "source": "referral"}, {"code": "PT_ORDERS", "source": "referral"}, {"code": "NURSING_ORDERS", "source": "referral"}, {"code": "ST_ORDERS", "source": "referral"}, {"code": "M1306", "source": "tasks"}, {"code": "M1311A", "source": "tasks"}, {"code": "M1311B", "source": "tasks"}, {"code": "M1311C", "source": "tasks"}, {"code": "M1311D", "source": "tasks"}, {"code": "M1311E", "source": "tasks"}, {"code": "M1311F", "source": "tasks"}, {"code": "SKIN_INJURY_TYPE", "source": "tasks"}, {"code": "PRESSURE_ULCER_STAGING", "source": "tasks"}, {"code": "SKIN_INJURY_MEASUREMENT_LENGTH", "source": "tasks"}, {"code": "SKIN_INJURY_MEASUREMENT_WIDTH", "source": "tasks"}, {"code": "SKIN_INJURY_MEASUREMENT_DEPTH", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_PULMONARY_ASSMNT", "name": "QA_Pulmonary", "id": 8289, "types": ["SOC_CHECKLIST", "SOC-DEV"], "sections": [681, 33], "source": "tasks", "source_id": "1974", "schema": {}, "mandatory": false, "notes": "Internal Consistency: SOC summary vs. SOC detailed pulmonary findings (m1400, effort, auscultation).\n\nWNL status matches findings?\n\nWNL=No -> Active Problem documented?\n\nActive Problem -> Care Plan Need documented?\n\nrespiratory_medication_and_treatment vs. SOC MEDICATIONS.\n\nReferral Context: SOC findings/summary/treatments vs. Ref active_problems_summary & Ref special_treatments.respiratory_therapies.\nRate '0' for internal inconsistencies OR significant contradictions with available referral context.", "enabled": true, "question": "Review the SOC 'Pulmonary' assessment findings (m1400 Dyspnea, respiratory_effort, lung_fields_auscultation, respiratory_medication_and_treatment) and the summary object (pulmonary_wnl, pulmonary_active_problems, pulmary_care_plan_needs).\n\nAssess internal consistency: Does pulmonary_wnl reflect detailed findings? If not WNL, are pulmonary_active_problems and pulmary_care_plan_needs documented? Is respiratory_medication_and_treatment consistent with SOC MEDICATIONS?\n\nCompare against relevant available referral context (Referral active_problems_summary for respiratory issues, Referral special_treatments for O2/respiratory therapies).\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all of the following are true:\n\nThe SOC pulmonary_wnl status is consistent with the detailed pulmonary findings documented (e.g., not '1' if M1400>0, abnormal effort, or abnormal auscultation).\n\nIf pulmonary_wnl is '2' (No), then pulmonary_active_problems is not empty/blank.\n\nIf pulmonary_active_problems is not empty, then pulmary_care_plan_needs is not empty/blank.\n\nThe documented respiratory_medication_and_treatment aligns with relevant meds in the SOC MEDICATIONS list.\n\nThe documented findings and summary do not significantly contradict relevant pulmonary information noted in the available referral context (active problems, special treatments).\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nInternal inconsistency in the summary (WNL status vs. findings, missing problem, or missing care need).\n\nInternal inconsistency between respiratory_medication_and_treatment and SOC MEDICATIONS.\n\nA significant pulmonary issue or treatment (like O2 therapy) noted in the referral is seemingly ignored or contradicted in the SOC Pulmonary assessment without clear rationale.\n\nreason (Text String):\n\nIf rating is '1', state that the Pulmonary assessment and summary are internally consistent and align with available referral context, noting missing referral context if applicable (e.g., \"Pulmonary assessment and summary internally consistent and align with referral context.\", \"Internally consistent. Referral context for respiratory status missing.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Inconsistency: Pulmonary marked WNL despite M1400=2 (Dyspnea w/ mod exertion).', 'Inconsistency: Pulmonary not WNL, but no active problem documented.', 'Inconsistency: Active problem (SOB) documented, but no care plan need listed.', 'Discrepancy: Referral notes O2 therapy, but not mentioned in SOC O0110 or respiratory_medication_and_treatment.', 'Inconsistency: Albuterol listed in SOC Meds but not mentioned in respiratory_medication_and_treatment.').\n\nalt_rationale (Text String): If rating is '0', explain the expected action or correction for the first or most significant discrepancy (e.g., 'M1400=2 indicates pulmonary_wnl should be 2.', 'Active problem needs to be specified for dyspnea.', 'Care plan need required for SOB.', 'Referral indicates O2 use; should be documented in O0110/respiratory treatments.', 'Albuterol use should be noted in respiratory treatments.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Set pulmonary_wnl to 2 (No).\"', '\"Add specific active problem for Pulmonary.\"', '\"Add care plan need for SOB management.\"', '\"Add O2 therapy (e.g., C2) to O0110/respiratory treatments.\"', '\"Update respiratory_medication_and_treatment list.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Pulmonary: { \"m1400\": \"0\", \"respiratory_effort\": [\"1\"], \"lung_fields_auscultation\": [ { \"clear\": \"3\", \"sound\": \"1\", ... } ], \"respiratory_medication_and_treatment\": [], \"summary\": { \"pulmonary_wnl\": \"1\", \"pulmonary_active_problems\": \"\", \"pulmary_care_plan_needs\": \"\" } } (All WNL)", "SOC Meds: [...] (No respiratory meds)", "Referral Func/Cog: { ..., \"active_problems_summary\": { \"cardiovascular\": \"WNL\", ... } }", "Referral Med/Surg: { ..., \"special_treatments,_procedures_programs\": { \"respiratory_therapies\": [] } }"], "rationale": "SOC findings WNL, summary consistent (WNL=1). No respiratory meds/treatments in SOC or referral. Referral context aligns. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Pulmonary assessment and summary internally consistent and align with referral context.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}], "options": [], "description": "Interview/assess/verbalize     \n     \n Are findings WNL?     \n (Consider  symptoms, medications, skilled needs, care plan.)     \n If not, describe…     \n - Respiratory effort     \n - Lung fields auscultation     \n - Respiratory meds and treatments     \n          a) Nebulizer treatments and inhalers     \n          b) Tracheostomy inner cannula     \n          c) Oxygen and supports     \n - Active problems and skilled needs?", "dependencies": [{"code": "ORDERS_REFERRAL", "source": "referral", "name": "Orders", "question": "Extract the specific services ordered, including the frequency and duration if specified, for Nursing, Physical Therapy, Occupational Therapy, Speech Therapy, Medical Social Work, and Home Health Aide. Also, capture any specified clinical parameters to monitor (e.g., vital sign ranges to report).", "description": "Specific Service Orders"}, {"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "diagnoses_treatments", "source": "referral", "name": "Medical/Surgical History", "question": "Extract details about the patient's past surgical history and any relevant ongoing special treatments, procedures, or programs (like IV therapy, dialysis, oxygen, etc.) mentioned in the provided referral documentation.", "description": "Medical/Surgical History"}, {"code": "MEDICATIONS_SOC", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_MEDICATION_REVIEW_DETAILS", "name": "QA_Medication Review Details", "id": 8273, "types": ["SOC_CHECKLIST"], "sections": [29], "source": "tasks", "source_id": "234", "schema": {"type": "object", "properties": []}, "mandatory": false, "notes": "Compare:\n\nSOC 485_poc_17_allergies vs. Ref MEDICATION_REVIEW_DETAILS.allergies.\n\nSOC n01415 vs. Ref MEDICATION_REVIEW_DETAILS.n01415.\n\nInternal Check: SOC n01415 selections vs. drug classes in SOC MEDICATIONS.\nRate '0' for confirmed significant mismatches or inconsistencies based on available data.\n\n", "enabled": true, "question": "Review the SOC 'Medication Review Details', specifically Allergies (485_poc_17_allergies) and High-Risk Drug classes (n01415). Compare Allergies against available referral allergy data ('Medication Review Details_REFERRAL'). Compare High-Risk Drugs (n01415) against available referral data AND assess its internal consistency with the detailed SOC 'Medications' list. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if both Allergies are consistent with available referral data AND the High-Risk Drug selections (n01415) are consistent with available referral data AND accurately reflect the drug classes present in the SOC MEDICATIONS list. Minor discrepancies in referral data or missing referral data are acceptable if no direct contradictions are found.\n\nAssign '0' (Incorrect/Issue) if at least one significant mismatch or inconsistency is found in Allergies vs. Referral, OR n01415 vs. Referral, OR n01415 vs. SOC Medications.\n\nreason (Text String):\n\nIf rating is '1', state that Allergies and High-Risk Drugs (n01415) are consistent with available referral data and the SOC medication list, noting any key referral data missing (e.g., \"Allergies and n01415 consistent with referral and SOC meds.\", \"Allergies match referral; n01415 consistent with SOC meds, referral n01415 missing.\").\n\nIf rating is '0', state all specific significant discrepancies found (e.g., 'Allergy mismatch (SOC lists Penicillin, Ref lists NKDA).', 'n01415 inconsistency: Missing Anticoagulant indicated by <PERSON><PERSON><PERSON> on SOC med list.', 'n01415 mismatch vs Referral: Referral notes Opioid use, SOC n01415 does not reflect this.').\n\nalt_rationale (Text String): If rating is '0', explain the correct information based on the referral or SOC medication list for the first or most significant discrepancy (e.g., 'Referral states NKDA.', 'SOC medication list includes Warfarin (Anticoagulant - N0415E).', 'Referral indicates Opioid use (N0415H).'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', provide the expected JSON snippet for the specific field(s) needing correction based on the alt_rationale (e.g., '{ \"allergies\": { \"present\": \"2\", \"description\": \"NKDA\" } }', '{ \"n01415\": { \"n0415e\": [\"1\"] } }', '{ \"n01415\": { \"n0415h\": [\"1\"] } }'). Combine fields if appropriate. Leave blank if rating is '1'.", "examples": [{"context": ["SOC Med Review: { \"allergies\": { \"present\": \"1\", \"description\": \"Penicillin\" }, \"n01415\": { \"n0415e\": [\"1\"], \"n0415j\": [\"1\"] } }", "SOC Meds: [ { \"name\": \"<PERSON>fari<PERSON>\", ... }, { \"name\": \"Insulin\", ... } ]", "Referral Med Review: { \"allergies\": { \"present\": \"1\", \"description\": \"Penicillin\" }, \"n01415\": { \"n0415e\": [\"1\"], \"n0415j\": [\"1\"] } }"], "rationale": "Allergies match referral. SOC n01415 matches referral AND is consistent with <PERSON><PERSON><PERSON> (Anticoagulant) and <PERSON><PERSON><PERSON> (Hypoglycemic) on SOC med list. Rating '1'.", "answer": "", "assessment": "{ \"rating\": \"1\", \"reason\": \"Allergies and n01415 High-Risk Drugs consistent with referral and SOC medications.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Med Review: { \"allergies\": { \"present\": \"2\", \"description\": \"NKDA\" }, \"n01415\": { \"n0415z\": \"1\" } } (n0415z=1 means None of the above)", "SOC Meds: [ { \"name\": \"Aspirin\", \"purpose\": \"Antiplatelet\", ... }, { \"name\": \"Lisinop<PERSON>\", ... } ]", "Referral Med Review: { \"allergies\": { \"present\": \"2\", \"description\": \"NKDA\" }, \"n01415\": { \"n0415z\": \"1\" } }"], "rationale": "Allergies match referral. SOC n01415 matches referral. HOWEVER, SOC n01415 is internally inconsistent because As<PERSON>rin (Antiplatelet) is on the SOC med list, but n01415 says 'None'. Rating '0'.", "answer": "", "assessment": "{ \"rating\": \"0\", \"reason\": \"n01415 inconsistency: Missing Antiplatelet (N0415I) indicated by <PERSON><PERSON><PERSON> on SOC med list.\", \"alt_rationale\": \"SOC medication list includes Aspirin (Antiplatelet); n01415 should reflect N0415I.\", \"alt_answer\": \"{ \\\\\"n01415\\\\\": { \\\\\"n0415i\\\\\": [\\\\\"1\\\\\"], \\\\\"n0415z\\\\\": \\\\\"\\\\\" } }\" }"}, {"context": ["SOC Med Review: { \"allergies\": { \"present\": \"1\", \"description\": \"Codeine\" }, \"n01415\": { ... } }", "SOC Meds: [...] (Consistent with SOC n01415)", "Referral Med Review: { \"allergies\": { \"present\": \"2\", \"description\": \"NKDA\" }, \"n01415\": { ... } } (Assume n01415 matches)"], "rationale": "Allergy status/description mismatches between SOC and referral. Rating '0'.", "answer": "", "assessment": "{ \"rating\": \"0\", \"reason\": \"Allergy mismatch (SOC lists Codeine, Referral lists NKDA).\", \"alt_rationale\": \"Referral states NKDA (No Known Drug Allergies).\", \"alt_answer\": \"{ \\\\\"allergies\\\\\": { \\\\\"present\\\\\": \\\\\"2\\\\\", \\\\\"description\\\\\": \\\\\"NKDA\\\\\" } }\" }"}], "options": [], "description": "Verbalize the patient's allergies:     \n     \n - Do they have any?     \n - If so, what are they?", "dependencies": [{"code": "MEDICATION_REVIEW_DETAILS", "source": "referral", "name": "Medication Review Details", "question": "Extract information regarding the patient's known allergies and their use of specific high-risk drug classes (Antipsychotic, Anticoagulant, Antibiotic, Opioid, Antiplatelet, Hypoglycemic), as found in the provided referral documentation.", "description": "Medications & Allergies"}, {"code": "MEDICATIONS_REFERRAL", "source": "referral", "name": "Medications", "question": "Extract the list of medications the patient is currently taking, including details such as name, dose, strength, route, frequency, start/end dates, status, purpose, and any special instructions, as found in the provided referral documentation.", "description": "Medications & Allergies"}, {"code": "MEDICATIONS_SOC", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_Discharge_Details", "name": "QA_Discharge Details", "id": 8256, "types": ["SOC_CHECKLIST"], "sections": [28], "source": "tasks", "source_id": "227", "schema": {}, "mandatory": false, "notes": "Compare:\n\nSOC M1005 vs. Ref Recent_Acute_Care_Summary_REFERRAL.discharge_date.\n\nSOC M1000 consistency checks:\n\nIf Ref Recent_Acute_Care_Summary has data OR Ref Referral_Overview.source[0].facility_type is 1-7, then SOC M1000 should NOT be 'NA'.\n\nIf Ref Recent_Acute_Care_Summary is empty AND Ref Referral_Overview.source[0].facility_type is 'NA', then SOC M1000 SHOULD be 'NA'.\n\nIf Ref Referral_Overview.source[0].facility_type is 1-7, its value should be included in SOC M1000 selections.\nRate '0' only for confirmed mismatches in available data.", "enabled": true, "question": "Review the SOC 'Discharge Details' section (M1005 Inpatient Discharge Date, M1000 Inpatient Facilities Discharged From). Compare M1005 against the discharge date in the 'Recent Acute Care Summary_REFERRAL'. Assess if M1000 is consistent with the facility type in 'Referral_Overview_REFERRAL' source details and the presence/absence of a recent discharge in 'Recent Acute Care Summary_REFERRAL'. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if both M1005 matches the available referral discharge date AND M1000 selections are consistent with the available referral context (presence of recent discharge, referral source type), OR if referral data is missing preventing comparison but no mismatches are found. (See Notes).\n\nAssign '0' (Incorrect/Issue) if at least one mismatch is found in the date comparison OR in the consistency check for M1000 based on available referral data.\n\nreason (Text String):\n\nIf rating is '1', state that discharge details are consistent with available referral data, noting any key referral data missing (e.g., \"M1005 matches referral discharge date; M1000 consistent. Referral source facility type missing.\", \"Discharge details consistent.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'M1005 discharge date mismatch.', 'M1000 facility type inconsistency (SOC says SNF, Referral source was Hospital).', 'M1000 is NA but referral shows recent hospital discharge.').\n\nalt_rationale (Text String): If rating is '0' due to a discrepancy, explain the correct information based on the referral documentation for the first or most significant discrepancy noted (e.g., 'Referral Recent Acute Care Summary lists discharge date as 03/15/2025.', 'Referral Overview source facility type is Hospital (3); M1000 should include 3.', 'Referral shows recent discharge; M1000 should not be NA.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', provide the expected JSON snippet for the specific field(s) identified as discrepant in the alt_rationale based on referral data (e.g., '{ \"m1005\": \"03/15/2025\" }', '{ \"m1000\": \"Add '3' to selection\" }', '{ \"m1000\": \"Remove 'NA', select appropriate facility type(s)\" }'). Combine fields if appropriate. Leave blank if rating is '1'.", "examples": [{"context": ["SOC: { \"m1005\": \"03/15/2025\", \"m1000\": \"2\", ... } (M1000 = SNF)", "Referral Recent Acute Care: { \"discharge_date\": \"03/15/2025\", \"facility_name\": \"Anytown SNF\", ...}", "Referral Overview: { \"source\": [ { \"facility_type\": \"2\", ... } ], ...} (Source Type = SNF)"], "rationale": "M1005 matches. M1000 includes the referral source type (2) and is not NA, consistent with recent discharge.", "answer": "{ \"rating\": \"1\", \"reason\": \"M1005 discharge date and M1000 facility type consistent with referral data.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC: { \"m1005\": \"03/16/2025\", \"m1000\": \"2\", ... }", "Referral Recent Acute Care: { \"discharge_date\": \"03/15/2025\", ...}", "Referral Overview: { \"source\": [ { \"facility_type\": \"2\", ... } ], ...}"], "rationale": "M1005 mismatches the referral discharge date. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"M1005 discharge date mismatch.\", \"alt_rationale\": \"Referral Recent Acute Care Summary lists discharge date as 03/15/2025.\", \"alt_answer\": \"{ \\\\\"m1005\\\\\": \\\\\"03/15/2025\\\\\" }\" }"}], "options": [], "description": "Verbalize all that apply:     \n 1  - Long-term nursing facility (NF)     \n 2  - Skilled nursing facility (SNF/TCU)     \n 3  - Short-stay acute hospital (IPPS)     \n 4  - Long-term care hospital (LTCH)     \n 5  - Inpatient rehabilitation hospital or unit (IRF)     \n 6  - Psychiatric hospital or unit     \n 7  - Other (specify)     \n NA  - Patient was not discharged from an inpatient facility- skip to B0200 Hearing", "dependencies": [{"code": "Recent_Acute_Care_Summary_REFERRAL", "source": "referral", "name": "Recent Acute Care Summary", "question": "Extract key details about the patient's most recent acute care episode (e.g., hospitalization, SNF stay, ER visit, significant procedure) that is relevant to the current home health referral, as documented in the referral materials.", "description": "Recent Acute Care Summary"}, {"code": "Referral_Overview_REFERRAL", "source": "referral", "name": "Referral Overview", "question": " Extract key overview information about the home health referral, including primary diagnosis, source details, referral date, reason for referral, services ordered, and physician-ordered start date, from the provided referral documentation. Synthesize a concise narrative summary combining these key elements.", "description": "Referral Overview"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_COGNITIVE_STATUS_DETAILS", "name": "QA_Cognitive Status Details", "id": 8277, "types": ["SOC_CHECKLIST"], "sections": [32], "source": "tasks", "source_id": "270", "schema": {}, "mandatory": false, "notes": "Check:\n\nInternal Consistency: 485 POC 19 vs. M1700 vs. M1710. C1310 vs. M1710.\n\nReferral Context: SOC M1700/M1710 vs. Ref baseline_cognitive_status. SOC C1310/M1710 vs. Ref active_problems_summary.cognitive_status.\nRate '0' for internal inconsistencies OR significant unexplained contradictions with available referral context.", "enabled": true, "question": "Review the SOC 'Cognitive Status Details' (485 POC 19 Mental Status, M1700 Cognitive Functioning, M1710 When Confused, C1310 Delirium Signs/Sx).\n\nAssess internal consistency: Do 485 POC 19, M1700, and M1710 present a coherent picture of cognitive function? Are C1310 selections logical and consistent with confusion status (M1710)?\n\nCompare against relevant available referral context (Referral baseline_cognitive_status and active_problems_summary.cognitive_status). Are there significant, unexplained changes from baseline? Are active referral issues addressed?\nAssign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if all of the following are true:\n\nThe SOC cognitive assessments (485 POC 19, M1700, M1710, C1310) are internally consistent.\n\nThe documented cognitive status does not significantly contradict the available referral baseline or active cognitive status without explanation.\n\nAssign '0' (Incorrect/Issue) if at least one of the following is true:\n\nInternal inconsistency (e.g., M1700=0 but M1710 indicates frequent confusion; C1310 shows delirium signs but M1710=0).\n\nSignificant unexplained discrepancy between SOC assessment and referral baseline cognitive status.\n\nAn active cognitive issue (like delirium) noted in the referral context is seemingly ignored or contradicted in the SOC assessment.\n\nreason (Text String):\n\nIf rating is '1', state that the cognitive assessment is internally consistent and aligns with available referral context, noting missing referral context if applicable (e.g., \"Cognitive status details internally consistent and align with referral context.\", \"Internally consistent. Referral baseline cognitive status missing.\").\n\nIf rating is '0', state all specific discrepancies found (e.g., 'Inconsistency: M1700 indicates Alert/Oriented but M1710 shows constant confusion.', 'Inconsistency: C1310 indicates fluctuating inattention but M1710=0 (Never Confused).', 'Discrepancy: Referral baseline notes severe dementia, but SOC M1700 indicates requires prompting only under stress (1).', 'Discrepancy: Referral notes active delirium, but C1310 fields not addressed appropriately.').\n\nalt_rationale (Text String): If rating is '0', explain the expected action or correction for the first or most significant discrepancy (e.g., 'M1710 selection should align with M1700; M1700=0 implies M1710=0 or 1.', 'M1710 should reflect confusion if C1310 delirium signs present.', 'Referral baseline dementia suggests M1700 should be a higher level of impairment.', 'C1310 fields need completion based on delirium report.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Review and align M1700 and M1710 selections.\"', '\"Update M1710 based on C1310 findings.\"', '\"Re-evaluate M1700 considering baseline dementia.\"', '\"Complete C1310 delirium assessment.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC Cog Status: { \"485_poc_19\": [\"1\"], \"m1700\": \"0\", \"m1710\": \"0\", \"c1310\": { \"a_\": \"0\", \"b_\": \"0\", \"c_\": \"0\", \"d_\": \"0\" } } (A<PERSON><PERSON>, Inde<PERSON>, Never confused, No delirium signs)", "Referral Func/Cog: { \"baseline_cognitive_status\": { \"mental_status\": \"Alert & Oriented\", ... }, \"active_problems_summary\": { \"cognitive_status\": \"Baseline A&O\", ... } }"], "rationale": "All SOC fields are internally consistent (A&O aligns with M1700=0, M1710=0, C1310=all 0). Aligns with referral baseline and active status. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"Cognitive status details internally consistent and align with referral context.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC Cog Status: { ..., \"m1700\": \"0\", \"m1710\": \"4\", ... } (M1700=Alert/Independent, M1710=Constantly Confused)", "Referral: (Assume no conflicting info)"], "rationale": " Internal inconsistency: M1700 indicates high function, while M1710 indicates constant confusion. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"Inconsistency: M1700 indicates Alert/Oriented but M1710 shows constant confusion.\", \"alt_rationale\": \"M1700=0 (Alert/Independent) is inconsistent with M1710=4 (Constantly confused). Assessment needs clarification.\", \"alt_answer\": \"\\\"Review and align M1700 and M1710 selections based on patient presentation.\\\"\" }"}], "options": [], "description": "Verbalize all that apply:     \n     \n 1  - <PERSON><PERSON>     \n 2  - Comat<PERSON>     \n 3  - <PERSON><PERSON>     \n 4  - <PERSON><PERSON>     \n 5  - <PERSON>sorient<PERSON>     \n 6  - <PERSON><PERSON><PERSON>     \n 7  - <PERSON><PERSON><PERSON>     \n 8  - Other", "dependencies": [{"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "BIMS_ASSESSMENT", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_M1845", "name": "Toileting Hygiene", "id": 3264, "types": ["SOC_CHECKLIST"], "sections": [36], "source": "tasks", "source_id": "323", "schema": {}, "mandatory": false, "notes": "To verify toileting hygiene ability, we should look at related functional assessments, cognitive status, equipment needs, and other toileting-related data points to ensure consistency in the patient's assessed ability level", "enabled": true, "question": "Based on the OASIS E item code M1845; Does the patient have the ability to maintain perineal hygiene safely, adjust clothes and/or incontinence pads before and after using toilet commode bedpan urinal 1 managing ostomy, includes cleaning area around stoma but not managing equipment?", "inputType": "text", "criteria": "1. Compare M1845 toileting hygiene ability with GG0130C1/C2 toileting hygiene scores for alignment\n2. Verify toileting transfer ability (M1840, GG0170F1/F2) supports assessed hygiene level\n3. Check if cognitive status (M1700) impacts hygiene ability\n4. Review if presence of incontinence (M1610), bowel issues (M1620), or ostomy (M1630) affects assessed level\n5. Confirm DME and HHA orders align with assessed assistance needs\n6. All data points should tell consistent story about patient's toileting capabilities", "examples": [], "options": [], "description": "Current ability to maintain perineal hygiene safely, adjust clothes and/or incontinence pads before and after using toilet, commode, bedpan, urinal. If managing ostomy, includes cleaning area around stoma, but not managing equipment.     \n     \n 0  - Able to manage toileting hygiene and clothing management without assistance     \n     \n 1  - Able to manage toileting hygiene and clothing management without assistance if     \n supplies/implements are laid out for the patient     \n     \n 2  - Someone must help the patient to maintain toileting hygiene and/or adjust clothing     \n     \n 3   - Patient depends entirely upon another person to maintain toileting hygiene", "dependencies": [{"code": "HHA_ORDERS", "source": "referral"}, {"code": "M1610", "source": "tasks"}, {"code": "M1620", "source": "tasks"}, {"code": "M1630", "source": "tasks"}, {"code": "M1840", "source": "tasks"}, {"code": "GG0130C1", "source": "tasks"}, {"code": "GG0130C2", "source": "tasks"}, {"code": "GG0170F1", "source": "tasks"}, {"code": "GG0170F2", "source": "tasks"}, {"code": "M1700", "source": "tasks"}, {"code": "DME", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GG0170E1", "name": "Chair / Bed to Chair Transfer", "id": 2635, "types": ["SOC_CHECKLIST"], "sections": [35], "source": "tasks", "source_id": "316", "schema": {}, "mandatory": false, "notes": "To QA bed/chair transfer ability, we should consider multiple related mobility/transfer assessments, functional limitations, device usage, assistance needs, and safety factors that would validate the scoring. Scores between current and discharge goals should also be logically related.", "enabled": true, "question": "Based on the OASIS E item GG0170E1; Is the patient able to transfer to and from a bed to a chair (or wheelchair)?", "inputType": "text", "criteria": "1. Compare current transfer ability (E1) against related functional measures like bed mobility, sit-to-stand, and overall functional status to ensure consistency\n2. Verify transfer ability aligns with documented functional limitations, device needs, and homebound status\n3. Ensure discharge goal (E2) shows reasonable progression from current status while considering diagnoses and prior level\n4. Check that required equipment/DME supports documented transfer ability\n5. Cross-reference M1850 transfer score to validate similar functional assessment\n6. Verify narrative documentation of transfer status matches scored ability level", "examples": [], "options": [], "description": "The ability to transfer to and from a bed to a chair (or wheelchair).", "dependencies": [{"code": "HOMEBOUND_CRITERIA_1", "source": "referral"}, {"code": "GG0170B1", "source": "tasks"}, {"code": "GG0170B2", "source": "tasks"}, {"code": "GG0170D1", "source": "tasks"}, {"code": "GG0170D2", "source": "tasks"}, {"code": "485_POC_18_A", "source": "tasks"}, {"code": "485_POC_18_B", "source": "tasks"}, {"code": "MUSCULOSKELETAL_STATUS", "source": "tasks"}, {"code": "MUSCULOSKELETAL_DEVICE", "source": "tasks"}, {"code": "CHAIR_BED_TRANSFER", "source": "tasks"}, {"code": "FUNCTIONAL_STATUS", "source": "tasks"}, {"code": "DME", "source": "tasks"}, {"code": "M1850", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}, {"code": "QA_GG0170", "name": "QA_(GG0170) Mobility", "id": 8282, "types": ["SOC_CHECKLIST"], "sections": [35], "source": "tasks", "source_id": "306", "schema": {}, "mandatory": false, "notes": "Compare SOC GG0170 items against:\n\nSOC Context: M1840 (Toilet Tx), M1860 (Ambulation), Mobility_Safety_Aid (Activities, DME).\n\nReferral Context: Ref active_problems_summary.functional_status, Ref reason, Ref Recent_Acute_Care, Ref Home Safety (stairs).\nFocus on consistency regarding current mobility/transfer abilities. Rate '0' for significant contradictions based on available data.", "enabled": true, "question": "Review the SOC assessment of current mobility performance (GG0170A Roll, GG0170B Sit to Lying, GG0170C Lying to Sit, GG0170D Sit to Stand, GG0170E Chair Transfer, GG0170F Toilet Transfer, GG0170G Car Transfer, GG0170I-O Walking/Steps, GG0170P Pick up object, GG0170Q-S Wheeling). Compare these against:\n\nCorresponding SOC ADL assessments (M1840 Toilet Transfer, M1860 Ambulation).\n\nOther SOC functional context (Mobility_Safety_Aid Activities Permitted, DME).\n\nRelevant available referral context describing current functional abilities (Referral active_problems_summary.functional_status, reason, Recent_Acute_Care_Summary context, home safety for stairs).\nAssess for general consistency. Assign a rating and provide a reason.", "inputType": "json", "criteria": "Respond with a JSON object containing the following properties:\n\nrating (Select Option Value):\n\nAssign '1' (Correct) if the GG0170 selections generally align with the corresponding M1840/M1860 assessments, other SOC functional context (DME use, permitted activities), AND with the overall picture of the patient's current mobility abilities described in the available referral context. Minor differences in granularity are acceptable if directionally consistent.\n\nAssign '0' (Incorrect/Issue) if there is a significant contradiction between one or more GG0170 selections and either the M1840/M1860 assessments, other SOC functional context, OR the available referral context regarding current mobility function (e.g., GG0170I shows walking independence, M1860 shows bedfast; GG0170E shows transfer dependence, referral states independent transfers).\n\nreason (Text String):\n\nIf rating is '1', state that the GG0170 Mobility assessment is consistent with M1800s, other SOC function fields, and available referral context, noting key missing referral context if applicable (e.g., \"GG0170 consistent with M1800s, Mobility Safety Aid, and referral context.\", \"Consistent; Referral lacks details on current mobility problems.\").\n\nIf rating is '0', state all specific significant discrepancies found (e.g., 'GG0170I inconsistency: GG indicates Walking Indep (06), M1860 indicates Bedfast (6).', 'GG0170E inconsistency: GG indicates Transfer Dependence (01), referral active problems state Independent transfers.', 'GG0170N/O inconsistency: GG indicates unable to do steps (88), but referral notes patient lives on 2nd floor w/o elevator.').\n\nalt_rationale (Text String): If rating is '0', explain the expected status based on the conflicting data source (M1800s, other SOC func, or referral) for the first or most significant discrepancy (e.g., 'M1860 score of 6 suggests GG0170I/J/K should be 01 or 88.', 'Referral context implies transfer independence, suggesting GG0170E needs review.', 'Patient living situation implies ability to manage stairs (GG0170N/O) needs reassessment or clarification.'). Leave blank if rating is '1'.\n\nalt_answer (Text String): If rating is '0', indicate the expected corrective action (e.g., '\"Correct GG0170I/J/K based on M1860.\"', '\"Review GG0170E based on referral context.\"', '\"Re-evaluate GG0170N/O based on living situation or document limitations.\"'). Leave blank if rating is '1'.", "examples": [{"context": ["SOC GG0170: { \"gg0170i\": \"03\", \"gg0170j\": \"03\", \"gg0170d\": \"03\", \"gg0170e\": \"03\", ... } (Walking/Transfers Mod Assist)", "SOC M1800s: { \"m1860\": \"4\", \"m1840\": \"1\", ... } (Walks w/ Supervision/Assist, Toilet Tx w/ Supervision/Assist)", "Referral Func/Cog: { \"active_problems_summary\": { \"functional_status\": \"Needs mod assist amb/transfers post CVA\", ... } }"], "rationale": "GG0170 scores indicating moderate assistance align reasonably with M1860/M1840 indicating supervision/assistance, and with the referral context. Rating '1'.", "answer": "{ \"rating\": \"1\", \"reason\": \"GG0170 Mobility consistent with M1800s ADLs and referral context.\", \"alt_rationale\": \"\", \"alt_answer\": \"\" }"}, {"context": ["SOC GG0170: { ..., \"gg0170i\": \"06\", ... } (Walk 10 ft Independent)", "SOC M1800s: { ..., \"m1860\": \"6\", ... } (Bedfast)", "Referral: (Assume confirms bedbound status)"], "rationale": "Significant internal contradiction: GG0170I indicates walking independence, while M1860 indicates bedfast. Rating '0'.", "answer": "{ \"rating\": \"0\", \"reason\": \"GG0170I inconsistency: GG indicates Walking Indep (06), M1860 indicates Bedfast (6).\", \"alt_rationale\": \"M1860 score of 6 (Bedfast) suggests GG0170I should be 01 (Dependent), 07 (Refused), 09 (NA), 10 (Env Limit), or 88 (Med/Safety).\", \"alt_answer\": \"{ \\\\\"gg0170i\\\\\": \\\\\"Update based on M1860\\\\\" }\" }"}, {"context": [], "rationale": "", "answer": ""}], "options": [], "description": "The ability to roll from lying on back to left and right side, and return to lying on back on the bed.", "dependencies": [{"code": "FUNCTIONAL_COGNITIVE_BODY_SYSTEM_CONTEXT", "source": "referral", "name": "Functional, Cognitive & Body System Context", "question": "Extract information about the patient's baseline (pre-morbid/prior) functional status (mobility, transfers, ADLs, etc.) and baseline cognitive status (mental status, memory, communication, etc.). Also, document any currently active problems noted by body system (vitals, pain, cardiovascular, integumentary, etc.) as summarized in the provided referral documentation.", "description": " Functional, Cognitive & Body System Context "}, {"code": "Referral_Overview_REFERRAL", "source": "referral", "name": "Referral Overview", "question": " Extract key overview information about the home health referral, including primary diagnosis, source details, referral date, reason for referral, services ordered, and physician-ordered start date, from the provided referral documentation. Synthesize a concise narrative summary combining these key elements.", "description": "Referral Overview"}, {"code": "Recent_Acute_Care_Summary_REFERRAL", "source": "referral", "name": "Recent Acute Care Summary", "question": "Extract key details about the patient's most recent acute care episode (e.g., hospitalization, SNF stay, ER visit, significant procedure) that is relevant to the current home health referral, as documented in the referral materials.", "description": "Recent Acute Care Summary"}, {"code": "SOCIAL_ENVIRONMENTAL_CONTEXT_REFERRAL", "source": "referral", "name": "Social & Environmental Context", "question": "Extract information regarding the patient's social and environmental context, including their living situation, available support systems or caregivers, any known home safety concerns, and transportation needs or limitations, as described in the provided referral documentation.", "description": " Social & Environmental Context"}, {"code": "ADLs/IADLs_M1800s", "source": "tasks"}, {"code": "Mobility_Safety_Aid", "source": "tasks"}], "conditionals": {"conditions": [], "order": ""}}]