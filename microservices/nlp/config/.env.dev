HOST=0.0.0.0
PORT=3200
ENV=development
PATRIUM_ENV=dev

FIREWORKS_API_KEY=fw_3ZNxYa2hfVddvKsQtGY4fgmU
GEMINI_API_KEY=AIzaSyC1aBVpks-84Uz-CzQVO6wg4RHH2L2MUmA
OPENAI_API_KEY=********************************************************
GROQ_API_KEY=********************************************************
CEREBRAS_API_KEY=csk-n4trhf8pm2pffe9et9eed2p8ncj6pc2x3h9598mcrtmk4n2x
DEEPGRAM_API_KEY=****************************************
ANTHROPIC_API_KEY=************************************************************************************************************

BASETEN_API_KEY=744tj7Li.pbxbPMdyqRYQANjbARt6WKc752xXpeXC
BASETEN_EMBEDDING_URL=https://model-jwd8nyeq.api.baseten.co/environments/production/predict

RUNPOD_API_KEY=rpa_QNHRRC9IXHOU4CUTPHG7X604ZAOATKKOGU5JFSQGm6dm1f
RUNPOD_EMBEDDING_URL=https://api.runpod.ai/v2/55vbrkh6ax6du8/runsync

IN_QUEUE=https://sqs.us-east-1.amazonaws.com/858023996327/patrium-health-development-nlp-input
OUT_QUEUE=https://sqs.us-east-1.amazonaws.com/858023996327/patrium-health-development-nlp-output

WEAVIATE_HOST=hybrid-db
WEAVIATE_PORT=8080

LANGFUSE_HOST=http://langfuse:3333
LANGFUSE_PUBLIC_KEY=pk-lf-24083c3f-aad7-4ada-ac3e-d60ba81024f1
LANGFUSE_SECRET_KEY=******************************************

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=tNvbjqAXyf+R1YE4cNVvUh3IiEQOaGF2Tzl/b7V4
AWS_REGION=us-east-1
AWS_S3_BUCKET=patrium-health-development

CONCURRENCY=26
MAX_RETRIES=3
RETRY_WAIT_SECS=5
MAX_TOKENS=4096
TEMPERATURE=0

DATABASE_HOST=************
DATABASE_PORT=5432
DATABASE_USER=patriumdev
DATABASE_PASSWORD=yau-prince-TOTEM
DATABASE_NAME=patrium

JUPYTER_PLATFORM_DIRS=1
