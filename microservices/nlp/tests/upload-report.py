#!/usr/bin/env python3
# Uploads the report to ReportPortal

import os
import sys
import json
import requests
import io
from pathlib import Path

# Ensure the file exists
report_file = Path(__file__).parent.parent / "report.xml"
if not report_file.exists():
    print(f"Report does not exist: {report_file}")
    sys.exit(0)

# Read the file content
with open(report_file, "rb") as f:
    file_content = f.read()

# Project URL and other data
project_url = "https://gitlab.patrium.health/engineering/backend/api"
ci_commit_sha = os.environ.get('CI_COMMIT_SHA', '')
ci_job_id = os.environ.get('CI_JOB_ID', '')
ci_commit_branch = os.environ.get('CI_COMMIT_BRANCH', '')

# Create JSON data
json_data = {
    "name": "NLP Service",
    "description": f"Commit: {project_url}/-/commit/{ci_commit_sha}",
    "attributes": [
        {
            "key": "project",
            "value": "Backend/API",
            "system": False
        },
        {
            "key": "branch",
            "value": ci_commit_branch,
            "system": False
        },
        {
            "key": "commit",
            "value": ci_commit_sha,
            "system": False
        }
    ]
}

# Convert JSON to bytes
json_bytes = io.BytesIO(json.dumps(json_data).encode('utf-8'))

# Create multipart form data
files = {
    'file': ('report.xml', file_content, 'text/xml'),
    'launchImportRq': ('json_data.json', json_bytes, 'application/json')
}

# Get API key from environment
rp_api_key = os.environ.get('RP_API_KEY', '')

# Send the request
try:
    response = requests.post(
        "https://qa.patrium.health/api/v1/plugin/backend/junit/import",
        headers={
            "authorization": f"Bearer {rp_api_key}"
        },
        files=files
    )
    print("Upload result:", response.text)
except Exception as e:
    print("Upload failed:", e)
