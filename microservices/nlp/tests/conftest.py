from unittest.mock import MagicMock, patch

import pytest

class TestTraceClient:
    def __init__(self):
        self.span = MagicMock()
        # Configure span to return itself for chaining
        self.span.return_value = self.span
        self.span.span.return_value = self.span
        self.span.event = MagicMock()
        self.span.end = MagicMock()
        self.update = MagicMock()
        self.generation = self.span
        self.event = MagicMock()

@pytest.fixture(scope="function")
def mock_trace():
    return TestTraceClient()

@pytest.fixture(scope="function")
def mock_signature():
    return MagicMock()

@pytest.fixture(scope="function")
def mock_lm():
    return MagicMock()


@pytest.fixture(scope="function")
def mock_thread_pool():
    with patch('comprehend.pipelines.common.ThreadPoolExecutor') as mock:
        yield mock
