from unittest.mock import <PERSON>Mock

from comprehend.utils import get_net_prompt_with_fallback


def test_get_net_prompt_with_fallback():
    """Test that a prompt can be returned from langfuse"""

    # Mock prompt returned by langfuse
    lf_prompt = MagicMock()
    lf_prompt.config = [{"key":"date", "type": "input"}]
    lf_prompt.compile = MagicMock()
    lf_prompt.compile.return_value = "langfuse_prompt"

    # Langfuse client
    langfuse = MagicMock()
    langfuse.get_prompt = MagicMock()
    langfuse.get_prompt.return_value = lf_prompt

    (prompt, _, _) = get_net_prompt_with_fallback("name", langfuse, {}, "fallback_prompt")

    assert  prompt == "langfuse_prompt"


def test_get_net_prompt_with_fallback_uses_fallback():
    """Test that the fallback prompt will be used if langfuse has an error"""

    # Langfuse client
    langfuse = MagicMock()
    langfuse.get_prompt = MagicMock()
    langfuse.get_prompt.side_effect = Exception("Failure")

    (prompt, _, _) = get_net_prompt_with_fallback("name", langfuse, {}, "fallback_prompt")

    assert  prompt == "fallback_prompt"
