from unittest.mock import MagicMock, patch, ANY

import pytest

from comprehend import transcript_pipeline
from comprehend.pipelines.common import ReqCtx
from comprehend.pipelines.transcript_ import validate_answers
from tests.conftest import TestTraceClient


@pytest.fixture(scope="function")
def mock_setup_trace():
    with patch('comprehend.pipelines.transcript_.setup_trace') as mock:
        mock.return_value = MagicMock()
        yield mock


@pytest.fixture(scope="function")
def mock_get_visit_data():
    with patch('comprehend.pipelines.transcript_.get_visit_data') as mock:
        yield mock


@pytest.fixture(scope="function")
def mock_questions_retriever():
    with patch('comprehend.pipelines.transcript_.questions_retriever') as mock:
        yield mock


@pytest.fixture(scope="function")
def mock_bulk_answer_questions():
    with patch('comprehend.pipelines.transcript_.bulk_answer_questions') as mock:
        yield mock


@pytest.fixture(scope="function")
def mock_validate_answers():
    with patch('comprehend.pipelines.transcript_.validate_answers') as mock:
        yield mock


@pytest.fixture(scope="function")
def mock_qa_pipeline():
    with patch('comprehend.pipelines.transcript_.qa_pipeline') as mock:
        yield mock


@pytest.fixture(scope="function")
def mock_delete_gemini_docs():
    with patch('comprehend.pipelines.transcript_.delete_gemini_docs') as mock:
        yield mock


async def test_transcript_pipeline(mock_setup_trace, mock_get_visit_data, mock_questions_retriever,
                                   mock_bulk_answer_questions, mock_validate_answers, mock_qa_pipeline,
                                   mock_delete_gemini_docs):
    """Test transcript_pipeline"""
    ctx: ReqCtx = {"user_id": "usr_", "agency_id": "ag_"}
    transcript = [
        {
            "end": 18.345001,
            "start": 2.8799999,
            "speaker": "CLINICIAN",
            "num_words": 35,
            "sentences": "I'm here with patient Jane Doe. Her date of birth is 12/09/1930. she is a 94 year old female. this is a start of care visit conducted on 07/10/2025 following a referral from doctor.",
            "timestamp": "00:00:02"
        }
    ]
    code = "6AAAA"
    visit_id = "v_"
    visit_type = "SOC"
    questions = [{
        "code": "PATIENT_LIVING_SITUATION",
        "question": "Please describe the patient's living situation.",
        "name": "Patient Living Situation"
    }]
    answers = [
        {
            "code": "PATIENT_LIVING_SITUATION",
            "answer": {
                "m1100": {
                    "m1100a": "04"
                },
                "m2102f": "0"
            },
            "snippets": [
                "[CLINICIAN - 00:02:06]: the patient lives alone. Her son provides assistance with ...",
            ],
            "rationale": "The transcript states, 'the patient lives alone,' which corresponds to M1100 option 'A'."
        }
    ]

    test_trace_client = TestTraceClient()
    mock_setup_trace.return_value = test_trace_client
    mock_bulk_answer_questions.return_value = answers
    mock_validate_answers.return_value = answers

    mock_get_visit_data.return_value = {
        "answers": [],
        "referral_responses": []
    }

    mock_questions_retriever.return_value = questions

    # Execute function
    result = await transcript_pipeline(ctx, transcript, code, visit_id, visit_type)

    # Assertions
    assert result == answers
    mock_setup_trace.assert_called()
    mock_get_visit_data.assert_called_with(ANY, visit_id)
    mock_questions_retriever.assert_called_with(ctx, que_type=visit_type)
    test_trace_client.span.assert_called_with(
        name="Answer Questions",
        input={
            "transcript": transcript,
            "questions": questions,
            "singles": []
        })
    # mock_bulk_answer_questions.assert_called_with(
    #     **{"span": ANY,
    #        "model_config": ANY,
    #        "chunk_size": ANY,
    #        "questions": questions,
    #        "transcript": transcript,
    #        "relevant_data": ANY,
    #        "current_visit": ANY}
    # )
    mock_validate_answers.assert_called()
    mock_delete_gemini_docs.assert_called()
    mock_qa_pipeline.assert_called()


async def test_transcript_pipeline_previous_answers(mock_setup_trace, mock_get_visit_data, mock_questions_retriever,
                                                    mock_bulk_answer_questions, mock_validate_answers, mock_qa_pipeline,
                                                    mock_delete_gemini_docs):
    """Test that previous answers from the visit are not used."""
    ctx: ReqCtx = {"user_id": "usr_", "agency_id": "ag_"}
    transcript = [
        {
            "end": 18.345001,
            "start": 2.8799999,
            "speaker": "CLINICIAN",
            "num_words": 35,
            "sentences": "I'm here with patient Jane Doe. Her date of birth is 12/09/1930. she is a 94 year old female. this is a start of care visit conducted on 07/10/2025 following a referral from doctor.",
            "timestamp": "00:00:02"
        }
    ]
    code = "6AAAA"
    visit_id = "v_"
    visit_type = "SOC"
    questions = [{
        "code": "PATIENT_LIVING_SITUATION",
        "question": "Please describe the patient's living situation.",
        "name": "Patient Living Situation"
    }]
    answers = []

    test_trace_client = TestTraceClient()
    mock_setup_trace.return_value = test_trace_client
    mock_bulk_answer_questions.return_value = answers
    mock_validate_answers.return_value = answers

    mock_get_visit_data.return_value = {
        "answers": [
            {
                "code": "PATIENT_LIVING_SITUATION",
                "answer": {
                    "m1100": {
                        "m1100a": "04"
                    },
                    "m2102f": "0"
                },
                "snippets": [
                    "[CLINICIAN - 00:02:06]: the patient lives alone. Her son provides assistance with ...",
                ],
                "rationale": "The transcript states, 'the patient lives alone,' which corresponds to M1100 option 'A'."
            }
        ],
        "referral_responses": []
    }

    mock_questions_retriever.return_value = questions

    # Execute function
    result = await transcript_pipeline(ctx, transcript, code, visit_id, visit_type)

    # Assertions
    assert result == answers
    mock_setup_trace.assert_called()
    mock_get_visit_data.assert_called_with(ANY, visit_id)
    mock_questions_retriever.assert_called_with(ctx, que_type=visit_type)
    test_trace_client.span.assert_called_with(
        name="Answer Questions",
        input={
            "transcript": transcript,
            "questions": questions,
            "singles": []
        })
    # mock_bulk_answer_questions.assert_called_with(
    #     **{"span": ANY,
    #        "model_config": ANY,
    #        "questions": questions,
    #        "transcript": transcript,
    #        "relevant_data": ANY,
    #        "current_visit": ANY}
    # )
    mock_bulk_answer_questions.assert_called()
    mock_validate_answers.assert_called()
    mock_delete_gemini_docs.assert_called()
    mock_qa_pipeline.assert_called()


async def test_transcript_pipeline_error_handling(mock_setup_trace, mock_get_visit_data, mock_questions_retriever,
                                                  mock_bulk_answer_questions, mock_validate_answers, mock_qa_pipeline,
                                                  mock_delete_gemini_docs):
    """Test transcript_pipeline error handling"""
    ctx: ReqCtx = {"user_id": "usr_", "agency_id": "ag_"}
    transcript = []
    code = "6AAAA"
    visit_id = "v_"
    visit_type = "SOC"

    test_trace_client = TestTraceClient()
    mock_setup_trace.return_value = test_trace_client

    mock_get_visit_data.side_effect = Exception("Failure")

    # Execute function
    with pytest.raises(Exception):
        await transcript_pipeline(ctx, transcript, code, visit_id, visit_type)

    # Assertions
    mock_setup_trace.assert_called()
    mock_get_visit_data.assert_called_with(ANY, visit_id)
    assert mock_get_visit_data.call_count > 1
    mock_delete_gemini_docs.assert_called()


async def test_validate_answers(mock_trace, mock_bulk_answer_questions):
    transcript = [
        {
            "end": 18.345001,
            "start": 2.8799999,
            "speaker": "CLINICIAN",
            "num_words": 35,
            "sentences": "I'm here with patient Jane Doe.",
            "timestamp": "00:00:02"
        }
    ]
    questions = [
        {"code": "PATIENT_NAME", "question": "What is the patient's name.", "name": "Patient Name"},
    ]
    answers = [
        {
            "code": "PATIENT_NAME",
            "answer": "Jane Doe",
            "snippets": [
                "[CLINICIAN - 00:02:06]: I'm here with patient Jane Doe.",
            ],
            "rationale": "The transcript states, 'I'm here with patient Jane Doe.'"
        }
    ]
    details = {
        "first_name": "Jane",
        "middle_name": "Q",
        "last_name": "Doe",
        "dob": "12/01/1980",
        "gender": "Female"
    }

    mock_bulk_answer_questions.return_value = answers

    result = await validate_answers(mock_trace, transcript, questions, details)

    assert result == answers
    mock_trace.span.assert_called_with(name="Validate Answers",
                                       input={"transcript": transcript, "questions": questions})
    mock_bulk_answer_questions.assert_called()


async def test_validate_answers_error_handling(mock_trace, mock_bulk_answer_questions):
    transcript = []
    questions = []
    details = {}

    mock_bulk_answer_questions.side_effect = Exception("Failure")

    with pytest.raises(Exception):
        await validate_answers(mock_trace, transcript, questions, details)

    mock_trace.span.assert_called_with(name="Validate Answers",
                                       input={"transcript": transcript, "questions": questions})
    assert mock_bulk_answer_questions.call_count > 1
