import pytest
from unittest.mock import MagicM<PERSON>, patch, ANY

from comprehend.pipelines.common import answer_questions, bulk_answer_questions, ModelConfig
from comprehend.pipelines.transcript_ import TRANSCRIPT_PROMPT
from comprehend.utils.constants import GE<PERSON>NI_MODEL
from google.genai import types

@pytest.fixture(scope="function")
def mock_get_execution_order():
    with patch('comprehend.pipelines.common.get_execution_order') as mock:
        yield mock

@pytest.fixture(scope="function")
def mock_build_prompt():
    with patch('comprehend.pipelines.common.build_prompt') as mock:
        yield mock

@pytest.fixture(scope="function")
def mock_execute_template():
    with patch('comprehend.pipelines.common.execute_template') as mock:
        yield mock

@pytest.fixture(scope="function")
def mock_chunk_list():
    with patch('comprehend.pipelines.common.chunk_list') as mock:
        yield mock

@pytest.fixture(scope="function")
def mock_gemini_client():
    with patch('comprehend.pipelines.common.gemini_client') as mock:
        yield mock

async def test_answer_questions_basic_functionality(
    mock_trace, mock_signature, mock_lm, mock_get_execution_order,
    mock_build_prompt, mock_execute_template
):
    """Test basic functionality with single question."""
    # Setup test data
    questions = [{"code": "PATIENT_LIVING_SITUATION", "question": "Please describe the patient's living situation.", "name": "Patient Living Situation"}]
    visit = {"id": "visit_123", "visit_date": "2023-01-01"}
    
    # Mock execution order to return questions in single group
    mock_get_execution_order.return_value = [questions]
    
    # Mock build_prompt to return template dict
    mock_template = {
        "code": "Q1",
        "lm": mock_lm,
        "predictor": MagicMock(),
        "prompt": "test prompt",
        "context": []
    }
    mock_build_prompt.return_value = mock_template
    
    # Mock execute_template to return answer
    mock_answer = {
        "code": "PATIENT_LIVING_SITUATION",
        "rationale": "The transcript states...",
        "answer": "The patient lives alone",
        "snippets": []
    }
    mock_execute_template.return_value = mock_answer
    
    # Execute function
    result = await answer_questions(mock_trace, questions, mock_signature, mock_lm, visit)
    
    # Assertions
    assert len(result) == 1
    assert result[0] == mock_answer
    
    # Verify span creation
    mock_trace.span.assert_called_with(
        name="Answer Questions",
        input={"questions": questions, "visit": visit}
    )
    
    # Verify execution order was called
    mock_get_execution_order.assert_called_once_with(questions)
    
    # Verify build_prompt was called
    mock_build_prompt.assert_called_once()
    
    # Verify execute_template was called
    mock_execute_template.assert_called_once()


async def test_answer_questions_multiple_with_dependencies(
    mock_trace, mock_signature, mock_lm, mock_get_execution_order,
    mock_build_prompt, mock_execute_template
):
    """Test multiple questions with dependencies."""
    questions = [
        {"code": "Q1", "question": "What are symptoms?", "dependencies": []},
        {"code": "Q2", "question": "What is diagnosis?", "dependencies": [{"code": "Q1"}]}
    ]
    visit = {"id": "visit_123", "visit_date": "2023-01-01"}
    
    # Mock execution order to return questions in dependency order
    mock_get_execution_order.return_value = [[questions[0]], [questions[1]]]

    # Mock templates and answers
    mock_template1 = {"code": "Q1", "lm": mock_lm, "predictor": MagicMock()}
    mock_template2 = {"code": "Q2", "lm": mock_lm, "predictor": MagicMock()}
    mock_build_prompt.side_effect = [mock_template1, mock_template2]
    
    mock_answer1 = {"code": "Q1", "rationale": "Symptoms observed", "answer": "Fever, cough"}
    mock_answer2 = {"code": "Q2", "rationale": "Based on symptoms", "answer": "Common cold"}
    mock_execute_template.side_effect = [mock_answer1, mock_answer2]
    
    # Execute function
    result = await answer_questions(mock_trace, questions, mock_signature, mock_lm, visit)
    
    # Assertions
    assert len(result) == 2
    assert result[0] == mock_answer1
    assert result[1] == mock_answer2
    
    # Verify execution order respected dependencies
    mock_get_execution_order.assert_called_once_with(questions)


async def test_answer_questions_concurrent_execution(
    mock_trace, mock_signature, mock_lm, mock_get_execution_order,
    mock_build_prompt, mock_execute_template, mock_thread_pool
):
    """Test concurrent execution with multiple chunks."""
    # Create multiple questions to trigger concurrent execution
    questions = [
        {"code": f"Q{i}", "question": f"Question {i}", "name": f"field_{i}"}
        for i in range(1, 6)
    ]
    visit = {"id": "visit_123", "visit_date": "2023-01-01"}
    
    mock_get_execution_order.return_value = [questions]
    
    # Setup ThreadPoolExecutor mock
    mock_executor = MagicMock()
    mock_thread_pool.return_value.__enter__.return_value = mock_executor
    
    # Mock templates and answers
    mock_templates = [{"code": f"Q{i}", "lm": mock_lm, "predictor": MagicMock()} for i in range(1, 6)]
    mock_build_prompt.side_effect = mock_templates
    
    mock_answers = [{"code": f"Q{i}", "rationale": f"Answer {i}", "answer": f"Result {i}"} for i in range(1, 6)]
    mock_executor.map.return_value = mock_answers
    
    # Execute function
    result = await answer_questions(mock_trace, questions, mock_signature, mock_lm, visit)
    
    # Assertions
    assert len(result) == 5
    
    # Verify ThreadPoolExecutor was used
    mock_thread_pool.assert_called()


async def test_answer_questions_with_previous_answers(
    mock_trace, mock_signature, mock_lm, mock_get_execution_order,
    mock_build_prompt, mock_execute_template, mock_chunk_list
):
    """Test with previous answers provided."""
    questions = [{"code": "Q1", "question": "What is the diagnosis?"}]
    visit = {"id": "visit_123", "visit_date": "2023-01-01"}
    prev_answers = [{"code": "Q0", "answer": "Previous result", "rationale": "Previous reasoning"}]

    mock_get_execution_order.return_value = [questions]
    mock_chunk_list.return_value = [questions]

    mock_template = {"code": "Q1", "lm": mock_lm, "predictor": MagicMock()}
    mock_build_prompt.return_value = mock_template

    mock_answer = {"code": "Q1", "rationale": "New reasoning", "answer": "New result"}
    mock_execute_template.return_value = mock_answer

    result = await answer_questions(
        mock_trace, questions, mock_signature, mock_lm, visit,
        prev_answers=prev_answers
    )

    # Assertions
    assert len(result) == 2  # Previous answers plus new answer
    assert result[1] == mock_answer

    # Verify build_prompt was called with previous answers
    build_prompt_call = mock_build_prompt.call_args
    assert build_prompt_call[1]['prev_answers'] == prev_answers


async def test_answer_questions_empty_list(
    mock_trace, mock_signature, mock_lm, mock_get_execution_order
):
    """Test behavior with empty questions list."""
    questions = []
    visit = {"id": "visit_123", "visit_date": "2023-01-01"}

    mock_get_execution_order.return_value = []

    # Execute function
    result = await answer_questions(mock_trace, questions, mock_signature, mock_lm, visit)

    # Assertions
    assert result == []

    # Verify span was created and ended
    mock_trace.span.assert_called_with(
        name="Answer Questions",
        input={"questions": questions, "visit": visit}
    )
    mock_trace.span.return_value.end.assert_called()


async def test_answer_questions_error_handling(
    mock_trace, mock_signature, mock_lm, mock_get_execution_order,
    mock_build_prompt, mock_execute_template, mock_chunk_list
):
    """Test error handling when build_prompt or execute_template fails."""
    questions = [{"code": "Q1", "question": "What is the diagnosis?"}]
    visit = {"id": "visit_123", "visit_date": "2023-01-01"}
    
    mock_get_execution_order.return_value = [questions]
    mock_chunk_list.return_value = [questions]
    
    # Mock build_prompt to raise an exception
    mock_build_prompt.side_effect = Exception("Build prompt failed")
    
    # Execute function and expect exception to propagate
    with pytest.raises(Exception, match="Build prompt failed"):
        await answer_questions(mock_trace, questions, mock_signature, mock_lm, visit)
    
    # Verify span was still created
    mock_trace.span.assert_called_with(
        name="Answer Questions",
        input={"questions": questions, "visit": visit}
    )

async def test_bulk_answer_questions(mock_trace, mock_gemini_client):
    visit_date = "2025-07-30 09:00:00"
    model_config = ModelConfig(
        model=GEMINI_MODEL,
        config=types.GenerateContentConfig(
            system_instruction=TRANSCRIPT_PROMPT(visit_date, {}),
            top_p=0.95,
            temperature=0.05,
            max_output_tokens=24576
        )
    )
    questions = [{"code": "PATIENT_NAME", "question": "What is the patient's name.", "name": "Patient Name"}]
    transcript = [
        {
            "end": 18.345001,
            "start": 2.8799999,
            "speaker": "CLINICIAN",
            "num_words": 35,
            "sentences": "I'm here with patient Jane Doe.",
            "timestamp": "00:00:02"
        }
    ]

    mock_response = MagicMock()
    mock_response.text = """
- code: "PATIENT_NAME"
  snippets:
    - "[CLINICIAN - 00:00:02]: I'm here with patient Jane Doe."
  rationale: "The clinician states the patient's name is Jane Doe."
  answer: "Jane Doe"
"""
    mock_usage_metadata = MagicMock()
    mock_usage_metadata.total_token_count = 1
    mock_usage_metadata.prompt_token_count = 1
    mock_usage_metadata.cached_content_token_count = 1
    mock_usage_metadata.candidates_token_count = 1
    mock_response.usage_metadata = mock_usage_metadata
    mock_gemini_client.models.generate_content.return_value = mock_response

    await bulk_answer_questions(mock_trace, model_config, questions, transcript=transcript)
    mock_trace.generation.assert_called()
    mock_gemini_client.models.generate_content.assert_called()
    assert mock_gemini_client.models.generate_content.call_count == 1


async def test_bulk_answer_questions_error_handling(
    mock_trace, mock_gemini_client, mock_thread_pool
):
    """Test error handling in bulk_answer_questions."""
    visit_date = "2025-07-30 09:00:00"
    model_config = ModelConfig(
        model=GEMINI_MODEL,
        config=types.GenerateContentConfig(
            system_instruction=TRANSCRIPT_PROMPT(visit_date, {}),
            top_p=0.95,
            temperature=0.05,
            max_output_tokens=24576
        )
    )
    questions = [
        {"code": "PATIENT_NAME", "question": "What is the patient's name.", "name": "Patient Name"},
        {"code": "PATIENT_AGE", "question": "What is the patient's age.", "name": "Patient Age"}
    ]
    transcript = [
        {
            "end": 18.345001,
            "start": 2.8799999,
            "speaker": "CLINICIAN",
            "num_words": 35,
            "sentences": "I'm here with patient Jane Doe.",
            "timestamp": "00:00:02"
        }
    ]

    # Mock gemini_client.models.generate_content to raise an exception
    mock_gemini_client.models.generate_content.side_effect = Exception("LLM generation failed")

    ans = await bulk_answer_questions(mock_trace, model_config, questions, transcript=transcript)

    assert ans == []
    mock_trace.generation.assert_called()
    mock_trace.event.assert_called_with(name="Missing Answer", input=ANY)
    mock_gemini_client.models.generate_content.assert_called()
    # Retried at least once
    assert mock_gemini_client.models.generate_content.call_count > 1
