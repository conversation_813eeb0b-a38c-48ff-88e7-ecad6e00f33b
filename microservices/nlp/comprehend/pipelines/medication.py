import base64
import io
from typing import List

import requests
from google.genai import types
from loguru import logger

from services.aws.s3 import get_s3_url
from . import langfuse, gemini_client
from ..utils import extract_json, upload_to_gemini, delete_gemini_docs, remove_code_backticks, \
    generate_object_id
from ..utils.constants import GEMINI_MODEL
from ..utils.monitor import setup_trace

MEDICATION_PROMPT = """Respond with an array of plain JSON (without backticks or content type) with the medications name, route, class, dose, strength, purpose and frequency.
If medication is not available respond with an empty array; for empty medicine properties respond with empty strings.
Use the following JSON format:
[{
    "name": "MEDICATION NAME",
    "route": "ROUTE",
    "class": "MEDICATION CLASS",
    "dose": "DOSAGE",
    "strength": "STRENGTH",
    "purpose": "PURPOSE",
    "frequency": "FREQUENCY"
}]"""


def get_mime_type(url: str) -> str:
    extension = url.lower().split('.')[-1]
    mime_types = {
        'png': 'image/png',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'gif': 'image/gif',
        'webp': 'image/webp',
        'bmp': 'image/bmp',
        'tiff': 'image/tiff',
        'tif': 'image/tiff'
    }
    return mime_types.get(extension, 'image/jpeg')  # Default to JPEG if extension not found


def get_as_base64(url):
    return base64.b64encode(requests.get(url).content).decode('utf-8')


def medication_pipeline(image_urls: List[str], bucket: str = None):
    """Process the image using Vision API."""

    run_id = generate_object_id('med')
    trace = setup_trace(
        langfuse,
        id=run_id,
        name="Medication Pipeline",
        input={"image_urls": image_urls},
        metadata={"bucket": bucket}
    )

    logger.debug(f'Processing medication images :: {image_urls} :: {bucket}')

    urls = [get_s3_url(url, bucket) for url in image_urls]
    _image_names = []

    def call_vision_api():
        """Call Vision API."""
        logger.info(f"Calling vision API: {bucket} :: {urls}")

        # Upload images to Gemini
        images: List[types.File] = []
        for ix, url in enumerate(urls):
            name = f"{run_id}-{ix}".replace("_", "-").replace(" ", "-")
            _image_names.append(name)
            r = requests.get(url)
            mime_type = get_mime_type(url)
            images.append(upload_to_gemini(
                client=gemini_client,
                path=io.BytesIO(r.content),
                name=name,
                mime_type=mime_type
            ))

        gen = trace.generation(
            name="Medication Generation",
            model=GEMINI_MODEL,
            model_parameters={
                "prompt": MEDICATION_PROMPT,
                "temperature": str(0.1),
                "top_p": str(0.95)
            },
            input={"images": _image_names}
        )

        response = gemini_client.models.generate_content(
            model=GEMINI_MODEL,
            config=types.GenerateContentConfig(
                system_instruction=MEDICATION_PROMPT,
                temperature=0.2,
                top_p=0.95,
                response_mime_type='application/json'
            ),
            contents=[
                types.Content(
                    role="user",
                    parts=[
                        *[types.Part.from_uri(file_uri=image.uri, mime_type=image.mime_type) for image in images]
                    ]
                )
            ]
        )

        # Send a message to process the images
        metadata = response.usage_metadata

        gen.update(output={
            "metadata": {
                "total_token_count": metadata.total_token_count,
                "prompt_token_count": metadata.prompt_token_count,
                "cached_content_token_count": metadata.cached_content_token_count,
                "candidates_token_count": metadata.candidates_token_count,
            },
            "response": response.text
        })

        return response.text

    # validate json response, if valid return else call again
    try:
        result = call_vision_api()
        logger.info(f"RESULT 1: {result}")
        result = remove_code_backticks(result)

        trace.update(output={"result": result})
        return extract_json(result)
    except Exception as e:
        logger.error(f"Exception, calling again... {e}")
        result2 = call_vision_api()
        logger.info(f"RESULT 2: {result2}")
        result2 = remove_code_backticks(result2)

        trace.update(output={"result2": result2})
        return extract_json(result2)
    finally:
        logger.debug(f'Medication processing complete :: {image_urls}')
        # Clean up the uploaded images
        delete_gemini_docs(gemini_client, _image_names)
