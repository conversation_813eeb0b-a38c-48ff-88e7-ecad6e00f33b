import json
import warnings

import dspy
import weaviate
from google import genai
from weaviate.config import AdditionalConfig, Timeout

from config import settings
from packages.dsp_bind import google
from packages.langfuse import Langfuse
from ..utils.constants import MODEL, OPENAI_MODEL, GEMINI_MODEL, GEMINI_PRO_MODEL

langfuse = Langfuse(
    host=settings.LANGFUSE_HOST,
    public_key=settings.LANGFUSE_PUBLIC_KEY,
    secret_key=settings.LANGFUSE_SECRET_KEY
)

gemini_client = genai.Client(api_key=settings.GEMINI_API_KEY)

ENV = settings.ENV
IS_PRODUCTION = ENV == "production"
MAX_TOKENS = settings.MAX_TOKENS
TEMPERATURE = settings.TEMPERATURE
MAX_RETRIES = settings.MAX_RETRIES
CONCURRENCY = settings.CONCURRENCY
RETRY_WAIT_SECS = settings.RETRY_WAIT_SECS

if IS_PRODUCTION:
    warnings.filterwarnings("ignore")

google_llm_model = google.Gemini(
    model=GEMINI_MODEL,
    api_key=settings.GEMINI_API_KEY,
    max_output_tokens=MAX_TOKENS * 2,
    temperature=TEMPERATURE
)

gemini_pro_llm_model = google.Gemini(
    model=GEMINI_PRO_MODEL,
    api_key=settings.GEMINI_API_KEY,
    max_output_tokens=MAX_TOKENS * 2,
    temperature=TEMPERATURE
)

dspy.settings.configure(lm=google_llm_model)


if settings.CI:
    weaviate_client = weaviate.connect_to_embedded(
        version="1.26.1",
        headers={
            "X-OpenAI-Api-Key": settings.OPENAI_API_KEY
        },
    )
else :
    weaviate_client = weaviate.connect_to_local(
        host=settings.WEAVIATE_HOST,
        port=settings.WEAVIATE_PORT,
        grpc_port=50051,
        headers={"X-Openai-Api-Key": settings.OPENAI_API_KEY},
        additional_config=AdditionalConfig(timeout=Timeout(query=360 * 60, insert=360 * 60))
    )

# TODO: dynamically get ignore list
to_ignore = ['EP_GOALS', 'EP_INTERVENTIONS']
# to_ignore = ['EP_MEDICATIONS', 'MEDICATIONS', 'VISIT_SUMMARY', 'EP_GOALS', 'EP_INTERVENTIONS']


def ignore_question(que: dict):
    return que.get('code', '') in to_ignore


def get_model_name(lm=None):
    if lm is None:
        return MODEL
    else:
        model_class = lm.__class__.__name__
        if model_class == 'GPT3':
            return OPENAI_MODEL
        elif model_class == 'Google' or model_class == 'Gemini':
            return GEMINI_MODEL
        return ''


def get_model_config(lm=None, trace=False, **model_opts):
    def check(model):
        to_json = {'type': 'json_object'}
        general_opts = {
            "max_tokens": MAX_TOKENS,
            "temperature": TEMPERATURE,
        }
        if 'gpt-' in model or 'fireworks' in model:
            return {
                "response_format": json.dumps(to_json) if trace else to_json,
                **general_opts,
                **lm.kwargs
            }
        elif 'claude' in model:
            return {
                **general_opts,
                **lm.kwargs
            }
        elif 'gemini' in model:
            return {
                "max_output_tokens": model_opts.get('max_tokens', MAX_TOKENS),
                "temperature": model_opts.get('temperature', TEMPERATURE),
                **model_opts,
                **lm.kwargs
            }
        return {}

    if lm is not None:
        # get model name, default to potential nested references
        # logger.debug(f"Getting model config for {lm.__class__.__name__}")
        return check(get_model_name(lm))
    else:
        return check(MODEL)


# async def _run_local_recordings():
#     import time
#     import json
#     from comprehend.pipelines.linking import linking_pipeline
#     start = time.time()
#     # result = await linking_pipeline("4198", "SOC", [
#     #     'date of birth is 01/01/1990'
#     # ])
#     # ], rationale='the patients date of birth')
#     result = await linking_pipeline("4198", "SOC", [
#         'for GG0170. a is 6, goal is also 6. b is 4, goal is 6. c is 3 and goal '
#         'is 6, d is 2 and goal is 6, e is 1 and goal is 6, n is 2 and goal is 6'
#     ])
#     # result = await linking_pipeline("4198", "SOC",[
#     #     'for GG0170. a is 6, goal is also 6. b is 4, goal is 6'
#     # ])
#
#     print(f"--- Completed in {round(time.time() - start, 2)} seconds")
#
#     with open(f"../../transcripts/4198/linkin_.json", 'w') as f:
#         f.write(json.dumps(result, indent=2))
#
#
#     # from comprehend.pipelines.transcript import transcript_pipeline
#     #
#     # visit_id = "4225"
#     # with open("../../transcripts/4225/transcript.txt", 'r') as f:
#     #     transcript_a = f.read()
#     #
#     # a, q = await transcript_pipeline(
#     #     transcript_a,
#     #     visit_id,
#     #     visit_type='SOC',
#     #     answered=[],
#     #     run_unhandled=True,
#     #     gen_summary=True)
#     #
#     # with open(f"../../transcripts/{visit_id}/answers.json", 'w') as f:
#     #     f.write(json.dumps(a))
#     #
#     # with open(f"../../transcripts/{visit_id}/questions.json", 'w') as f:
#     #     f.write(json.dumps(q))
#     #
#     # with open(f"../transcripts/{visit_id}/runs.json", 'w') as f:
#     #     f.write(json.dumps(runs))
#
#
# if __name__ == "__main__":
#     import asyncio
#
#     asyncio.run(_run_local_recordings())
