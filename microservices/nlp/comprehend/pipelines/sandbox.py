import json
import uuid

from loguru import logger
from weaviate.classes.query import Filter

from packages.langfuse.client import StatefulTraceClient
from . import langfuse, MAX_RETRIES, IS_PRODUCTION, google_llm_model
from .common import answer_questions, ReqCtx, get_field_retriever
from ..utils import get_signature
from ..utils.monitor import setup_trace


async def sandbox_pipeline(
        ctx: ReqCtx,
        id: str,
        visit_type: str,
        field: str | dict,
        snippets: list,
        skip_qa: bool = True,
        trace: StatefulTraceClient = None,
        attempts=0):
    retries = attempts or 0
    trace = setup_trace(
        langfuse,
        trace=trace,
        id=id or str(uuid.uuid4().hex),
        name="Sandbox Pipeline",
        session_id=id,
        input={
            "type": visit_type,
            "skip_qa": skip_qa,
            "field": json.dumps(field),
            "snippets": snippets
        },
        metadata={
            "id": id,
            "ctx": json.dumps(ctx),
            "attempts": retries
        })

    try:
        if type(field) == dict:
            og_question = field
        else:
            retriever = get_field_retriever(ctx, que_type=visit_type)
            og_question = retriever.list(filters=(
                    Filter.by_property('code').equal(field)
                    & Filter.by_property('group_type').equal(visit_type)
            ), post_process=lambda docs: next((x for x in docs if x.get('code') == field), None))

        if og_question is None:
            trace.update(output={"answers": []})
            return []

        og_question.update({"snippets": snippets})
        questions = [og_question]

        trace.event(name="Questions", input={"questions": questions})

        if len(questions) == 0:
            return []

        signature, prompt_client = get_signature(reference='transcript', langfuse=langfuse)
        answers = await answer_questions(
            trace,
            questions=questions,
            signature=signature,
            prompt_client=prompt_client,
            lm=google_llm_model,
            skip_qa=skip_qa)

        trace.update(output={"answers": answers})
        return answers
    except Exception as e:
        if not IS_PRODUCTION:
            trace.update(output={"error": str(e)})
            raise e
        while retries < MAX_RETRIES:
            retries += 1
            logger.error(f"Error in pipeline: {e}")
            logger.error(f"Retrying {retries} of 3")
            return await sandbox_pipeline(
                ctx,
                id=id,
                visit_type=visit_type,
                snippets=snippets,
                field=field,
                skip_qa=skip_qa,
                trace=trace,
                attempts=retries)

        logger.error(f"Error in pipeline: {e}")
        return []
