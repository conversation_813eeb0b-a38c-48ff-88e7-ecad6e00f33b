import json
import os
import uuid
from typing import List

from loguru import logger

from comprehend.utils.monitor import setup_trace
from packages.langfuse.client import StatefulTraceClient
from . import langfuse, IS_PRODUCTION, MAX_RETRIES, google_llm_model
from .common import answer_questions, questions_retriever, ReqCtx
from ..utils import get_signature, process_json_text


async def care_plan_pipeline(
        ctx: ReqCtx,
        episode_id: str,
        goals: List[dict],
        responses: dict,
        conditions: List[dict],
        trace: StatefulTraceClient = None,
        attempts=0) -> List[dict]:
    retries = attempts or 0
    trace = setup_trace(
        langfuse,
        trace=trace,
        id=str(uuid.uuid4().hex),
        name="Care Plan Pipeline",
        session_id=episode_id,
        input={
            "goals": goals,
            "responses": responses,
            "conditions": conditions,
        },
        metadata={
            "ctx": json.dumps(ctx),
            "attempts": retries
        })

    try:
        all_questions = questions_retriever(ctx, que_type='SOC')
        # Load examples
        with open(os.path.join(os.path.dirname(__file__), "examples", "care_plan.json"), "r") as f:
            examples = json.load(f)

        # Process interventions first
        intervention_span = trace.span(name="Intervention Aggregate")
        interventions = []
        for goal in goals:
            for intervention in goal.get('interventions', []):
                qa = json.loads(intervention.get('quality_assurance', "[]") or "[]")
                if len(qa) == 0:
                    continue

                qa_item = next(filter(lambda x: x.get('type') == 'input', qa), None)
                if qa_item is None:
                    continue

                goal_title = goal.get('title')
                intervention_title = process_json_text(intervention.get('title'))

                model = qa_item.get('model', {})
                sources = model.get('sources', [])

                relevant_data = []
                for source in sources:
                    value = responses.get(source.get('code'))
                    if value is not None:
                        que = next(filter(lambda x: x["code"] == source.get('code'), all_questions), {}) or {}
                        relevant_data.append({
                            'question': que.get('question', None),
                            'criteria': que.get('criteria', None),
                            'code': source.get('code'),
                            'answer': value
                        })

                interventions.append({
                    'goal_id': goal.get('id'),
                    'intervention_id': intervention.get('id'),
                    'question_data': {
                        'code': intervention.get('id'),
                        'question': f"""Goal: {goal_title}; \nIntervention: {intervention_title};""",
                        'criteria': model.get('criteria', ''),
                        'relevant_data': relevant_data,
                        "source": [],
                        'examples': examples
                    }
                })

        intervention_span.end(output={
            "interventions": [dict(int, question_data=dict(int.get('question_data'), examples=None)) for int in
                              interventions]
        })

        # Get answers for interventions
        signature, prompt_client = get_signature(reference='care_plan', langfuse=langfuse)
        intervention_questions = [int.get('question_data') for int in interventions]

        intervention_answers = await answer_questions(
            trace,
            questions=intervention_questions,
            lm=google_llm_model,
            signature=signature,
            skip_order=True,
            ensure_context=False,
            prompt_client=prompt_client)

        included_interventions = list(filter(lambda x: x.get('answer') == 'INCLUDE', intervention_answers))
        included_intervention_ids = [que.get('code') for que in included_interventions]

        # Process steps for included interventions
        step_span = trace.span(name="Step Aggregate")
        steps = []
        for goal in goals:
            for intervention in goal.get('interventions', []):
                if intervention.get('id') not in included_intervention_ids:
                    continue

                included_intervention = next(
                    filter(lambda x: x.get('code') == intervention.get('id'), included_interventions), None)

                for step in intervention.get('steps', []):
                    qa = json.loads(step.get('quality_assurance', "[]") or "[]")
                    if len(qa) == 0:
                        continue

                    qa_item = next(filter(lambda x: x.get('type') == 'input', qa), None)
                    if qa_item is None:
                        continue

                    goal_title = goal.get('title')
                    intervention_title = process_json_text(intervention.get('title'))
                    step_title = process_json_text(step.get('title'))

                    model = qa_item.get('model', {})
                    sources = model.get('sources', [])

                    relevant_data = []
                    for source in sources:
                        value = responses.get(source.get('code'))
                        if value is not None:
                            relevant_data.append({
                                'code': source.get('code'),
                                'value': value
                            })

                    steps.append({
                        'goal_id': goal.get('id'),
                        'intervention_id': intervention.get('id'),
                        'intervention_rationale': included_intervention.get('rationale'),
                        'step_id': step.get('id'),
                        'question_data': {
                            'code': step.get('id'),
                            'question': f"""Goal: {goal_title}; \nIntervention: {intervention_title}; \nStep: {step_title};""",
                            'criteria': model.get('criteria', ''),
                            'relevant_data': relevant_data,
                            "source": [],
                            'examples': examples
                        }
                    })

        step_span.end(output={
            "steps": [dict(step, question_data=dict(step.get('question_data'), examples=None)) for step in steps]
        })

        # Get answers for steps
        step_questions = [step.get('question_data') for step in steps]

        step_answers = await answer_questions(
            trace,
            questions=step_questions,
            lm=google_llm_model,
            signature=signature,
            skip_order=True,
            ensure_context=False,
            prompt_client=prompt_client)

        included_steps = list(filter(lambda x: x.get('answer') == 'INCLUDE', step_answers))

        result = []
        for step in steps:
            ans = next(filter(lambda x: x.get('code') == step.get('step_id'), included_steps), None)
            if ans:
                result.append({
                    "goal_id": step.get('goal_id'),
                    "intervention_id": step.get('intervention_id'),
                    "intervention_rationale": step.get('intervention_rationale'),
                    "step_id": step.get('step_id'),
                    "step_rationale": ans.get('rationale')
                })

        trace.update(output={"result": result})
        return result
    except Exception as e:
        if not IS_PRODUCTION:
            trace.update(output={"error": str(e)})
            raise e
        while retries < MAX_RETRIES:
            retries += 1
            logger.error(f"Error in pipeline: {e}")
            logger.error(f"Retrying {retries} of 3")
            return await care_plan_pipeline(
                ctx,
                goals=goals,
                episode_id=episode_id,
                responses=responses,
                conditions=conditions,
                trace=trace,
                attempts=retries)

        logger.error(f"Error in pipeline: {e}")
        return []
