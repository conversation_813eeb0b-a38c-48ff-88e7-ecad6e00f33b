{"code": "correction", "examples": [{"transcript": "The patients name is <PERSON>, she lives with her 2 children in the apartment", "answer": [{"items": [{"reference": "a question about the patients name", "updates": "The patients name is <PERSON>", "snippets": ["The patients name is <PERSON>"]}, {"reference": "question on the patients gender", "updates": "The patient is referred to as she", "snippets": ["She lives with her 2 children in the apartment"]}]}]}, {"transcript": "So you reside in North Carolina, Yes. For your chest, you said pain began via a sudden onset. ", "answer": [{"items": [{"reference": "question referring to the state of residence", "updates": "The correct value for state of residence is North Carolina", "snippets": ["so you reside in North Carolina. Yes."]}, {"reference": "question on how chest pain began for the patient", "updates": "The correct value for how the chest pain began is by a sudden onset", "snippets": ["For your chest, you said pain began via a sudden onset"]}]}]}, {"transcript": "for GG0170. a is 6", "answer": {"items": [{"reference": "oasis question on GG0170A1, current performance is 6", "updates": "The correct value for GG0170A1, current performance is 6", "snippets": ["for GG0170. a is 6"]}]}}], "correction_examples": [{"question": "Based on the OASIS E item M0069; What is the patient's preferred gender?", "update": "The correct value for gender is 2", "answer": "2"}, {"question": "Based on the OASIS E item M0050; What is the patient's state of residence?", "update": "The correct value for M0050 is North Carolina", "answer": "North Carolina"}]}