import uuid
from typing import List

from google.genai import types
from loguru import logger

from comprehend.pipelines import IS_PRODUCTION, MAX_RETRIES, langfuse
from comprehend.pipelines.common import bulk_answer_questions, ModelConfig, questions_retriever, ReqCtx
from comprehend.utils import post_process_answer, get_unique_items, extract_json
from comprehend.utils.constants import GEMINI_MODEL
from comprehend.utils.graphs import get_reverse_graph, get_qa_addressable
from comprehend.utils.monitor import setup_trace
from packages.langfuse.client import StatefulTraceClient
from services.data.db import db_request

QA_STATUS = {
    "VALID": "1",
    "INVALID": "0"
}

QA_PROMPT = """You are a clinician's expert quality assurance assistant.
Your goal is to validate the rationale and answers to clinical questions by analyzing respective transcript snippets and availed related data points to determine clinical accuracy.
The answers must strictly adhere to the provided answering criteria and follow the format indicated by the questions answer format and schema.

Instructions on validation:
1. Review the question, criteria, notes, transcript snippets and related data points carefully.
2. Assess the accuracy and consistency of the provided rationale and answer based on all available information.
3. Provide a rating of "VALID" if the answer and rationale are accurate, coherent, and supported by evidence.
4. Provide a rating of "INVALID" if the answer or rationale are unclear, inconsistent, or not supported by the available context.
5. If rating is "INVALID", explain your reasoning in detail and provide alternative rationale and answer.
6. Related data points are selected to help determine if the answer being QA'd is likely accurate - for example, if a patient is in a wheelchair, we do not expect them to walk up multiple stairs.

Output Format:
Your response must be a valid YAML array with dictionaries with the following fields:
  - code: The respective question code.
  - rating: The rating of the answer and rationale, either "VALID" or "INVALID".
  - reason: A stepped deduction explaining your assessment of the rationale and answer's accuracy
  - alt_rationale: An alternative rationale if the original is invalid, or "NA" if the original is valid
  - alt_answer: An alternative answer if the original is invalid, or "NA" if the original is valid

Make sure to Enforce this YAML Schema for each object in the list:
Ensure to ALWAYS encapsulate string values in double quotes to avoid any parsing errors caused by special characters.

- code: string
  rating: enum['VALID', 'INVALID']
  reason: "string that explains your assessment"
  alt_rationale: "string with alternative rationale or NA"
  alt_answer: object if has a schema | "string or NA"
"""


async def qa_pipeline(
        ctx: ReqCtx,
        visit_id: str,
        visit_type: str = "SOC",
        live: bool = False,
        visit: dict = None,
        referral: dict = None,
        referral_responses: List[dict] = None,
        relevant_data: List[dict] = None,
        answers: List[dict] = None,
        trace: StatefulTraceClient = None,
        attempts=0):
    retries = attempts or 0

    if answers is None:
        answers = []

    trace = trace or setup_trace(
        langfuse,
        trace=trace,
        id=f"{visit_id}_{str(uuid.uuid4().hex)}",
        name="Live QA" if live else "Transcript QA",
        input={
            "visit_id": visit_id,
            "visit_type": visit_type,
            "answers": answers or []
        },
        metadata={"attempts": retries})

    span = trace.span(
        name="Run QA",
        input={
            "visit_id": visit_id,
            **({
                   "visit_type": visit_type,
                   "answers": answers or []
               } if not live else {})
        }
    )

    try:
        _answers = answers
        _referral_responses = referral_responses or []
        _visit = visit
        if live or visit is None:
            _visit = visit or db_request(
                span,
                ref="visits",
                operation="get",
                select=["id", "type", "code", "status", "episode_id"],
                id=visit_id)
            logger.debug(f"Visit :: {visit_id} :: {visit}")
            _answers = db_request(
                span,
                ref="responses",
                operation="list",
                select=["id", "snippets", "field_id", "rationale", "response", "qa"],
                query={"visit_id": visit_id})
        else:
            # Ensure we have answers
            if len(answers) == 0:
                _answers = db_request(
                    span,
                    ref="responses",
                    operation="list",
                    select=["id", "snippets", "field_id", "rationale", "response", "qa"],
                    query={"visit_id": visit_id})
            else:
                _answers = [{
                    "field_id": ans.get("code"),
                    "response": ans.get("answer", None),
                    "qa": ans.get("qa", None),
                    **(dict(ans, code=None, answer=None, qa=None))
                } for ans in answers]

        if type(_visit) != dict or (type(_answers) != list and len(_answers) == 0):
            return []

        trace.update(session_id=_visit.get("code"))
        visit_type = _visit.get("type", "SOC") if _visit is not None else visit_type
        span.event(name="Visit", input={"visit": _visit})
        if _visit is not None and _visit.get("episode_id", None) is not None:
            _referral = referral or db_request(
                span,
                ref="referral",
                operation="indexed_get",
                select=["id", "status", "episode_id"],
                data={"episode_id": _visit.get("episode_id")})

            span.event(name="Referral", input={"referral": _referral})
            if _referral is not None:
                _referral_responses = referral_responses or db_request(
                    span,
                    ref="referral_responses",
                    operation="list",
                    select=["id", "field_id", "rationale", "response", "snippets"],
                    query={"referral_id": _referral.get("id")})

                span.event(name="Referral Answers", input={"answers": _referral_responses})
                # _answers.extend(_referral_responses)

        logger.debug(f"Answers :: {visit_id} :: {len(_answers)}")

        referral_answers = []
        for ref_answer in _referral_responses:
            referral_answers.append({
                "code": ref_answer.get("code", None) or ref_answer.get("field_id"),
                "answer": ref_answer.get("answer", None) or ref_answer.get("response"),
                "rationale": ref_answer.get("rationale"),
                "snippets": ref_answer.get("snippets", []),
                "qa": ref_answer.get("qa", None)
            })

        span.event(name="Referral Answers", input={"answers": referral_answers})

        answers = []
        for _answer in _answers:
            # if already in referral_answers skip
            code = _answer.get("code", None) or _answer.get("field_id")
            in_referral = next((x for x in referral_answers if x.get("code") == code), None)
            if in_referral is not None:
                continue
            answers.append({
                "code": code,
                "answer": _answer.get("answer", None) or _answer.get("response"),
                "rationale": _answer.get("rationale"),
                "snippets": _answer.get("snippets", []),
                "qa": _answer.get("qa", None)
            })

        span.event(name="Answer Aggregate", input={"answers": answers})

        visit_type = _visit.get("type", "SOC") if _visit is not None else visit_type
        # signature, prompt_client = get_signature(reference='live_qa', langfuse=langfuse)
        base_questions = questions_retriever(ctx, que_type=visit_type)
        qa_questions = questions_retriever(ctx, que_type=visit_type, scope='QA')

        reverse = get_reverse_graph(qa_questions)
        target_items = {}
        for item in reverse:
            if item.startswith('QA_'):
                target_items[item] = reverse[item]

        span.event(name="Reverse Graph", input={"reverse": reverse})

        qa_addressable = get_qa_addressable(span, target_items, qa_questions, [*answers, *referral_answers])

        logger.info(f"Addressable questions :: {len(qa_addressable)}")

        # ignore if live and less than half
        # if is_live and (len(addressable) < (len(questions) * 0.5)):
        #     return []


        # Add answer snippets to questions
        for qa_question in qa_addressable:
            source_question = next((x for x in base_questions if x.get("code") == qa_question.get("code").replace('QA_', '')), {})
            relevant_answer = next((x for x in answers if x.get("code") == qa_question.get("code").replace('QA_', '')), None)
            if relevant_answer is None:
                continue
            qa_question.update({
                "schema": (source_question or {}).get("schema", {}),
                "rationale": relevant_answer.get("rationale", ""),
                "answer": relevant_answer.get("answer", ""),
                "snippets": get_unique_items(relevant_answer.get('snippets', [])),
            })

        ans_span = span.span(
            name="Assess Questions",
            input={"questions": qa_addressable}
        )

        qa_answers = await bulk_answer_questions(
            span=ans_span,
            model_config=ModelConfig(
                model=GEMINI_MODEL,
                config=types.GenerateContentConfig(
                    system_instruction=QA_PROMPT,
                    top_p=0.95,
                    temperature=0.05,
                    max_output_tokens=24576
                )
            ),
            chunk_size=3,
            only_rating=True,
            questions=qa_addressable,
            relevant_data=relevant_data
        )

        def filter_unchanged(new_qa):
            new_qa = new_qa or {}
            if new_qa.get('rationale', '') == 'No mention in the source material':
                return False
            base = next((y for y in answers if y.get('code') == new_qa.get('code', '').replace('QA_', '')), {})
            base_qa = base.get('qa', {}) or {}
            if str(base_qa.get('rating', '')) == str(new_qa.get('rating', '')):
                return False
            return True

        qa_answers_filtered = list(filter(lambda x: filter_unchanged(x), qa_answers))

        span.event(
            name="QA Answers",
            input={"qa_answers": qa_answers},
            output={"qa_answers": qa_answers_filtered}
        )

        qa_answers = qa_answers_filtered

        results = []
        for que in qa_addressable:
            qa_answer = next((x for x in qa_answers if x['code'] == que['code']), {})
            base_answer = next((x for x in answers if x['code'] == que['code'].replace('QA_', '')), {})

            if (type(qa_answer.get('snippets')) == list
                    and len(qa_answer.get('snippets')) == 0
                    and type(qa_answer.get('relevant_data')) == list
                    and len(qa_answer.get('relevant_data')) == 0
            ):
                # skip if no snippets or relevant data
                continue

            # Skip if in live mode and QA response hasn't changed
            if live:
                base_qa = base_answer.get('qa', {}) or {}
                new_qa = {
                    "status": qa_answer.get('rating', 'INVALID'),
                    "rating": QA_STATUS.get(qa_answer.get('rating', 'INVALID')),
                    "reason": qa_answer.get('reason'),
                    "alt_rationale": qa_answer.get('alt_rationale'),
                    "alt_answer": post_process_answer(extract_json(qa_answer.get('alt_answer')))
                }

                if (base_qa.get('status') == new_qa['status'] and
                        base_qa.get('rating') == new_qa['rating'] and
                        base_qa.get('reason') == new_qa['reason']):
                    continue

            results.append({
                **base_answer,
                "snippets": get_unique_items(base_answer.get('snippets', [])),
                "qa": {
                    "status": qa_answer.get('rating'),
                    "rating": QA_STATUS.get(qa_answer.get('rating', 'INVALID')),
                    "reason": qa_answer.get('reason'),
                    "alt_rationale": qa_answer.get('alt_rationale'),
                    "alt_answer": post_process_answer(extract_json(qa_answer.get('alt_answer')))
                }
            })

        if not live:
            processed = [item.get('code') for item in results]
            other_answers = list(filter(lambda a: a.get('code') not in processed, answers))
            span.event(name='Other Answer', input={"answers": other_answers})
            results.extend(other_answers)

        span.end(output={"results": results})
        return results
    except Exception as e:
        if not IS_PRODUCTION:
            span.update(output={"error": str(e)})
            raise e
        while retries < MAX_RETRIES:
            retries += 1
            logger.error(f"Error in pipeline: {e}")
            logger.error(f"Retrying {retries} of 3")
            return await qa_pipeline(
                ctx,
                visit_id=visit_id,
                visit_type=visit_type,
                live=live,
                referral=referral,
                referral_responses=referral_responses,
                answers=answers,
                relevant_data=relevant_data,
                trace=trace,
                attempts=retries)

        logger.error(f"Error in pipeline: {e}")
        return []
