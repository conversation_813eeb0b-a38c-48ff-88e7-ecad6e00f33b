import json
import time
import uuid
from typing import List, Dict

from google.genai import types
from loguru import logger

from config import settings
from packages.langfuse.client import StatefulTraceClient
from . import langfuse, gemini_client
from .common import get_visit_data, bulk_answer_questions, ModelConfig, questions_retriever, ReqCtx
from .. import qa_pipeline
from ..utils import delete_gemini_docs, merge_answers, get_prompt_date, get_net_prompt_with_fallback
from ..utils.constants import GEMINI_MODEL
from ..utils.monitor import setup_trace

TRANSCRIPT_PROMPT = lambda visit_date, details: f"""You are a clinician's expert documentation assistant.
Your goal is to extract & infer the correct answers to questions by analyzing a clinician’s transcript and respective related data points.
The answers must strictly adhere to the provided answering criteria and follow the format indicated by the questions schema.
The questions may be an OASIS-E1 or standard clinical.

For context, {get_prompt_date(visit_date)}
Patient Details:
{json.dumps(details, indent=1)}

Instructions on answering:
1. Identify the Question Code:
  - Locate the question code provided in the input. This code is the primary identifier for the question and ensures you are addressing the correct item.
2. Review Transcript Snippets:
  - Examine the transcript snippets (presented in their original order).
  - Focus on direct confirmations from the clinician. If such confirmation is absent, refer to the patient’s answer.
3. Reference the Answering Criteria:
  - Always refer back to the answering criteria or schema. For example, if the criteria specify that the answer should begin with "0", ensure that your answer starts with "0".
  - Confirm that any answer is directly supported by an explicit reference to the question or its code in the transcript or related data.
4. Synthesize Multiple Sources:
  - If multiple snippets mention the question code, synthesize the information to produce the most accurate and complete answer.
  - use available relevant_data to determine the most appropriate answer
  - Ensure to consider previuos values and rationles to merge in already existing but unaffected fields
  - In cases of conflicting or ambiguous information, default to returning null.

Output Format:
 Your final answer must be a valid YAML array containing dictionaries with the following keys:
  - code: The respective question code.
  - snippets: Array of strings (maximum of 20 snippets) of the transcript snippets and or dependency data used to derive the answer in the following format:
     Transcript snippets: "[(SPEAKER) (section start time)]: <snippet excerpt>",
     Dependency data:     "[(source) (code)] <dependency answer>"
     Eg: Question on the patients name
     ---
     snippets:
       - "[CLINICIAN - 44.23]: alright. so hi, sammy sosa. that's correct. right?",
       - "[PATIENT/CAREGIVER - 44.12]: yes.",
       - "[referral - FIRST_NAME]: Sammy",
       - "[referral - LAST_NAME]: Sosa"
  - rationale: A string that explains your step-by-step reasoning as to how you derived the answer from the transcript snippets and related data.
  - answer: The final and MOST important field, the answer, formatted according to the criteria and schema. Use null in respective fields if no valid answer is found.

Make sure to Enforce this YAML Schema for each object in the list:
Ensure to ALWAYS encapsulate rationale strings in double quotes to avoid any parsing errors caused by special characters.

- code: string
  snippets:
    - list of strings
  rationale: "string that is ALWAYS in double quotes and escapes special characters (like quotes and semicolons) to avoid parsing errors"
  answer: object if has a schema | "string in double quotes otherwise"
"""

TRANSCRIPT_QA_PROMPT = lambda details: f"""You are a clinician's expert quality assurance assistant.
Your goal is to validate the rationale and answers to the questions by analyzing a clinician’s transcript and respective related data points.
The answers must strictly adhere to the provided answering criteria and follow the format indicated by the questions schema.
The questions may be an OASIS-E1 or standard clinical.

Patient Details:
{json.dumps(details, indent=1)}

Instructions on answering:
1. Review the rationale and answer provided for each question.
2. Assess the accuracy of the rationale and answer based on the transcript, all available snippets and sources.
3. Provide a rating of "VALID" if the answer and rationale are accurate and coherent, and "INVALID" if they are unclear or inconsistent.
4. If the current answer and rationale is invalid, provide an alternative rationale and answer that is more accurate based on the provided snippets and sources.
5. If the answer & rationale is valid, leave the rationale and answer empty.

Output Format:
 Your final answer must be a valid YAML array containing dictionaries with the following keys:
  - code: The respective question code.
  - rating: The rating of the answer and rationale, either "VALID" or "INVALID".
  - reason: A stepped deduction on the accuracy of the rationale and answer based on context.
  - rationale: an alternative rationale based on the provided snippets and sources.
  - answer: an alternative answer (json object if schema exists) based on the provided snippets and sources.

Make sure to Enforce this YAML Schema for each object in the list:
Ensure to ALWAYS encapsulate reason and rationale strings in double quotes to avoid any parsing errors caused by special characters.

- code: string
  reason: "string"
  rating: enum['VALID', 'INVALID']
  rationale: "string that is ALWAYS in double quotes to avoid parsing errors"
  answer: object if has a schema | "string in double quotes otherwise"
"""


async def validate_answers(
        trace: StatefulTraceClient,
        transcript: List[dict],
        questions: List[dict],
        details: dict,
        # answers: List[Dict],
        attempts: int = 0) -> List[Dict]:
    retries = attempts or 0
    logger.info(f"Validating answers")

    try:
        span = trace.span(
            name="Validate Answers",
            input={
                "transcript": transcript,
                "questions": questions
            }
        )

        answers = await bulk_answer_questions(
            span=span,
            model_config=ModelConfig(
                model=GEMINI_MODEL,
                config=types.GenerateContentConfig(
                    system_instruction=TRANSCRIPT_QA_PROMPT(details),
                    top_p=0.95,
                    temperature=0.05,
                    max_output_tokens=24576
                )
            ),
            transcript=transcript,
            questions=questions,
            # prompt_client=prompt_client,
        )

        # with open("2.answers.json", "w") as f:
        #     json.dump(answers, f, indent=2)

        # with open("2.answers.json", "r") as f:
        #     answers = json.load(f)

        return answers
    except Exception as e:
        if settings.PATRIUM_ENV == 'local':
            trace.update(output={"error": str(e)})
            raise e
        while retries < settings.MAX_RETRIES:
            retries += 1
            logger.error(f"Error validating transcript answers: {e}")
            logger.error(f"Retrying {retries} of {settings.MAX_RETRIES}")
            return await validate_answers(
                trace=trace,
                transcript=transcript,
                questions=questions,
                # answers=answers,
                details=details,
                attempts=retries)
        raise e


to_ignore = ['EP_GOALS', 'EP_INTERVENTIONS']
single_process = ['VISIT_SUMMARY', 'MEDICATIONS']
# to_ignore = ['EP_MEDICATIONS', 'MEDICATIONS', 'EP_GOALS', 'EP_INTERVENTIONS']


def ignore_question(que: dict):
    return que.get('code', '') in [*to_ignore, *single_process]


async def transcript_pipeline(
        ctx: ReqCtx,
        transcript: list[dict],
        code: str,
        visit_id: str,
        visit_type: str,
        visit_date: str = None,
        override=None,
        answered=None,
        return_qa=True,
        trace=None,
        attempts=0):
    retries = attempts or 0
    start = time.time()

    if answered is None:
        answered = []

    if override is None:
        override = []

    trace = setup_trace(
        langfuse,
        trace=trace,
        id=str(uuid.uuid4().hex),
        name="Transcript Pipeline",
        input={"transcript": transcript},
        session_id=code,
        metadata={
            "ctx": json.dumps(ctx),
            "code": code,
            "visit_id": visit_id,
            "visit_date": visit_date,
            "visit_type": visit_type,
            "answered": answered,
            "override": override,
            "attempts": retries
        }
    )

    _doc_names = []
    try:
        visit_data = get_visit_data(trace, visit_id)

        relevant_data = []
        handled_answers = visit_data.get("answers", [])
        referral_responses = visit_data.get("referral_responses", [])
        for resp in referral_responses:
            relevant_data.append({
                "code": resp.get("field_id"),
                "source": "referral",
                "rationale": resp.get("rationale"),
                "answer": resp.get("response") or resp.get("response_values")
            })

        for resp in handled_answers:
            relevant_data.append({
                "code": resp.get("field_id"),
                "source": "visit",
                "rationale": resp.get("rationale"),
                "answer": resp.get("response") or resp.get("response_values")
            })

        # with open("0.answers.json", "w") as f:
        #     json.dump(relevant_data, f, indent=2)

        source_questions = questions_retriever(ctx, que_type=visit_type)
        ctx_questions = list(filter(lambda x: not ignore_question(x), source_questions))
        single_questions = list(filter(lambda x: x.get("code") in single_process, source_questions))

        logger.info(f"Processing Transcript :: {code} :: {len(ctx_questions)} : {len(single_questions)} questions")

        # prompt, _, prompt_client = get_net_prompt('TRANSCRIPT_PROMPT', langfuse)

        span = trace.span(
            name="Answer Questions",
            input={
                "transcript": transcript,
                "questions": ctx_questions,
                "singles": single_questions
            }
        )

        patient = visit_data.get("patient", {})
        patient_details = {
            "first_name": patient.get("first_name", ""),
            "middle_name": patient.get("middle_name", ""),
            "last_name": patient.get("last_name", ""),
            "dob": str(patient.get("dob", "")),
            "gender": patient.get("gender", "")
        }

        prompt_date = get_prompt_date(visit_date)
        fallback_prompt = TRANSCRIPT_PROMPT(visit_date, patient_details)
        (prompt, _, prompt_client) = get_net_prompt_with_fallback("transcript_pipeline", langfuse,
                                                                  {
                                                                      "prompt_date": prompt_date,
                                                                      "patient_details": json.dumps(patient_details, indent=1)
                                                                  },
                                                                  fallback=fallback_prompt
                                                                  )
        model_config = ModelConfig(
            model=GEMINI_MODEL,
            config=types.GenerateContentConfig(
                system_instruction=prompt,
                top_p=0.95,
                temperature=0.05,
                max_output_tokens=24576
            )
        )

        answers = await bulk_answer_questions(
            span=span,
            model_config=model_config,
            transcript=transcript,
            questions=ctx_questions,
            relevant_data=relevant_data,
            current_visit=visit_data.get("visit"),
            prompt_client=prompt_client
        )

        # if the questions include single process questions, run them separately
        if len(single_questions) > 0:
            span_single = trace.span(
                name="Single Answer Questions",
                input={"singles": single_questions}
            )

            # Handle single process questions
            single_answers = await bulk_answer_questions(
                span=span_single,
                model_config=model_config,
                chunk_size=1,
                transcript=transcript,
                questions=single_questions,
                relevant_data=relevant_data,
                current_visit=visit_data.get("visit")
            )

            answers.extend(single_answers)

        # with open("1.answers.json", "w") as f:
        #     json.dump(answers, f, indent=2)

        # with open("1.answers.json", "r") as f:
        #     answers = json.load(f)

        logger.debug(f"Stage 1 complete :: {code} :: {time.time() - start} seconds")

        qa_questions = []
        for ans in answers:
            question = next((x for x in [*ctx_questions, *single_questions] if x.get("code") == ans.get("code")), None)
            if question is None:
                continue
            question.update(ans)
            qa_questions.append(question)

        answers = await validate_answers(
            trace=trace,
            transcript=transcript,
            questions=qa_questions,
            details=patient_details
            # answers=answers
        )

        logger.debug(f"Stage 2 complete :: {code} :: {time.time() - start} seconds")

        if return_qa:
            logger.debug(f"Running QA Pipeline :: {code}")
            _prev_answers = answers
            _answers_without_qa = list(filter(lambda x: x.get("qa") is None, visit_data.get("answers")))

            _non_qa_answers = []
            for ans in _answers_without_qa:
                _non_qa_answers.append({
                    "code": ans.get("field_id"),
                    "rationale": ans.get("rationale"),
                    "answer": ans.get("response") or ans.get("response_values"),
                    "snippets": ans.get("snippets", []),
                    "qa": ans.get("qa", None)
                })

            trace.event(name="Non QA Answers", input={"answers": _non_qa_answers})

            try:
                answers = merge_answers([*_non_qa_answers, *answers])
                answers = await qa_pipeline(
                    ctx,
                    visit_id=visit_id,
                    visit_type=visit_type,
                    visit=visit_data.get("visit", None),
                    referral=visit_data.get("referral", None),
                    referral_responses=visit_data.get("referral_responses", None),
                    relevant_data=relevant_data,
                    answers=answers,
                    trace=trace,
                    attempts=retries)
            except  Exception as e:
                logger.error(f"QA Error within transcript pipeline: {e}")
                answers = []

            if len(answers) == 0:
                answers = _prev_answers

            logger.debug(f"Stage 3 complete - QA :: {code} :: {time.time() - start} seconds")

        # Filter answers without codes
        answers = list(filter(lambda x: x.get("code") is not None, answers))

        trace.update(output={"answers": answers})
        return answers
    except Exception as e:
        if settings.PATRIUM_ENV == 'local':
            trace.update(output={"error": str(e)})
            raise e
        while retries < settings.MAX_RETRIES:
            retries += 1
            logger.error(f"Error processing transcript: {e}")
            logger.error(f"Retrying {retries} of {settings.MAX_RETRIES}")
            return await transcript_pipeline(
                ctx,
                transcript=transcript,
                code=code,
                visit_id=visit_id,
                visit_type=visit_type,
                visit_date=visit_date,
                override=override,
                answered=answered,
                return_qa=return_qa,
                trace=trace,
                attempts=retries)
        trace.update(output={"error": str(e)})
        raise e
    finally:
        delete_gemini_docs(gemini_client, _doc_names)
        logger.info(f"Transcript processed :: {attempts} :: {time.time() - start} seconds")
