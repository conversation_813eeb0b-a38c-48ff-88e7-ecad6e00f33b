import json
import uuid

from loguru import logger

from packages.langfuse.client import StatefulTraceClient
from services.data.db import db_request
from . import langfuse, MAX_RETRIES, IS_PRODUCTION, google_llm_model
from .common import extract_questions, answer_questions, get_referral, questions_retriever, \
    get_field_retriever, ReqCtx
from ..utils import get_signature
from ..utils.monitor import setup_trace


async def linking_pipeline(
        ctx: ReqCtx,
        id: str,
        visit_type: str,
        visit_id: str,
        episode_id: str,
        snippets: list,
        skip_qa: bool = False,
        rationale: str = None,
        trace: StatefulTraceClient = None,
        attempts=0):
    retries = attempts or 0
    trace = setup_trace(
        langfuse,
        trace=trace,
        id=str(uuid.uuid4().hex),
        name="Linking Pipeline",
        session_id=id,
        input={
            "snippets": snippets,
            "rationale": rationale
        },
        metadata={
            "id": id,
            "ctx": json.dumps(ctx),
            "type": visit_type,
            "skip_qa": skip_qa,
            "visit_id": visit_id,
            "episode_id": episode_id,
            "attempts": retries
        })

    try:
        transcript = '. '.join(snippets).strip()
        ctx_questions = questions_retriever(ctx, que_type=visit_type)
        ctx_retriever = get_field_retriever(ctx, que_type=visit_type, alpha=0.85, top_k=10)

        questions = extract_questions(
            trace,
            documents=[transcript],
            questions=ctx_questions,
            top_n=5,
            retriever=ctx_retriever,
            identified=[{
                "reference": rationale,
                # "snippets": snippets
                "snippets": snippets + [f"(Transcript Summary) {rationale}"]
            }] if (rationale is not None and len(rationale) > 0) else None,
            rerank=True,
            lm=google_llm_model)

        if len(questions) == 0:
            return [{"id": id, "deleted": True}]

        json_questions = list(filter(lambda x: x.get("type", "") == "json", questions))

        if len(json_questions) > 0:
            json_codes = list(map(lambda x: f"""'{x.get("code")}'""", json_questions))
            prev_answers = db_request(
                span=trace.span(name="Get Previous Responses"),
                ref="responses",
                operation="list",
                select=["id", "field_id", "response"],
                query={
                    "visit_id": visit_id,
                    "field_id": {
                        "op": "IN",
                        "data": f"({', '.join(json_codes)})"
                    }
                })

            # fill in relevant previous data into json questions
            for js_que in json_questions:
                answer = next((x for x in prev_answers if x["field_id"] == js_que.get("code")), None)
                if answer is not None:
                    question = next((x for x in questions if x["code"] == js_que.get("code")), None)
                    if question is not None:
                        question["previous_value"] = answer.get("response")

        que_snippets = []
        for _ in questions:
            que_snippets.append(transcript)

        referral = get_referral(trace, episode_id)
        responses = referral.get("responses", [])

        relevant_data = []
        for resp in responses:
            relevant_data.append({
                "code": resp.get("field_id"),
                "source": "referral",
                "rationale": resp.get("rationale"),
                "answer": resp.get("response") or resp.get("response_values")
            })

        signature, prompt_client = get_signature(reference='transcript', langfuse=langfuse)
        answers = await answer_questions(
            trace,
            questions=questions,
            signature=signature,
            relevant_data=relevant_data,
            prompt_client=prompt_client,
            lm=google_llm_model,
            skip_qa=skip_qa,
            model_opts={"thinking_config": {"thinking_budget": 0}})

        if len(answers) > 0:
            send_answers = []
            valid_answers = list(filter(lambda x: (x['answer'] != 'NA' and x['answer'] != 'UK'), answers))

            if len(valid_answers) == 0:
                trace.update(output=[{"id": id, "deleted": True}])
                return [{"id": id, "deleted": True}]

            for answer in valid_answers:
                answer.update({"id": id, "deleted": False})
                send_answers.append(answer)

            trace.update(output=send_answers)
            return send_answers

        trace.update(output=[{"id": id, "deleted": True}])
        return [{"id": id, "deleted": True}]
    except Exception as e:
        if not IS_PRODUCTION:
            trace.update(output={"error": str(e)})
            raise e
        while retries < MAX_RETRIES:
            retries += 1
            logger.error(f"Error in pipeline: {e}")
            logger.error(f"Retrying {retries} of 3")
            return await linking_pipeline(
                ctx,
                id=id,
                visit_type=visit_type,
                visit_id=visit_id,
                episode_id=episode_id,
                snippets=snippets,
                skip_qa=skip_qa,
                rationale=rationale,
                trace=trace,
                attempts=retries)

        logger.error(f"Error in pipeline: {e}")
        return []
