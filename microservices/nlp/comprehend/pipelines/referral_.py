import io
import json
import time
import uuid
from typing import List, Dict

import requests
from google.genai import types
from json_repair import repair_json
from loguru import logger

from config import settings
from packages.langfuse.client import StatefulTraceClient
from services.aws.s3 import get_s3_url
from . import langfuse, gemini_client
from .common import questions_retriever, ReqCtx
from ..utils import remove_code_backticks, extract_json, get_net_prompt, upload_to_gemini, delete_gemini_docs
from ..utils.constants import GEMINI_PRO_MODEL
from ..utils.monitor import setup_trace


def validate_answers(
        trace: StatefulTraceClient,
        run_id: str,
        docs: List[types.File],
        questions: List[dict],
        answers: List[Dict],
        attempts: int = 0) -> List[Dict]:
    retries = attempts or 0

    logger.info(f"Validating referral answers :: {run_id}")

    try:
        span = trace.span(
            name="Validate Referral Answers",
            input={
                "answers": answers,
                "retries": retries
            }
        )
        prompt, _, prompt_client = get_net_prompt('referral-gemini-qa', langfuse)
        qa_gen = span.generation(
            name="Referral Generation QA",
            model=GEMINI_PRO_MODEL,
            model_parameters={
                "temperature": str(0.1),
                "top_p": str(0.95)
            },
            input={"answers": json.dumps(answers)},
            prompt=prompt_client)

        session = gemini_client.chats.create(
            model=GEMINI_PRO_MODEL,
            config=types.GenerateContentConfig(
                system_instruction=prompt,
                temperature=0.1,
                top_p=0.95
            ),
            history=[
                {
                    "role": "user",
                    "parts": [
                        types.Part.from_text(text="Referral Documents: "),
                        *[types.Part.from_uri(file_uri=doc.uri, mime_type=doc.mime_type) for doc in docs]
                    ]
                },
                {
                    "role": "user",
                    "parts": [types.Part.from_text(text=json.dumps(questions, indent=1))]
                }
            ])

        response = session.send_message(message=types.Part.from_text(text=json.dumps(answers, indent=1)))

        metadata = response.usage_metadata
        qa_gen.update(output={
            "metadata": {
                "total_token_count": metadata.total_token_count,
                "prompt_token_count": metadata.prompt_token_count,
                "cached_content_token_count": metadata.cached_content_token_count,
                "candidates_token_count": metadata.candidates_token_count,
            },
            "result": response.text
        })

        result = remove_code_backticks(response.text)
        answers = extract_json(result)

        if type(answers) == str:
            qa_gen_2 = span.generation(
                name="Referral Generation QA - Part 2",
                model=GEMINI_PRO_MODEL,
                model_parameters={
                    "temperature": str(0.1),
                    "top_p": str(0.95)
                },
                input={"result": result})

            response_2 = session.send_message(message=types.Part.from_text(
                text="Continue the JSON so as to join (append via a plus) it with the previous part of the object"))

            metadata = response.usage_metadata
            qa_gen_2.update(output={
                "metadata": {
                    "total_token_count": metadata.total_token_count,
                    "prompt_token_count": metadata.prompt_token_count,
                    "cached_content_token_count": metadata.cached_content_token_count,
                    "candidates_token_count": metadata.candidates_token_count,
                },
                "result": response_2.text
            })

            result_2 = remove_code_backticks(response_2.text)
            answers = extract_json(result + result_2)

            if type(answers) == str:
                answers = repair_json(answers, return_objects=True)

        span.end(output={"answers": answers})

        return answers
    except Exception as e:
        if settings.PATRIUM_ENV == 'local':
            trace.update(output={"error": str(e)})
            raise e
        while retries < settings.MAX_RETRIES:
            retries += 1
            logger.error(f"Error validating referral: {e}")
            logger.error(f"Retrying {retries} of {settings.MAX_RETRIES}")
            return validate_answers(
                trace=trace,
                run_id=run_id,
                docs=docs,
                questions=questions,
                answers=answers,
                attempts=retries)
        raise e


async def referral_pipeline(
        ctx: ReqCtx,
        document_refs: List[str],
        source: str,
        service_line: str,
        bucket: str = None,
        run_id=None,
        trace=None,
        attempts=0):
    retries = attempts or 0
    start = time.time()

    if run_id is None:
        run_id = str(uuid.uuid5(settings.NAMESPACE_URL, '--'.join(document_refs)))

    logger.info(f"Processing referrals docs :: {document_refs} :: {run_id}")

    trace = setup_trace(
        langfuse,
        trace=trace,
        id=str(uuid.uuid4().hex),
        name="Referral Pipeline",
        input={
            "run_id": run_id,
            "documents": document_refs,
            "bucket": bucket,
            "source": source,
            "retries": retries
        },
        metadata={
            "document": document_refs,
            "source": source,
            "attempts": retries,
            "run_id": run_id
        }
    )

    _doc_names = []
    try:
        docs: List[types.File] = []
        for ix, document_ref in enumerate(document_refs):
            name = (f"ref-doc-{run_id}-{ix}"
                    .replace("_", "-")
                    .replace(" ", "-"))
            _doc_names.append(name)
            if source == 'aws':
                r = requests.get(get_s3_url(document_ref, bucket))
                docs.append(upload_to_gemini(
                    client=gemini_client,
                    path=io.BytesIO(r.content),
                    name=name,
                    mime_type="application/pdf"
                ))
            else:
                docs.append(upload_to_gemini(
                    client=gemini_client,
                    path=document_ref,
                    name=name,
                    mime_type="application/pdf"
                ))

        ctx_questions = questions_retriever(ctx, que_type=f"REFERRAL_{service_line}")

        start = time.time()
        logger.info(f"Processing referral :: {run_id} :: {len(ctx_questions)} questions :: {len(docs)} documents")

        prompt, _, prompt_client = get_net_prompt('referral-gemini', langfuse)

        gen = trace.generation(
            name="Referral Generation",
            model=GEMINI_PRO_MODEL,
            model_parameters={
                "temperature": str(0.1),
                "top_p": str(0.95)
            },
            input={
                "docs": json.dumps(_doc_names),
                "questions": ctx_questions
            },
            prompt=prompt_client
        )

        response = gemini_client.models.generate_content(
            model=GEMINI_PRO_MODEL,
            config=types.GenerateContentConfig(
                system_instruction=prompt,
                temperature=0.1,
                top_p=0.95
            ),
            contents=[{
                "role": "user",
                "parts": [
                    *[types.Part.from_uri(file_uri=doc.uri, mime_type=doc.mime_type) for doc in docs],
                    types.Part.from_text(text=json.dumps(ctx_questions, indent=1))
                ]
            }]
        )

        metadata = response.usage_metadata
        gen.update(output={
            "metadata": {
                "total_token_count": metadata.total_token_count,
                "prompt_token_count": metadata.prompt_token_count,
                "cached_content_token_count": metadata.cached_content_token_count,
                "candidates_token_count": metadata.candidates_token_count,
            },
            "response": response.text
        })

        logger.info(f"Processing complete :: {run_id} :: {time.time() - start} seconds")
        logger.debug(f"Processing Metadata :: {run_id} :: {response.usage_metadata}")

        result = remove_code_backticks(response.text)
        answers = extract_json(result)

        trace.event(name="Base Answers", input={"answers": answers})

        if type(answers) == str:
            answers = repair_json(answers, return_objects=True)

        answers = validate_answers(trace, run_id, docs, ctx_questions, answers)

        trace.update(output={"answers": answers})
        return answers
    except Exception as e:
        if settings.PATRIUM_ENV == 'local':
            trace.update(output={"error": str(e)})
            raise e
        while retries < settings.MAX_RETRIES:
            retries += 1
            logger.error(f"Error processing referral: {e}")
            logger.error(f"Retrying {retries} of {settings.MAX_RETRIES}")
            return await referral_pipeline(
                ctx,
                document_refs,
                source=source,
                bucket=bucket,
                run_id=run_id,
                trace=trace,
                attempts=retries)
        trace.update(output={"error": str(e)})
        raise e
    finally:
        delete_gemini_docs(gemini_client, _doc_names)
        logger.info(f"Referral processed :: {attempts} :: {time.time() - start} seconds")
