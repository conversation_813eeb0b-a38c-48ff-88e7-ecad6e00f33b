import json
import os
import time
import uuid
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Literal, TypedDict

import dsp
import dspy
import yaml
from dspy import signature_to_template
from google.genai import types
from google.genai.types import PartUnion
from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType, EpisodicNode
from graphiti_core.search.search_filters import SearchFilters
from loguru import logger
from typing_extensions import NotRequired
from weaviate.classes.query import Filter
from weaviate.collections.classes.config import Configure, Property, DataType

from config import settings
from packages.langfuse.client import StatefulSpanClient, StatefulTraceClient
from services.data.db import db_request
from services.data.sync import cached_question_retriever, is_cache_valid
from . import IS_PRODUCTION, CONCURRENCY, MAX_RETRIES, langfuse, \
    weaviate_client, get_model_config, get_model_name, ignore_question, gemini_client
from ..lib.graph_db import graph
from ..lib.reranker import reranker
from ..lib.retriever import HybridSearchRetriever
from ..models.embedding import embed
from ..utils import get_signature, get_prompt_date, chunk_list, extract_json, merge_questions, embedding_text, \
    get_unique_items, remove_code_backticks, clone_object
from ..utils.constants import MODEL, BACKWARD_COMPATIBILITY_MAP
from ..utils.graphs import get_execution_order


class ReqCtx(TypedDict):
    agency_id: str
    user_id: NotRequired[str]


class ModelConfig:
    def __init__(self, model: str, config: types.GenerateContentConfig):
        self.model = model
        self.config = config


def build_prompt(
        span: StatefulSpanClient,
        signature,
        question_obj: dict,
        prev_answers=None,
        relevant_data=None,
        prompt_client=None,
        visit: dict = None,
        model_opts=None,
        lm=None):
    if prev_answers is None or type(prev_answers) != list:
        prev_answers = []

    if relevant_data is None or type(relevant_data) != list:
        relevant_data = []

    if visit is None or not isinstance(visit, dict):
        visit = {}

    if model_opts is None or not isinstance(model_opts, dict):
        model_opts = {}

    span_ = span.span(
        trace_id=span.trace_id,
        name="Build Prompt",
        input={
            "question": json.dumps(question_obj),
            "visit_id": visit.get("id"),
            "model_opts": json.dumps(model_opts),
        }
    )

    code = question_obj.get("code")
    question = question_obj.get("question")
    schema = question_obj.get("schema")
    criteria = question_obj.get("criteria", "")
    notes = question_obj.get("notes", [])
    examples = question_obj.get("examples", [])
    description = question_obj.get("description", "")
    previous_value = question_obj.get("previous_value", None)
    dependencies = question_obj.get("dependencies", [])

    transcripts = question_obj.get("snippets", [])

    # predictor = dsp_pm.Predict(signature, lm, **get_model_config(lm), **model_opts)
    predictor = dspy.Predict(signature, **get_model_config(lm, **model_opts))

    date = get_prompt_date(visit.get("visit_date"))

    is_assessment_question = question_obj.get("rationale", None) or question_obj.get("answer", None)

    demos = []
    example_builds = []
    for eg in examples:
        eg_rel_data = eg.get("relevant_data", None)

        example = {
            "question": question,
            "context": eg.get("context", None),
            "relevant_data": json.dumps(eg_rel_data) if eg_rel_data else "",
            "rationale": eg.get("rationale"),
            "answer": eg.get("answer"),
            "assessment": eg.get("assessment")
        }

        ass = example.get('assessment')
        rat = example.get('rationale')
        ans = example.get('answer')
        if isinstance(rat, str):
            rat = rat.strip()
        if isinstance(ans, str):
            ans = ans.strip()
        if isinstance(ass, str):
            ass = ass.strip()

        if ans and rat:
            if is_assessment_question and not ass:
                continue
            demo_data = {
                "question": example.get('question'),
                "context": example.get('context', []) or [],
                "relevant_data": example.get('relevant_data', '') or '',
                "rationale": rat or '',
                "answer": json.dumps(ans or ''),
                "assessment": json.dumps(example.get('assessment', '') or '')
            }
            example_builds.append(demo_data)
            demos.append(dspy.Example(**demo_data))

    prompt = signature_to_template(signature)(dsp.Example(demos=demos))
    relevant_list = [*prev_answers, *relevant_data]
    que_relevant_data = question_obj.get("relevant_data", [])

    for dep in dependencies:
        dep_data = next((x for x in relevant_list if x["code"] == dep.get("code")), None)
        if dep_data is not None:
            exists = next((x for x in que_relevant_data if x["code"] == dep.get("code")), None)

            if exists is not None:
                continue

            que_relevant_data.append({
                "code": dep.get("code"),
                "source": dep.get("source", None),
                "question": dep.get("question", None),
                "description": dep.get("description", None),
                "answer": dep_data.get("answer", None),
                "rationale": dep_data.get("rationale", None)
            })

    optionals = {
        "rationale": question_obj.get("rationale", None),
        "answer": json.dumps(question_obj.get("answer", None))
    } if is_assessment_question else {}

    span_.end(output={
        "code": code,
        "question": question,
        "prompt": prompt,
        "date": date,
        "examples": json.dumps(example_builds, indent=1),
        "optionals": json.dumps(optionals, indent=1),
        "previous_value": json.dumps(previous_value, indent=1) if previous_value is not None else None,
        "schema_def": json.dumps(schema, indent=1),
        "relevant_data": json.dumps(que_relevant_data, indent=1) if len(que_relevant_data) > 0 else None,
    })

    return {
        "lm": lm,
        "predictor": predictor,
        "demos": demos,
        "prompt": prompt,
        "question": question,
        "notes": notes,
        "code": code,
        "previous_value": json.dumps(previous_value, indent=2) if previous_value is not None else "",
        "schema_def": json.dumps(schema, indent=2),
        "date": date,
        "prompt_client": prompt_client,
        "criteria": criteria,
        "context": transcripts,
        "description": description,
        "relevant_data": json.dumps(que_relevant_data, indent=2) if len(que_relevant_data) > 0 else "",
        **optionals
    }


def execute_template(
        span: StatefulSpanClient,
        run_template: dict,
        question: dict,
        ctx: dict = None,
        skip_qa: bool = True,
        ensure_context: bool = True):
    start_a = time.time()
    code = run_template.get('code')
    qid = f"{code}-{str(uuid.uuid4().hex)}"
    logger.info(f"--- Question answering starting: {qid} : {start_a}")
    span_ = span.span(
        trace_id=span.trace_id,
        name="Execute Template",
        input={
            "code": code,
            "qid": qid,
            "ctx": ctx,
            "question": question,
            "run_template": json.dumps(dict(
                run_template,
                lm=None,
                prompt_client=None,
                predictor=None,
                demos=None
            ))
        }
    )

    if ensure_context and (
            len(run_template.get("context", []) or []) == 0 and len(run_template.get("relevant_data", []) or []) == 0):
        span_.event(name="Empty Context / Data", input={
            "question": question.get("question"),
            "code": question.get("code")
        })
        output = {
            "code": question.get("code"),
            "rationale": "No mention in the source material",
            "answer": "NA",
            "snippets": run_template.get("context"),
            "relevant_data": run_template.get("relevant_data"),
        }
        span_.end(output=output)
        return output

    predictor = run_template.get("predictor")
    prompt_client = run_template.get("prompt_client")
    context = run_template.get("context")
    lm = run_template.get("lm")

    generate = span_.generation(
        name="Generate Answer",
        model=get_model_name(lm),
        model_parameters={**get_model_config(lm, trace=True)},
        input={
            "code": code,
            "context": context,
            "date": run_template.get("date"),
            "prompt": run_template.get("prompt"),
            "question": question.get("question"),
            "previous_value": run_template.get("previous_value", []),
            "relevant_data": run_template.get("relevant_data", []),
            "answer": run_template.get("answer", None)
        },
        prompt=prompt_client
    )

    try:
        with dspy.context(lm=lm):
            res = predictor(**run_template)
        generate.event(name="Prediction", input={"prediction": res})
        if res is None:
            raise Exception("No response")
        answer = extract_json(res.get("answer", "") or res.get("assessment", ""))
    except Exception as e:
        logger.error(f"Error in prediction :: {code} :: {e}")
        try:
            logger.info(f"--- Retrying prediction :: {code} :: {e}")
            with dspy.context(lm=lm):
                res = predictor(**run_template)
            if res is None:
                raise Exception(f"2: No response :: {code}")
            generate.event(name="Prediction Retry", input={"prediction": res})
            answer = extract_json(res.get("answer", "") or res.get("assessment", ""))
        except Exception as e:
            raise e

    answer_dict = {}
    if type(answer) == dict:
        answer_dict = answer

    response = {
        **answer_dict,
        "question": question.get("question"),
        "code": question.get("code"),
        "rationale": answer_dict.get("rationale"),
        "answer": extract_json(answer_dict.get("answer"), False),
        "snippets": context
    }

    generate.end(output={
        "_type": str(type(answer)),
        "response": json.dumps(response)
    })

    if not skip_qa:
        response = run_base_qa(
            span=span,
            lm=lm,
            ctx=ctx,
            run_template=run_template,
            question=question,
            response=response
        )

    logger.info(f"--- Question answered :: {code} :: {round(time.time() - start_a, 3)} seconds")

    span_.end(output={
        "_type": str(type(answer)),
        "response": json.dumps(response)
    })

    return response


async def answer_questions(
        trace: StatefulTraceClient,
        questions: list,
        signature,
        lm,
        visit=None,
        ctx: dict = None,
        prev_answers: List[Dict] = None,
        relevant_data: List[Dict] = None,
        prompt_client=None,
        skip_qa=True,
        skip_order=False,
        model_opts=None,
        ensure_context=True,
        loop_on_n: int = 1):
    logger.info(f"--- Answering questions: {len(questions)} questions: {CONCURRENCY} at a time")

    if prev_answers is None:
        prev_answers = []

    span = trace.span(
        name="Answer Questions",
        input={
            "questions": questions,
            "visit": visit,
            # "model_opts": json.dumps(model_opts)
        }
    )

    # Gather answers
    start_ans = time.time()
    # chunks = chunk_list(questions, CONCURRENCY)

    ordered = get_execution_order(questions) if not skip_order else [questions]
    span.event(name="Ordered Questions", input={"order": ordered})

    answers = prev_answers
    for order in ordered:
        chunks = chunk_list(order, CONCURRENCY)

        build_span = span.span(
            name="Build Prompts",
            input={"chunks": len(chunks)}
        )

        exec_span = span.span(
            name="Execute Prompts",
            input={"chunks": len(chunks)}
        )

        for ix, chunk in enumerate(chunks):
            start_c = time.time()
            logger.info(f"--- Processing chunk {ix + 1} of {len(chunks)} - {len(chunk)} questions")

            # Execute chunk in parallel then gather results in the answers list
            executions = [
                (build_prompt(span=build_span,
                              signature=signature,
                              question_obj=question,
                              prompt_client=prompt_client,
                              visit=visit,
                              prev_answers=answers,
                              relevant_data=relevant_data,
                              model_opts=model_opts,
                              lm=lm), question)
                for question in chunk
            ]

            span.event(name=f"Executions {ix}", input={"executions": len(executions)})

            results = []
            if len(executions) <= loop_on_n:
                for tmpl, question in executions:
                    result = execute_template(
                        span=exec_span,
                        run_template=tmpl,
                        question=question,
                        ctx=ctx,
                        skip_qa=skip_qa,
                        ensure_context=ensure_context)
                    results.append(result)
            else:
                with ThreadPoolExecutor(max_workers=10) as executor:
                    results = executor.map(execute_template,
                                           [exec_span for _, _ in executions],
                                           [run_template for run_template, _ in executions],
                                           [question for _, question in executions],
                                           [ctx for _, _ in executions],
                                           [skip_qa for _, _ in executions],
                                           [ensure_context for _, _ in executions])

            answers.extend(results)
            logger.info(f"--- Chunk {ix + 1} of {len(chunks)} processed in {round(time.time() - start_c, 3)} seconds")

        logger.info(f"--- All {len(questions)} questions answered in {round(time.time() - start_ans, 3)} seconds")

        exec_span.end()
        build_span.end()
    span.end(output={"answers": answers})
    return answers


def run_base_qa(
        span: StatefulSpanClient,
        lm,
        run_template: dict,
        question: dict,
        response: dict,
        ctx: dict = None,
        model_opts=None,
        attempt=0):
    retries = attempt or 0
    if ctx is None:
        ctx = {}

    code = run_template.get("code")
    qa_span = span.span(
        name="Base Quality Assurance",
        input={
            "ctx": ctx,
            "code": run_template.get("code"),
            "question": question.get("question"),
            "run_template": json.dumps(dict(run_template, lm=None, prompt_client=None, predictor=None, demos=None)),
        }
    )

    if model_opts is None or not isinstance(model_opts, dict):
        model_opts = {}

    try:
        signature, prompt_client = get_signature(reference='base_qa', langfuse=langfuse)
        predictor = dspy.Predict(signature, **get_model_config(lm, **model_opts))

        context = run_template.get("context")

        with open(os.path.join(os.path.dirname(__file__), "examples", "qa.json"), "r") as f:
            examples = json.load(f)

        example_build = []
        for eg in examples:
            example_build.append((
                eg.get("question", ''),
                eg.get("code", ''),
                eg.get("context", []),
                eg.get("rationale"),
                eg.get("answer"),
                json.dumps({
                    "rating": eg.get("rating"),
                    "reason": eg.get("reason")
                })
            ))

        demos = []
        for que, code, ctx, rationale, answer, assess in example_build:
            if answer is None:
                continue
            demo = dspy.Example(
                question=que,
                code=code,
                context=ctx if ctx else [],
                rationale=rationale if rationale else "",
                answer=answer if answer else "",
                assessment=assess if assess else None
            )
            demos.append(demo)

        signature_prompt = {
            "question": run_template.get("question"),
            "date": run_template.get("date"),
            "description": run_template.get("description"),
            "relevant_data": run_template.get("relevant_data"),
            "notes": run_template.get("notes"),
            "criteria": run_template.get("criteria"),
            "context": run_template.get("context"),
            "code": run_template.get("code"),
            "rationale": response.get("rationale"),
            "answer": json.dumps(response.get("answer"), indent=2),
            "demos": demos
        }

        generate = qa_span.generation(
            name="Generate Base QA Rating",
            model=get_model_name(lm),
            model_parameters={**get_model_config(lm, trace=True)},
            input={
                "prompt": signature_to_template(signature)(dsp.Example(demos=demos)),
                "context": context,
                "relevant_data": run_template.get("relevant_data"),
                "question": question.get("question"),
                "code": question.get("code"),
                "rationale": response.get("rationale"),
                "answer": json.dumps(response.get("answer"), indent=2),
            },
            prompt=prompt_client
        )

        try:
            with dspy.context(lm=lm):
                res = predictor(**signature_prompt)

            if res is None:
                raise Exception("No response")

            assessment = extract_json(res.assessment)
            generate.event(name="Base QA Prediction",
                           input={"prediction": res},
                           output={"assessment": assessment})
        except Exception as e:
            logger.error(f"Error in Base QA prediction :: {code} :: {e}")
            # raise e
            try:
                logger.info(f"--- Retrying QA prediction :: {e}")
                with dspy.context(lm=lm):
                    _res = predictor(**signature_prompt)

                if _res is None:
                    raise Exception("2 No response")

                assessment = extract_json(_res.assessment)
                generate.event(name="Base QA Prediction Retry",
                               input={"previous": res, "prediction": _res},
                               output={"assessment": assessment})
            except Exception as e:
                raise e

        generate.end(output={"assessment": assessment})

        if type(assessment) != dict:
            qa_span.end(output={"skipped": run_template.get("code"), "assessment": assessment})
            return response

        if assessment.get("rating") == "0":
            response = {
                **response,
                # "snippets": get_unique_items(response.get('snippets', [])),
                "rationale": assessment.get("alt_rationale"),
                "answer": extract_json(assessment.get("alt_answer"))
            }

        response["rating"] = assessment.get("rating")
        response["reason"] = assessment.get("reason")

        qa_span.end(output={"assessment": assessment, "response": response})
        return response
    except Exception as e:
        logger.error(f"Error in Base QA : {e}")
        if retries < MAX_RETRIES:
            span.event(name="Base QA Prediction Retry", input={"prediction": e})
            return run_base_qa(span, lm, run_template, question, response, ctx, model_opts, retries + 1)
        raise e


def extract_questions(
        trace: StatefulTraceClient,
        documents: List[str],
        retriever: HybridSearchRetriever,
        questions: List[dict],
        lm,
        top_n: int = 2,
        rerank=False,
        identified: list = None) -> List[dict]:
    result_questions = []
    span = trace.span(
        name="Extract Questions",
        input={
            "documents": documents,
            "top_n": top_n,
            "rerank": rerank,
            "identified": identified
        }
    )

    def source_questions(transcript: str, attempts=0):
        retries = attempts or 0
        try:
            start_source = time.time()
            span_b = span.span(
                name="Source Questions",
                input={
                    "transcript": transcript,
                    "attempts": attempts
                },
                metadata={"attempts": retries}
            )

            if identified is not None and len(identified) > 0:
                answers = identified
            else:
                logger.info("--- Sourcing questions")
                signature, prompt_client = get_signature(reference='source_questions', langfuse=langfuse)
                predictor = dspy.Predict(signature, **get_model_config(lm, max_tokens=4096))

                with open(os.path.join(os.path.dirname(__file__), "examples", "correction.json"), "r") as f:
                    sample_questions = json.load(f)

                example_build = []
                for eg in sample_questions.get("examples", []):
                    example_build.append((eg.get("transcript"), json.dumps(eg.get("answer"))))

                demos = [dspy.Example(context=trx, answer=answer) for trx, answer in example_build]

                prompt = signature_to_template(signature)(dsp.Example(demos=demos))
                gen_b = span_b.generation(
                    name="Source Questions",
                    model=MODEL,
                    model_parameters={**get_model_config(lm, trace=True)},
                    input={
                        "prompt": prompt,
                        "transcript": transcript
                    },
                    prompt=prompt_client
                )

                with dspy.context(lm=lm):
                    prediction = predictor(context=transcript, demos=demos)
                # prediction = predictor(context=transcript, demos=demos, lm=lm)

                # if not IS_PRODUCTION:
                #     print("prediction ::", prediction)

                gen_b.end(output=prediction.answer)

                try:
                    answers = extract_json(prediction.answer)
                    answers = answers.get('items', [])
                except Exception as e:
                    logger.error(f"Error in prediction retrying: {e} :: {prediction.answer}")
                    # prediction = predictor(context=transcript, demos=demos)
                    with dspy.context(lm=lm):
                        prediction = predictor(context=transcript, demos=demos)
                    answers = extract_json(prediction.answer)
                    answers = answers.get('items', [])

                # if not IS_PRODUCTION:
                #     print("answers ::", answers)

            logger.info(f"--- answers :: {len(answers)}")
            random_questions = []

            def process_answers(answer):
                start_search = time.time()
                span_c = span_b.span(
                    name="Process Answers",
                    input={"answer": answer, "top_n": top_n}
                )

                query_ref = answer.get("reference") or '; '.join(answer.get("snippets", []))

                if query_ref is None or len(query_ref) == 0:
                    return []

                docs = retriever.invoke(query=query_ref)
                time_taken = round(time.time() - start_search, 3)
                logger.info(f"--- Processing {len(docs)} questions :: {time_taken} seconds")
                span_c.event(
                    name="Retrieved Questions",
                    input={"query": query_ref},
                    output={"docs": docs, "time_taken": time_taken}
                )

                ranked_questions = []
                for doc_ in docs:
                    # meta_ = doc.get("metadata", {})
                    og_question = next((x for x in questions if x["code"] == doc_["code"]), None)

                    if og_question is None:
                        continue

                    unique_snippets = set()
                    snippets = answer.get('snippets', [])
                    for snippet in snippets:
                        if isinstance(snippet, dict) and snippet.get('reference') is not None:
                            trace.event(
                                name="Random Answer in Snippet",
                                output={
                                    "answer": answer,
                                    "snippet": snippet,
                                }
                            )
                            ans = process_answers(snippet)
                            random_questions.extend(ans)
                            continue
                        if isinstance(snippet, list):
                            for snip in snippet:
                                unique_snippets.add(snip)
                        elif isinstance(snippet, str):
                            unique_snippets.add(snippet)

                    unique_snippets.add(transcript)

                    og_question.update({
                        "updates": answer.get("updates"),
                        "reference": answer.get("reference"),
                        "snippets": list(unique_snippets),
                        # "score": meta_['score'],
                        # "explain_score": meta_['explain_score']
                    })
                    ranked_questions.append(og_question)

                # Optionally rerank the questions
                if rerank and len(ranked_questions) > 0:
                    logger.info(f"--- Reranking {len(ranked_questions)} questions")
                    passages = []
                    for doc_ in ranked_questions:
                        passages.append({
                            "id": doc_["code"],
                            "text": f"Question: {doc_['question']} ; \nCode: {doc_['code']} ; \nField Name: {doc_['name']}",
                        })

                    reranked_ques = reranker(passages=passages, query=query_ref, span=span_c)
                    selected_ques = []
                    for r_doc in reranked_ques:
                        positioned_que = next(filter(lambda x: x["code"] == r_doc['id'], ranked_questions), None)
                        if positioned_que is not None:
                            selected_ques.append(positioned_que)
                    ranked_questions = selected_ques

                logger.info(f"--- {len(ranked_questions)} --> {top_n} pre questions sourced")

                span_c.end(output={
                    "questions": [f"{x['code']} - {x['question']}" for x in ranked_questions]
                })
                return ranked_questions[:top_n]

            similar_questions = []
            if len(answers) <= 2:
                for answer in answers:
                    similar_questions.extend(process_answers(answer))
            else:
                with ThreadPoolExecutor(max_workers=10) as executor:
                    results = executor.map(process_answers, [answer for answer in answers])

                    for result in results:
                        similar_questions.extend(result)

            if len(random_questions) > 0:
                trace.event(
                    name="Random Questions",
                    output={"questions": [dict(x, embedding=None) for x in random_questions]}
                )
                similar_questions.extend(random_questions)

            logger.info(f"{len(similar_questions)} sourced questions in {round(time.time() - start_source, 2)} seconds")

            span_b.end(output={
                "answers": answers,
                "questions": [f"{x['code']} - {x['question']}" for x in similar_questions]
            })
            return similar_questions
        except Exception as e:
            while retries < MAX_RETRIES:
                retries += 1
                logger.error(f"Error sourcing questions: {e}")
                logger.error(f"Retrying {retries} of 3")
                return source_questions(transcript, attempts=retries)
            raise e

    if len(documents) <= 2:
        for doc in documents:
            result_questions.extend(source_questions(doc))
    else:
        with ThreadPoolExecutor(max_workers=10) as executor:
            sourced_questions = executor.map(source_questions, [doc for doc in documents])  # rerank

            for ques in sourced_questions:
                result_questions.extend(ques)

    result_questions = merge_questions(result_questions)

    result_questions = list(filter(lambda x: not ignore_question(x), result_questions))

    span.end(output=result_questions)

    return clone_object(result_questions)


def get_que_retriever(
        visit_type: str,
        alpha: float = 0.75,
        top_k: int = 10,
        clean: bool = False,
        reset: bool = False,
        add_docs=True,
        score: bool = False) -> HybridSearchRetriever:
    _type_compat = BACKWARD_COMPATIBILITY_MAP.get(visit_type, None)
    _type = f"{visit_type}_questions" if _type_compat is None else _type_compat

    if reset:
        logger.info(f'--- Resetting collection :: {_type}')
        weaviate_client.collections.delete(name=_type)
        logger.info(f'--- Resetting complete :: {_type}')

    retriever = HybridSearchRetriever(
        client=weaviate_client,
        alpha=alpha,
        score=score or not IS_PRODUCTION,
        index_name=_type,
        reset=reset,
        attributes=["code", "name", "data"],
        k=top_k,
        exec_args={
            "target_vector": "content",
            "query_properties": [
                "question^1",
                "code^3",
                "name^2",
                "content^1"
            ]
        },
        collection_config={
            "name": _type,
            "properties": [
                Property(name="question", data_type=DataType.TEXT),
                Property(name="code", data_type=DataType.TEXT),
                Property(name="name", data_type=DataType.TEXT),
                Property(name="content", data_type=DataType.TEXT),
                Property(name="description", data_type=DataType.TEXT),
            ],
            "vectorizer_config": [
                Configure.NamedVectors.none(name="content")
            ]
        }
    )

    cache_valid = is_cache_valid(type=visit_type, scope='BASE')
    logger.info(f"Cache valid: {cache_valid}")
    if not cache_valid:
        # clean = True
        add_docs = True

    # Clean out previous data
    if clean:
        logger.info(f'--- Cleaning collection :: {_type}')
        collection = weaviate_client.collections.get(class_name=_type)
        collection.delete_objects()
        logger.info(f'--- Cleaning complete :: {_type}')

    if add_docs or retriever.load_docs():
        logger.info(f'--- Adding documents to Hybrid Search :: {_type}')
        questions = cached_question_retriever(type=visit_type, scope='BASE')

        docs = []
        embeds = []
        chunks = chunk_list(questions, 40)
        for ixi, chunk in enumerate(chunks):
            logger.info(f"Embedding question chunk :: {ixi + 1} / {len(chunks)}")
            embeds.extend(embed([embedding_text(que) for que in chunk], title="Clinician Questions"))
        logger.info(f"Embedded questions :: {len(embeds)} / {len(questions)}")

        for ix, que in enumerate(questions):
            if que.get('code', '').startswith('QA_') or que.get('source', '') == 'referral':
                continue

            docs.append({
                # Consistently generate the same UUID based on the code
                "uuid": uuid.uuid5(settings.NAMESPACE_URL, que.get('code')),
                "object": {
                    "question": que.get("question", ""),
                    "code": que.get("code", ""),
                    "name": que.get("name", ""),
                    "description": que.get("description", ""),
                    "content": embedding_text(que)
                },
                "vector": {"content": embeds[ix]}
            })
        retriever.add_documents(docs)
        logger.info('--- Completed adding documents to Hybrid Search')

    return retriever


def get_document_retriever(
        index_name: str,
        documents: List[str],
        alpha: float = 0.75,
        top_k: int = 10,
        score: bool = False,
        reset: bool = False,
) -> HybridSearchRetriever:
    index_name = f"Doc_{index_name}"
    logger.info(f'--- Adding documents to Hybrid Search :: {index_name}')
    retriever = HybridSearchRetriever(
        client=weaviate_client,
        alpha=alpha,
        score=score or not IS_PRODUCTION,
        index_name=index_name,
        reset=reset,
        attributes=["text"],
        k=top_k,
        exec_args={
            "target_vector": "content",
            "query_properties": ["text"]
        },
        collection_config={
            "name": index_name,
            "properties": [
                Property(name="text", data_type=DataType.TEXT),
            ],
            "vectorizer_config": [Configure.NamedVectors.none(name="content")]
        }
    )

    docs = []
    embeds = []
    chunks = chunk_list(documents, 20)
    for ixi, chunk in enumerate(chunks):
        logger.info(f"Embedding document chunk :: {ixi + 1} / {len(chunk)}")
        embeds.extend(embed(chunk, title="Clinician Transcript"))
    logger.info(f"Embedded documents :: {len(embeds)} / {len(documents)}")

    for ix, doc in enumerate(documents):
        docs.append({
            "uuid": str(uuid.uuid4()),
            "object": {"text": doc},
            "vector": {"content": embeds[ix]}
        })

    retriever.add_documents(docs)
    logger.info('--- Completed adding documents to Hybrid Search')

    return retriever


def append_context(span: StatefulSpanClient, retriever: HybridSearchRetriever, questions: list, top_n=10):
    start = time.time()
    logger.info(f"Finding answer contexts for {len(questions)} questions")

    span.event(
        name="Append Context",
        input={"questions": [q.get("code") for q in questions]}
    )

    processed_ques = []
    chunk_ix = 0
    chunks = chunk_list(questions, 60)
    for chunk in chunks:
        chunk_ix += 1
        logger.info(f'Processing chunk :: {chunk_ix} / {len(chunks)} :: {len(chunk)} questions')
        with ThreadPoolExecutor(max_workers=30) as executor:
            results = executor.map(
                lambda q: {
                    "question": q,
                    "snippets": reranker(
                        passages=[doc["text"] for doc in retriever.invoke(
                            query=q["question"],
                            opts={
                                # "vector": q["embedding"],
                                "limit": top_n
                            }
                        )],
                        query=q["question"],
                        key_ref=None,
                        span=span,
                        top_n=top_n,
                        query_text='questions',
                        passage_text='transcript snippet')
                }, chunk)

            for result in results:
                question = result["question"]
                # Check if it has snippets as an array before appending
                if isinstance(question.get("snippets", None), list):
                    question["snippets"] = get_unique_items(question["snippets"] + result["snippets"])
                else:
                    question["snippets"] = get_unique_items(result["snippets"])
                processed_ques.append(question)

    time_taken = round(time.time() - start, 3)
    logger.info(f"--- Context retrieved for all questions in {time_taken}")

    return processed_ques


def get_referral(trace: StatefulTraceClient, episode_id: str):
    try:
        span = trace.span(
            name="Get Referral Data",
            input={"episode_id": episode_id}
        )

        # TODO: use episode to directly query referral responses
        referral = db_request(
            span,
            ref="referral",
            operation="indexed_get",
            select=["id", "status", "episode_id"],
            data={"episode_id": episode_id})

        logger.debug(f"Referral :: {episode_id} :: {referral}")

        referral_responses = []
        if referral is not None:
            referral_responses = db_request(
                span,
                ref="referral_responses",
                operation="list",
                select=["id", "field_id", "rationale", "response", "snippets"],
                query={"referral_id": referral.get("id")})

        ref_result = {
            "referral": referral,
            "responses": referral_responses
        }

        span.end(output=ref_result)

        return ref_result
    except Exception as e:
        logger.error(f"Error getting referral :: {episode_id} :: {e}")
        return {
            "referral": None,
            "responses": []
        }


def get_visit_data(trace: StatefulTraceClient, visit_id: str):
    try:
        span = trace.span(
            name="Get Visit Data",
            input={"visit_id": visit_id}
        )

        visit = db_request(
            span,
            ref="visits",
            operation="get",
            select=["id", "type", "code", "status", "episode_id", "datetime", "patient_id"],
            id=visit_id)

        logger.debug(f"Visit :: {visit_id} :: {visit}")

        patient = db_request(
            span,
            ref="patients",
            operation="get",
            select=["id", "first_name", "middle_name", "last_name", "dob", "gender"],
            id=visit.get("patient_id"))

        logger.debug(f"Patient :: {visit_id} :: {patient}")

        answers = db_request(
            span,
            ref="responses",
            operation="list",
            select=["id", "snippets", "field_id", "rationale", "response", "response_values", "qa"],
            query={"visit_id": visit_id})

        logger.debug(f"Answers :: {visit_id} :: {len(answers)}")

        referral = db_request(
            span,
            ref="referral",
            operation="indexed_get",
            select=["id", "status", "episode_id"],
            data={"episode_id": visit.get("episode_id")})

        logger.debug(f"Referral :: {visit_id} :: {referral}")

        referral_responses = []
        if referral is not None:
            referral_responses = db_request(
                span,
                ref="referral_responses",
                operation="list",
                select=["id", "field_id", "rationale", "response", "snippets"],
                query={"referral_id": referral.get("id")})

        logger.debug(f"Referral Answers :: {visit_id} :: {len(referral_responses)}")

        visit_result = {
            "visit": visit,
            "patient": patient,
            "answers": answers or [],
            "referral": referral,
            "referral_responses": referral_responses or []
        }

        span.end(output=visit_result)
        return visit_result
    except Exception as e:
        logger.error(f"Error getting visit data :: {visit_id} :: {e}")
        return {
            "visit": None,
            "patient": None,
            "answers": [],
            "referral": None,
            "referral_responses": []
        }


def interpret_dep_rules(span: StatefulSpanClient, dependencies: List[dict], current_visit: dict) -> dict:
    """
    Interpret dependency rules and generate database requests and field mappings.
    
    Args:
        span: Tracing span for logging
        dependencies: List of dependency rules with new format
        current_visit: Current visit context (id, episode_id, datetime, etc.)
    
    Returns:
        dict: {
            "visit_queries": [...],  # DB queries to find target visits
            "response_queries": [...],  # Grouped response queries with context
            "field_mappings": {...}  # Field code to context mappings
        }
    """
    span_ = span.span(
        trace_id=span.trace_id,
        name="Interpret Dependency Rules",
        input={
            "dependencies": dependencies,
            "current_visit": current_visit
        }
    )

    visit_queries = []
    response_queries = []

    for dep in dependencies:
        level = dep.get("level")
        fields = dep.get("fields", [])
        visit_type = dep.get("visit")
        direction = dep.get("direction")
        nth_index = dep.get("nthIndex")
        filters = dep.get("filters", [])

        # Skip episode level and current visit (handled by original relevant_data list)
        if level == "episode" or visit_type == "current":
            continue

        # Build context prefix for field tagging
        context_prefix = _build_context_prefix(dep)

        # Extract field codes for this dependency
        field_codes = [field.get("code") for field in fields]

        # Build visit query based on dependency type
        if visit_type == "previous":
            query = _build_visit_query(current_visit, filters, "previous")
            visit_queries.append(query)
            response_queries.append({
                "context": context_prefix,
                "field_codes": field_codes,
                "query_type": "previous"
            })

        elif visit_type == "nth":
            query = _build_visit_query(current_visit, filters, "nth", direction=direction, nth_index=nth_index)
            visit_queries.append(query)
            response_queries.append({
                "context": context_prefix,
                "field_codes": field_codes,
                "query_type": "nth",
                "direction": direction,
                "nth_index": nth_index
            })

        elif visit_type == "timeWindow":
            query = _build_visit_query(current_visit, filters, "timeWindow")
            visit_queries.append(query)
            response_queries.append({
                "context": context_prefix,
                "field_codes": field_codes,
                "query_type": "timeWindow"
            })

    result = {
        "visit_queries": visit_queries,
        "response_queries": response_queries
    }

    span_.end(output=result)
    return result


def _build_context_prefix(dep: dict) -> str:
    """Build context prefix for field tagging based on dependency rules."""
    visit_type = dep.get("visit")
    direction = dep.get("direction")
    nth_index = dep.get("nthIndex")
    filters = dep.get("filters", [])

    # Extract filter values for prefix
    filter_parts = []
    for filter_item in filters:
        if "visit_type" in filter_item:
            filter_parts.append(filter_item["visit_type"])
        elif "visit_discipline" in filter_item:
            filter_parts.append(filter_item["visit_discipline"])
        elif "visit_date" in filter_item:
            date_filter = filter_item["visit_date"]
            number = date_filter.get("number")
            unit = date_filter.get("unit", "days")
            filter_parts.append(f"{number}{unit[:1]}")  # e.g., "2w" for 2 weeks

    filter_suffix = "_".join(filter_parts) if filter_parts else ""

    if visit_type == "previous":
        return f"p_{filter_suffix}" if filter_suffix else "p"
    elif visit_type == "nth":
        direction_prefix = direction[0] if direction else "n"  # "l" or "f"
        return f"{direction_prefix}{nth_index}_{filter_suffix}" if filter_suffix else f"{direction_prefix}{nth_index}"
    elif visit_type == "timeWindow":
        return f"last_{filter_suffix}" if filter_suffix else "last"

    return "unknown"


def _process_filters(current_visit: dict, filters: List[dict], query_type: str = None) -> tuple:
    query_filters = {"episode_id": current_visit.get("episode_id")}
    joins = []

    for filter_item in filters:
        if "visit_type" in filter_item:
            query_filters["type"] = filter_item["visit_type"]

        elif "visit_discipline" in filter_item:
            joins.extend([
                {"table": "Clinicians", "on": "Visits.clinician_id = Clinicians.id"},
                {"table": "Disciplines", "on": "Clinicians.discipline_id = Disciplines.id"}
            ])
            query_filters["Disciplines.short_form"] = filter_item["visit_discipline"]

        elif "visit_date" in filter_item:
            date_filter = filter_item["visit_date"]
            number = date_filter.get("number")
            unit = date_filter.get("unit", "days").lower()
            current_date = current_visit.get("datetime")

            # Skip date filtering if current_date is None
            if current_date is None:
                logger.warning("Current visit datetime is None, skipping date filter")
                continue

            if query_type == "timeWindow":
                if isinstance(current_date, str):
                    current_dt = datetime.fromisoformat(current_date.replace('Z', '+00:00'))
                elif isinstance(current_date, datetime):
                    current_dt = current_date
                else:
                    logger.warning(f"Invalid datetime format: {current_date}")
                    continue

                unit_singular = unit.rstrip('s')
                if unit_singular == "day":
                    delta = timedelta(days=number)
                elif unit_singular == "week":
                    delta = timedelta(weeks=number)
                elif unit_singular == "month":
                    delta = timedelta(days=number * 30)  # Approximate
                elif unit_singular == "year":
                    delta = timedelta(days=number * 365)  # Approximate
                else:
                    logger.warning(f"Unsupported time unit: {unit_singular}")
                    continue

                start_date = current_dt - delta

                query_filters["datetime"] = {
                    "op": "BETWEEN",
                    "value": [start_date.strftime('%Y-%m-%d %H:%M:%S'), current_dt.strftime('%Y-%m-%d %H:%M:%S')]
                }

    return query_filters, joins


def _build_visit_query(current_visit: dict, filters: List[dict], query_type: str, **kwargs) -> dict:
    """Build database query for visits with unified structure."""
    query_filters, joins = _process_filters(current_visit, filters, query_type)

    limit = kwargs.get("limit", 100)
    order = ""

    # Add specific datetime constraints based on query type
    if query_type == "previous":
        query_filters["datetime"] = {"op": "<", "value": current_visit.get("datetime")}
        limit = 1
        order = "ORDER BY datetime DESC"

    elif query_type == "nth":
        direction = kwargs.get("direction", "last")
        nth_index = kwargs.get("nth_index", 1)
        limit = nth_index
        order = "ORDER BY datetime ASC" if direction == "first" else "ORDER BY datetime DESC"

    elif query_type == "timeWindow":
        order = "ORDER BY datetime DESC"

    return {
        "ref": "visits",
        "operation": "list",
        "query": query_filters,
        "joins": joins if joins else None,
        "limit": limit,
        "order": order
    }


def _execute_dependency_queries(span: StatefulSpanClient, dep_result: dict) -> List[dict]:
    """Execute database queries for dependency data and apply field tagging."""
    span_ = span.span(
        trace_id=span.trace_id,
        name="Execute Dependency Queries",
        input=dep_result
    )

    visit_queries = dep_result.get("visit_queries", [])
    response_queries = dep_result.get("response_queries", [])

    # Step 1: Execute visit queries to get target visit IDs
    target_visits = []
    for query in visit_queries:
        try:
            visits = db_request(
                span_,
                ref=query["ref"],
                operation=query["operation"],
                query=query["query"],
                joins=query.get("joins"),
                order=query.get("order"),
                limit=query.get("limit", 1000)
            )

            if isinstance(visits, list):
                target_visits.extend(visits)
            elif visits:
                target_visits.append(visits)

        except Exception as e:
            logger.warning(f"Error executing visit query: {e}")
            continue

    # Step 2: Get visit IDs and execute response queries
    visit_ids = [visit.get("id") for visit in target_visits if visit.get("id")]

    if not visit_ids:
        span_.end(output=[])
        return []

    # Step 3: Execute response queries for each context
    tagged_responses = []
    for resp_query in response_queries:
        try:
            context = resp_query.get("context")
            field_codes = resp_query.get("field_codes", [])

            # Filter out None values from field_codes
            field_codes = [code for code in field_codes if code is not None]

            if not field_codes:
                logger.warning(f"No valid field codes for context {context}, skipping")
                continue

            # Get responses for all target visits
            responses = db_request(
                span_,
                ref="responses",
                operation="list",
                query={
                    "visit_id": {"op": "IN", "value": visit_ids},
                    "field_id": {"op": "IN", "value": field_codes}
                },
                select=["id", "field_id", "rationale", "response", "response_values", "visit_id"]
            )

            if not responses:
                continue

            # Apply field tagging
            for response in responses:
                field_id = response.get("field_id")
                tagged_field_id = f"{context}_{field_id}" if context else field_id

                tagged_responses.append({
                    "code": tagged_field_id,
                    "source": "visit_response",
                    "context": context,
                    "original_field_id": field_id,
                    "visit_id": response.get("visit_id"),
                    "answer": response.get("response"),
                    "rationale": response.get("rationale"),
                    "response_values": response.get("response_values")
                })

        except Exception as e:
            logger.warning(f"Error executing response query for context {resp_query.get('context')}: {e}")
            continue

    span_.end(output=tagged_responses)
    return tagged_responses


def _process_dependencies(span: StatefulSpanClient, dependencies: List[dict], relevant_list: List[dict],
                          current_visit: dict = None) -> List[dict]:
    if not dependencies:
        return []

    # Check if this is the new format (has 'level', 'fields', 'visit' keys)
    is_new_format = any(
        isinstance(dep, dict) and
        dep.get("level") and
        dep.get("fields") and
        dep.get("visit") is not None
        for dep in dependencies
    )

    if is_new_format and current_visit:
        dep_result = interpret_dep_rules(span, dependencies, current_visit)
        return _execute_dependency_queries(span, dep_result)
    else:
        legacy_responses = []

        for dep in dependencies:
            if not isinstance(dep, dict) or not dep.get("code"):
                continue

            dep_data = next((x for x in relevant_list if x.get("code") == dep.get("code")), None)
            if dep_data is not None:
                legacy_responses.append({
                    "code": dep.get("code"),
                    "source": dep.get("source", None),
                    "question": dep.get("question", None),
                    "description": dep.get("description", None),
                    "answer": dep_data.get("answer", None),
                    "rationale": dep_data.get("rationale", None)
                })

        return legacy_responses


async def extract_graph_questions(
        trace: StatefulTraceClient,
        code: str,
        questions: List[dict],
        retriever: Graphiti = graph
) -> List[dict]:
    span = trace.span(name="Graph Extract Questions", input={})

    results = []
    for que in questions[:10]:
        print('question', que.get("question"))
        snippets = await retriever.search(
            query=f"question: {que.get('question')}",
            group_ids=[code],
            num_results=10,
            search_filter=SearchFilters(invalid_at=None)
        )

        if len(snippets) == 0:
            continue

        snips = []
        for snip in snippets:
            print('--- fact ', snip.fact, snip.episodes)
            ep_content = []
            for episode in snip.episodes:
                ep = await EpisodicNode.get_by_uuid(graph.driver, episode)
                ep_content.append(ep.content)

            # episodes = await graph.get_episode_mentions(episode_uuids=snip.episodes)
            print('---', ep_content)
            snips.extend(ep_content)
            # snip.append()

        print('---')

        que["snippets"] = snips
        results.append(que)

    span.end(output={"questions": results})
    return results


async def get_document_graph(
        group_id: str,
        documents: List[dict],
        facts: List[dict]
) -> Graphiti:
    logger.debug(f'--- Adding documents to Graph Search :: {group_id}')

    await graph.build_indices_and_constraints(delete_existing=True)

    logger.debug(f'--- Adding facts to Graph Search :: {group_id}')

    for fact in facts:
        await graph.add_episode(
            name=f"fact_{group_id}",
            group_id=group_id,
            episode_body=fact,
            source_description="referral",
            reference_time=datetime.now(),
            source=EpisodeType.json
        )

    logger.debug(f'--- Adding documents to Graph Search :: {group_id}')

    for ix, doc in enumerate(documents):
        #  Adding already known factual information into the graph
        await graph.add_episode(
            group_id=group_id,
            name=f"{doc.get('speaker')}_{group_id}",
            episode_body=f"{doc.get('speaker')}: {doc.get('sentences')}",
            source_description="transcript",
            reference_time=datetime.now(),
            source=EpisodeType.message
        )

    logger.debug(f'--- Graph Search Built :: {group_id}')

    return graph


def build_question(
        span: StatefulSpanClient,
        question_obj: dict,
        prev_answers=None,
        relevant_data=None,
        current_visit=None):
    if prev_answers is None or type(prev_answers) != list:
        prev_answers = []

    if relevant_data is None or type(relevant_data) != list:
        relevant_data = []

    span_ = span.span(
        trace_id=span.trace_id,
        name="Build Prompt",
        input={
            "question": json.dumps(question_obj)
        }
    )

    code = question_obj.get("code")
    question = question_obj.get("question")
    inputType = question_obj.get("inputType", "text")
    schema = question_obj.get("schema", None) if inputType == "json" else None
    criteria = question_obj.get("criteria", "")
    notes = question_obj.get("notes", [])
    examples = question_obj.get("examples", [])
    description = question_obj.get("description", "")
    previous_value = question_obj.get("previous_value", None)
    dependencies = question_obj.get("dependencies", [])

    example_builds = []
    for eg in examples:
        eg_rel_data = eg.get("relevant_data", None)
        example_builds.append({
            # "question": question,
            "context": eg.get("context", None),
            "criteria": eg.get("criteria", None),
            "relevant_data": json.dumps(eg_rel_data) if eg_rel_data else None,
            "response": json.dumps({
                "rationale": eg.get("rationale"),
                "answer": eg.get("answer")
            })
        })

    # bridge in previous value from related data
    if not previous_value:
        prev = next((x for x in relevant_data if x.get('code') == code), None)
        logger.info(f"prev value :: {code} :: {prev}")
        if type(prev) is dict:
            previous_value = {
                'rationale': prev.get('rationale'),
                'answer': prev.get('answer')
            }

    relevant_list = [*prev_answers, *relevant_data]
    que_relevant_data = question_obj.get("relevant_data", [])

    if dependencies:
        dep_responses = _process_dependencies(span, dependencies, relevant_list, current_visit)
        que_relevant_data.extend(dep_responses)

    optionals = {
        "snippets": json.dumps(question_obj.get("snippets", [])),
        "rationale": question_obj.get("rationale", None),
        "answer": json.dumps(question_obj.get("answer", None)),
    } if question_obj.get("rationale", None) or question_obj.get("answer", None) else {}

    span_.end(output={
        "code": code,
        "question": question,
        "answer_format": inputType,
        "criteria": criteria,
        "notes": notes,
        "examples": json.dumps(example_builds, indent=1),
        "optionals": json.dumps(optionals, indent=1),
        "previous_value": json.dumps(previous_value, indent=1) if previous_value is not None else None,
        "schema_def": json.dumps(schema, indent=1),
        "relevant_data": json.dumps(que_relevant_data, indent=1) if len(que_relevant_data) > 0 else None,
    })

    return {
        "code": code,
        "question": question,
        "description": description,
        "answer_format": inputType,
        "criteria": criteria,
        "notes": notes,
        "schema": json.dumps(schema, indent=2),
        "previous_value": json.dumps(previous_value, indent=2) if previous_value is not None else "",
        "relevant_data": json.dumps(que_relevant_data, indent=2) if len(que_relevant_data) > 0 else "",
        "examples": example_builds,
        **optionals,
    }


def direct_llm_call(span: StatefulSpanClient, model_config: ModelConfig, parts: List[PartUnion], attempts=0):
    retries = attempts or 0
    try:
        gen = span.generation(
            name="Direct LLM Call",
            model=model_config.model,
            model_parameters={
                "temperature": str(model_config.config.temperature),
                "top_p": str(model_config.config.top_p)
            },
            input={
                "parts": json.dumps([p.text for p in parts])
            }
        )

        resp = gemini_client.models.generate_content(
            model=model_config.model,
            config=model_config.config,
            contents=[{
                "role": "user",
                "parts": parts
            }]
        )

        metadata = resp.usage_metadata
        gen.update(output={
            "response": resp.text,
            "metadata": {
                "total_token_count": metadata.total_token_count,
                "prompt_token_count": metadata.prompt_token_count,
                "cached_content_token_count": metadata.cached_content_token_count,
                "candidates_token_count": metadata.candidates_token_count,
            }
        })

        return resp.text
    except Exception as e:
        logger.error(f"Error in direct LLM call: {e}")
        if retries < MAX_RETRIES:
            return direct_llm_call(span, model_config, parts, retries + 1)
        raise e


async def bulk_answer_questions(
        span: StatefulSpanClient,
        model_config: ModelConfig,
        questions: List[dict],
        chunk_size: int = 4,
        only_rating: bool = False,
        transcript: List[dict] = None,
        prompt_client=None,
        relevant_data: List[dict] = None,
        current_visit: dict = None,
        attempts=0
):
    retries = attempts or 0
    if relevant_data is None:
        relevant_data = []

    if transcript is None:
        transcript = []

    logger.debug(f"--- Answering questions: {len(questions)} questions")

    def call_llm(ques=None, parts: List[PartUnion] = None, retry_count=0):
        try:
            if ques is None:
                ques = questions
            if parts is None:
                parts = []

            is_retry = len(ques) != len(questions)

            gen = span.generation(
                name=f"{'Retry ' if is_retry else ''}Answer Generation",
                model=model_config.model,
                model_parameters={
                    "temperature": str(model_config.config.temperature),
                    "top_p": str(model_config.config.top_p)
                },
                input={
                    "retry_count": retry_count,
                    "questions": json.dumps(ques) if len(ques) == 1 else "Full list",
                },
                prompt=prompt_client
            )

            transcript_ = [{
                "speaker": trx.get('speaker'),
                "words": trx.get('sentences'),
                "time": trx.get('timestamp')
            } for trx in transcript]

            resp = gemini_client.models.generate_content(
                model=model_config.model,
                config=model_config.config,
                contents=[{
                    "role": "user",
                    "parts": [
                        *([types.Part.from_text(text=json.dumps(transcript_))] if len(transcript_) > 0 else []),
                        types.Part.from_text(text=json.dumps(ques)),
                        *(parts or [])
                    ]
                }]
            )

            metadata = resp.usage_metadata
            gen.update(output={
                "response": resp.text,
                "metadata": {
                    "total_token_count": metadata.total_token_count,
                    "prompt_token_count": metadata.prompt_token_count,
                    "cached_content_token_count": metadata.cached_content_token_count,
                    "candidates_token_count": metadata.candidates_token_count,
                }
            })

            parsed_ans = yaml.safe_load(remove_code_backticks(resp.text))

            answer = []
            for ans in parsed_ans:
                if isinstance(ans, dict):
                    formatted_answer = {
                        **ans,
                        "answer": extract_json(ans.get("answer") or ans.get("alt_answer") or None)
                    }
                    formatted_answer.update(ans)
                    answer.append(formatted_answer)
                else:
                    span.event(name="Invalid Answer", input={"answer": ans})

            return answer
        except Exception as e:
            logger.error(f"Error in generation: {e}")
            while retry_count < (MAX_RETRIES + 2):
                retry_count += 1
                logger.error(f"Retrying generation {retry_count} of {(MAX_RETRIES + 2)}")
                return call_llm(
                    ques=ques,
                    parts=parts if retry_count < 2 else [*parts, {"text": f"Issue with previous response: {e}"}],
                    retry_count=retry_count
                )
            return []

    try:
        answers = []
        chunks = chunk_list(questions, chunk_size)
        chunk_ix = 0
        for chunk in chunks:
            chunk_ix += 1
            logger.debug(f"Processing Chunk :: {chunk_ix} / {len(chunks)} :: {[x.get('code') for x in chunk]}")
            _span = span.span(
                name="Process Transcript Chunk",
                input={
                    "ix": chunk_ix,
                    "chunk": chunk
                }
            )

            questions = [build_question(
                span=_span,
                question_obj=que,
                relevant_data=relevant_data,
                current_visit=current_visit
            ) for que in chunk]

            chunk_answers = call_llm(ques=questions)
            _span.event(name="Chunk Answers", output={"answers": chunk_answers})

            logger.debug(f"Processing Chunk complete :: {chunk_ix} / {len(chunks)}")

            _answers = []
            for question in questions:
                code = question.get("code")
                current_answer = next((x for x in chunk_answers if x.get("code") == code), None)

                if current_answer is None:
                    span.event(name="Missing Answer", input={"question": question})
                    res_ans = call_llm(ques=[question])
                    if len(res_ans) == 0:
                        logger.warning(f"Skipping {code}, failed to retry")
                        continue
                    current_answer = res_ans[0]

                if only_rating:
                    _answers.append({
                        "code": code,
                        **current_answer
                    })
                    continue

                ques_snips = question.get("snippets", None)
                answer_data = {
                    "code": code,
                    "snippets": (extract_json(ques_snips) if ques_snips is not None else None)
                                or current_answer.get("snippets", []),
                    "rationale": question.get("rationale", "") or current_answer.get("rationale", ""),
                    "answer": extract_json(question.get("answer") or current_answer.get("answer"))
                }

                rating = current_answer.get("rating", "")
                if current_answer and (rating == "INVALID" or rating == "0"):
                    answer_data.update({
                        "rationale": current_answer.get("rationale"),
                        "answer": extract_json(current_answer.get("answer"))
                    })

                _answers.append(answer_data)

            _span.update(output={"answers": _answers})
            answers.extend(_answers)

        span.update(output={"answers": answers})
        return answers
    except Exception as e:
        logger.error(f"Error in pipeline: {e}")
        if not IS_PRODUCTION:
            raise e
        while retries < MAX_RETRIES:
            retries += 1
            logger.error(f"Error in pipeline: {e}")
            logger.error(f"Retrying {retries} of 3")
            return await bulk_answer_questions(
                span=span,
                model_config=model_config,
                transcript=transcript,
                chunk_size=chunk_size,
                only_rating=only_rating,
                questions=questions,
                relevant_data=relevant_data,
                prompt_client=prompt_client,
                attempts=retries
            )

        logger.error(f"Error in pipeline: {e}")
        return []


def get_field_retriever(
        ctx: ReqCtx,
        que_type: str,
        alpha: float = 0.75,
        top_k: int = None,
) -> HybridSearchRetriever:
    _type_compat = BACKWARD_COMPATIBILITY_MAP.get(que_type, None)
    _type = que_type if _type_compat is None else _type_compat
    col_name = 'Field_content'
    logger.info(f"Field retriever :: {_type}")

    retriever = HybridSearchRetriever(
        client=weaviate_client,
        alpha=alpha,
        index_name=col_name,
        attributes=['question', 'code', 'name', 'group_type', 'data'],
        k=top_k,
        filters={},
        agency_id=ctx.get('agency_id', None),
        exec_args={
            'target_vector': "content",
            # 'filters': Filter.by_property('group_type').equal(_type),
            'query_properties': [
                "question^1",
                "code^3",
                "name^2",
                "description^2",
                "content^1"
            ]
        },
        collection_config={
            "name": col_name,
            'multi_tenancy_config': Configure.multi_tenancy(enabled=True, auto_tenant_creation=True),
            "properties": [
                Property(name="pat_id", data_type=DataType.TEXT),
                Property(name="question", data_type=DataType.TEXT),
                Property(name="code", data_type=DataType.TEXT),
                Property(name="name", data_type=DataType.TEXT),
                Property(name="type", data_type=DataType.TEXT),
                Property(name="source", data_type=DataType.TEXT),
                Property(name="source_id", data_type=DataType.TEXT),
                Property(name="description", data_type=DataType.TEXT),
                Property(name="group_type", data_type=DataType.TEXT),
                Property(name="content", data_type=DataType.TEXT),
                Property(name="data", data_type=DataType.BLOB)
            ],
            "vectorizer_config": [
                Configure.NamedVectors.none(name="content")
            ]
        }
    )

    return retriever


def questions_retriever(
        ctx: ReqCtx,
        que_type: str,
        scope: Literal['BASE', 'QA'] = 'BASE',
        alpha: float = 0.75,
        top_k: int = None
):
    logger.info(f"Retrieving questions for :: {ctx.get('agency_id')} :: {que_type}")
    retriever = get_field_retriever(ctx, que_type=que_type, alpha=alpha, top_k=top_k)

    items = retriever.list(
        filters=Filter.by_property('group_type').equal(que_type),
        opts={"limit": 800}
    )

    items = list(filter(lambda x: x.get('group_type', '') == que_type, items))

    if scope == 'QA':
        items = list(filter(lambda x: x.get('code', '').startswith('QA_'), items))
    else:
        items = list(filter(lambda x: not x.get('code', '').startswith('QA_'), items))

    # Filter for only unique codes
    unique_items = {}
    for item in items:
        code = item.get('code')
        if code and code not in unique_items:
            unique_items[code] = item

    return clone_object(list(unique_items.values()))
