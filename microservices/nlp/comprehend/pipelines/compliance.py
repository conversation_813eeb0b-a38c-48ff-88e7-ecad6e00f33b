import io
import json
import time
import uuid
from typing import List, Dict

import requests
from google.genai import types
from json_repair import repair_json
from loguru import logger

from config import settings
from packages.langfuse.client import StatefulTraceClient
from services.aws.s3 import get_s3_url
from . import langfuse, gemini_client
from .common import ReqCtx, direct_llm_call, ModelConfig, questions_retriever
from ..utils import remove_code_backticks, extract_json, upload_to_gemini, delete_gemini_docs
from ..utils.constants import GEMINI_MODEL
from ..utils.monitor import setup_trace

COMPLIANCE_PROMPT = """You are a clinician's expert compliance assessment assistant.
Your goal is to analyze provided compliance documents and data points to determine if they meet specific compliance criteria and requirements.
You must assess whether the given documents, evidence, and data points satisfy the compliance questions and standards provided.

Instructions on assessment:
1. Carefully review each compliance question and its specific criteria for what constitutes compliance.
2. Analyze the provided documents thoroughly to identify relevant information that addresses each compliance requirement.
3. Cross-reference the available data points with the compliance criteria to determine if requirements are met.
4. For each compliance question, provide a clear assessment of whether the criteria are satisfied based on the evidence.
5. Extract specific evidence from the documents and data points that supports your compliance determination.
6. If compliance cannot be determined due to insufficient information, clearly state what additional evidence would be needed.
7. Consider both explicit statements and implicit evidence that may demonstrate compliance.

Assessment Guidelines:
- COMPLIANT: All required criteria are clearly met with sufficient supporting evidence from the documents
- NON_COMPLIANT: Evidence clearly shows that one or more criteria are not met
- INSUFFICIENT_DATA: Cannot determine compliance due to lack of relevant information in the provided documents

Output Format:
Your response must be a valid JSON array with dictionaries containing the following fields:
  - code: The respective compliance question code
  - rationale: A detailed explanation of your assessment based on the evidence found
  - status: The compliance status, one of ["COMPLIANT", "NON_COMPLIANT", "INSUFFICIENT_DATA"]
  - evidence: List of specific excerpts or references from the documents that support your determination
  - recommendations: Suggested actions if compliance is not fully met

Ensure to ALWAYS encapsulate string values in double quotes to avoid any parsing errors caused by special characters.

JSON Schema for each object in the array:
- code: string
  status: enum['COMPLIANT', 'NON_COMPLIANT', 'INSUFFICIENT_DATA']
  rationale: "string explaining the assessment reasoning"
  evidence: ["array of specific document excerpts or references"]
  recommendations: ["array of suggested actions for achieving compliance"]
"""

COMPLIANCE_QA_PROMPT = """You are an expert compliance quality assurance assistant.
Your goal is to validate the accuracy and completeness of compliance assessments by reviewing the original compliance documents, data points, and the provided compliance determinations.
You must verify that the compliance status, rationale, and evidence are accurate and properly supported by the available documentation.

Instructions on validation:
1. Review each compliance assessment including the status, rationale, evidence, and recommendations provided.
2. Cross-check the assessment against the original compliance documents and data points to verify accuracy.
3. Ensure that the compliance status is appropriately determined based on the available evidence.
4. Validate that the rationale clearly explains the reasoning and is supported by the cited evidence.
5. Verify that all relevant evidence has been considered and properly referenced.
6. Assess whether recommendations are appropriate and actionable for the compliance status.
7. Provide a rating of "VALID" if the assessment is accurate, complete, and well-supported.
8. Provide a rating of "INVALID" if the assessment contains errors, omissions, or unsupported conclusions.

Validation Criteria:
- Evidence must directly support the compliance determination
- All relevant information from documents should be considered
- Status should align with the evidence and criteria
- Rationale should be clear, logical, and comprehensive
- Recommendations should be practical and relevant to achieving compliance

Output Format:
Your response must be a valid JSON array with dictionaries containing the following fields:
  - code: The respective compliance question code
  - rating: The validation rating, either "VALID" or "INVALID"
  - reason: A detailed explanation of your validation assessment
  - alt_status: Alternative compliance status if the original is invalid, or "NA" if valid
  - alt_rationale: Alternative rationale if the original is invalid, or "NA" if valid
  - alt_evidence: Alternative evidence if the original is invalid, or "NA" if valid
  - alt_recommendations: Alternative recommendations if the original is invalid, or "NA" if valid

Make sure to enforce this JSON Schema for each object in the list:
Ensure to ALWAYS encapsulate string values in double quotes to avoid any parsing errors caused by special characters.

- code: string
  rating: enum['VALID', 'INVALID']
  reason: "string explaining your validation assessment"
  alt_status: "string or NA"
  alt_rationale: "string or NA"
  alt_evidence: ["array or NA"]
  alt_recommendations: ["array or NA"]
"""


def validate_answers(
        trace: StatefulTraceClient,
        run_id: str,
        docs: List[types.File],
        data: List[Dict],
        questions: List[dict],
        answers: List[Dict],
        attempts: int = 0) -> List[Dict]:
    retries = attempts or 0

    logger.info(f"Validating compliance answers :: {run_id}")

    try:
        span = trace.span(
            name="Validate Compliance Answers",
            input={
                "answers": answers,
                "retries": retries
            }
        )

        config = ModelConfig(
            model=GEMINI_MODEL,
            config=types.GenerateContentConfig(
                system_instruction=COMPLIANCE_QA_PROMPT,
                temperature=0.1,
                top_p=0.95
            )
        )

        response = direct_llm_call(span, config, parts=[
            types.Part.from_text(text=f"Compliance Documents: {'' if len(docs) > 0 else 'No documents provided'}"),
            *[types.Part.from_uri(file_uri=doc.uri, mime_type=doc.mime_type) for doc in docs],
            types.Part.from_text(text=f"Data Points: \n{json.dumps(data, indent=1)}"),
            types.Part.from_text(text=f"Questions: \n{json.dumps(questions, indent=1)}"),

            types.Part.from_text(text=f"Compliance answers to validate: \n{json.dumps(answers, indent=1)}"),
        ])

        result = remove_code_backticks(response)
        validated = extract_json(result)

        # if an answer is VALID, return the original, if INVALID, rebuild with the alt_ fields
        for ans in answers:
            code = ans.get("code")
            validation = next((x for x in validated if x.get("code") == code), None)
            if validation is None:
                continue
            if validation.get("rating") == "VALID":
                continue
            ans.update({
                "status": validation.get("alt_status"),
                "rationale": validation.get("alt_rationale"),
                "evidence": validation.get("alt_evidence"),
                "recommendations": validation.get("alt_recommendations")
            })

        span.end(output={"answers": answers})

        return answers
    except Exception as e:
        if settings.PATRIUM_ENV == 'local':
            trace.update(output={"error": str(e)})
            raise e
        while retries < settings.MAX_RETRIES:
            retries += 1
            logger.error(f"Error validating compliance: {e}")
            logger.error(f"Retrying {retries} of {settings.MAX_RETRIES}")
            return validate_answers(
                trace=trace,
                run_id=run_id,
                docs=docs,
                data=data,
                questions=questions,
                answers=answers,
                attempts=retries)
        raise e


async def compliance_pipeline(
        ctx: ReqCtx,
        data: List[Dict],
        document_refs: List[str],
        ques: List[str],
        source: str,
        service_line: str,
        bucket: str = None,
        run_id=None,
        trace=None,
        attempts=0):
    retries = attempts or 0
    start = time.time()

    if run_id is None:
        run_id = str(uuid.uuid5(settings.NAMESPACE_URL, '--'.join(document_refs)))

    logger.info(f"Processing compliances docs :: {document_refs} :: {run_id}")

    trace = setup_trace(
        langfuse,
        trace=trace,
        id=str(uuid.uuid4().hex),
        name="Compliance Pipeline",
        input={
            "run_id": run_id,
            "documents": document_refs,
            "bucket": bucket,
            "source": source,
            "questions": ques,
            "retries": retries
        },
        metadata={
            "document": document_refs,
            "source": source,
            "attempts": retries,
            "run_id": run_id
        }
    )

    _doc_names = []
    try:
        docs: List[types.File] = []
        for ix, document_ref in enumerate(document_refs):
            name = (f"ref-doc-{run_id}-{ix}"
                    .replace("_", "-")
                    .replace(" ", "-"))
            _doc_names.append(name)
            if source == 'aws':
                r = requests.get(get_s3_url(document_ref, bucket))
                docs.append(upload_to_gemini(
                    client=gemini_client,
                    path=io.BytesIO(r.content),
                    name=name,
                    mime_type="application/pdf"
                ))
            else:
                docs.append(upload_to_gemini(
                    client=gemini_client,
                    path=document_ref,
                    name=name,
                    mime_type="application/pdf"
                ))

        start = time.time()
        questions = questions_retriever(ctx, que_type=f"COMPLIANCE_{service_line}")
        questions = list(filter(lambda x: x.get("code") in ques, questions))

        logger.info(
            f"Processing compliance :: {run_id} :: {len(docs)} documents :: {len(questions)} questions :: {len(data)} data points")

        # prompt, _, prompt_client = get_net_prompt('compliance-gemini', langfuse)

        span = trace.span(
            name="Generate Compliance Answers",
            input={
                "docs": json.dumps(_doc_names),
                "data": data,
                "questions": questions
            }
        )

        config = ModelConfig(
            model=GEMINI_MODEL,
            config=types.GenerateContentConfig(
                system_instruction=COMPLIANCE_PROMPT,
                temperature=0.1,
                top_p=0.95
            )
        )

        response = direct_llm_call(span, config, parts=[
            types.Part.from_text(text=f"Compliance Documents: {'' if len(docs) > 0 else 'No documents provided'}"),
            *[types.Part.from_uri(file_uri=doc.uri, mime_type=doc.mime_type) for doc in docs],
            types.Part.from_text(text=f"Data Points: \n{json.dumps(data, indent=1)}"),
            types.Part.from_text(text=f"Questions: \n{json.dumps(questions, indent=1)}")
        ])

        logger.info(f"Processing complete :: {run_id} :: {time.time() - start} seconds")

        result = remove_code_backticks(response)
        answers = extract_json(result)

        trace.event(name="Base Answers", input={"answers": answers})

        if type(answers) == str:
            answers = repair_json(answers, return_objects=True)

        answers = validate_answers(
            trace,
            run_id=run_id,
            docs=docs,
            data=data,
            questions=questions,
            answers=answers)

        trace.update(output={"answers": answers})
        return answers
    except Exception as e:
        if settings.PATRIUM_ENV == 'local':
            trace.update(output={"error": str(e)})
            raise e
        while retries < settings.MAX_RETRIES:
            retries += 1
            logger.error(f"Error processing compliance: {e}")
            logger.error(f"Retrying {retries} of {settings.MAX_RETRIES}")
            return await compliance_pipeline(
                ctx,
                document_refs=document_refs,
                data=data,
                ques=ques,
                source=source,
                bucket=bucket,
                run_id=run_id,
                trace=trace,
                attempts=retries)
        trace.update(output={"error": str(e)})
        raise e
    finally:
        delete_gemini_docs(gemini_client, _doc_names)
        logger.info(f"Compliance processed :: {attempts} :: {time.time() - start} seconds")
