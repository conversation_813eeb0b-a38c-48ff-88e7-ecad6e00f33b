import json
import uuid

from dictdiffer import diff
from google.genai import types
from loguru import logger

from packages.langfuse.client import StatefulTraceClient
from . import langfuse, MAX_RETRIES, IS_PRODUCTION
from .common import ReqCtx, ModelConfig, direct_llm_call
from ..utils import remove_code_backticks, extract_json
from ..utils.constants import GEMINI_PRO_MODEL, GEMINI_MODEL
from ..utils.monitor import setup_trace

GENERATE_PROMPT = """You are an expert at interpreting requests from users and making appropriate changes to an object.
The object contains details about an input field that a language model is going to use as instructions to answer a question.
The field is also used to present the answer to the user in the expected format, so it is important to observe the structure of the field object.
Your goal is to take in user requests and make appropriate changes to the field object given.

Instructions on answering:
1. Review the users request carefully and determine what needs to be changed in the field object.
2. Take into account any additional context provided.
3. If the input type is json, ensure to make appropriate changes to the schema as well as respective examples,  
4. Make appropriate changes to the field object to reflect the user's request.

Output Format:
 Your final answer must be a valid JSON object with the fields defined in the schema
"""


async def generate_pipeline(
        ctx: ReqCtx,
        id: str,
        message: str,
        data: dict,
        context: dict | None = None,
        trace: StatefulTraceClient = None,
        attempts=0):
    retries = attempts or 0
    trace = setup_trace(
        langfuse,
        trace=trace,
        id=id or str(uuid.uuid4().hex),
        name="Generate Pipeline",
        session_id=id,
        input={
            "message": message,
            "data": data,
            "context": context
        },
        metadata={
            "id": id,
            "ctx": json.dumps(ctx)
        })

    logger.info(f"Generating answer for :: {message}")
    try:
        span = trace.span(
            name="Generate Answer",
            input={
                "message": message,
                "data": data,
                "context": context
            }
        )

        with open('field-schema.json', 'r') as f:
            field_schema = json.load(f)

        config = ModelConfig(
            model=GEMINI_MODEL,
            # model=GEMINI_PRO_MODEL,
            config=types.GenerateContentConfig(
                system_instruction=GENERATE_PROMPT,
                top_p=0.95,
                temperature=0.05,
                max_output_tokens=24576,
                # response_schema=field_schema,
                response_mime_type="application/json",
                thinking_config=types.ThinkingConfig(thinking_budget=0)
            )
        )

        raw_answer = direct_llm_call(span, config, [
            types.Part.from_text(text=f"Field Schema: {json.dumps(field_schema, indent=1)}"),
            types.Part.from_text(text=f"""User Request: {message}

Context Data: {json.dumps(context, indent=1)}

Field Object: {json.dumps(data, indent=1)}""")
        ])

        answer = extract_json(remove_code_backticks(raw_answer))
        # answer = yaml.safe_load(remove_code_backticks(raw_answer))

        delta = list(diff(data, answer))

        trace.update(output={
            "delta": json.dumps(delta),
            "result": answer
        })

        return {"delta": delta, "result": answer}
    except Exception as e:
        if not IS_PRODUCTION:
            trace.update(output={"error": str(e)})
            raise e
        while retries < MAX_RETRIES:
            retries += 1
            logger.error(f"Error in generate pipeline: {e}")
            logger.error(f"Retrying {retries} of 3")
            return await generate_pipeline(
                ctx,
                id=id,
                message=message,
                data=data,
                context=context,
                trace=trace,
                attempts=retries)

        logger.error(f"Error in generate pipeline: {e}")
        return []
