import json
import time
from datetime import datetime

from loguru import logger

from services.aws.s3 import save_to_s3
from services.deepgram.transcribe import transcribe
from services.models import ProcessRecording, ProcessReferral, MedicationTask, LinkTask, QATask, ProcessCarePlan, \
    ProcessSandbox, ProcessGenerate, ProcessCompliance, ProcessCorrectionRecording
from utils import validate_field
from .pipelines.care_plan import care_plan_pipeline
from .pipelines.compliance import compliance_pipeline
from .pipelines.generate import generate_pipeline
from .pipelines.linking import linking_pipeline
from .pipelines.medication import medication_pipeline
from .pipelines.qa import qa_pipeline
from .pipelines.referral_ import referral_pipeline
from .pipelines.sandbox import sandbox_pipeline
from .pipelines.transcript_ import transcript_pipeline
from .pipelines.corrections import corrections_pipeline


def process_medication(body: MedicationTask):
    validate_field('urls', body)
    validate_field('bucket', body)

    logger.info(f"[ MEDICATION {body.urls} ] ---- {datetime.now()} ----")
    start = time.time()

    result = medication_pipeline(image_urls=body.urls, bucket=body.bucket)

    end = time.time()
    logger.info(f"[ MEDICATION COMPLETE {body.urls} ] ---- {round(end - start, 2)} secs ----")
    return result


async def process_referral(body: ProcessReferral):
    validate_field('id', body)
    validate_field('documents', body)

    logger.info(f"[ REFERRAL ] -------- {datetime.now()} ---------")
    start = time.time()

    answers = await referral_pipeline(
        ctx=body.ctx,
        run_id=body.id,
        document_refs=body.documents,
        service_line=body.service_line,
        source=body.source,
        bucket=body.bucket)

    end = time.time()
    referral_path = f"referrals/{body.id}.json"
    save_to_s3(referral_path, json.dumps({"answers": answers}))
    logger.info(f"[ REFERRAL COMPLETE ] -------- {round(end - start, 2)} secs ---------")

    return answers


async def process_transcript(body: ProcessRecording):
    validate_field('visit_id', body)
    validate_field('agency_id', body)
    validate_field('visit_type', body)
    validate_field('episode_id', body)
    validate_field('patient_id', body)
    validate_field('recording', body)
    # validate_field('mimetype', body, required=False)

    logger.info(f"[ PROCESSING RECORDING ] ---- {datetime.now()} ----")
    logger.info(f"---- {json.dumps(dict(body))} ----")
    start = time.time()

    base_path = f'{body.agency_id}/{body.patient_id}/{body.episode_id}/{body.visit_id}'
    transcript, words = await transcribe(recording_url=body.recording)

    transcript_path = f'{base_path}/transcript.json'
    save_to_s3(transcript_path, json.dumps({"transcript": transcript, 'words': words}))
    logger.debug(f'---- Saved transcript to :: {transcript_path} ----')

    answers = []
    if len(transcript) > 0:
        answers = await transcript_pipeline(
            ctx=body.ctx,
            transcript=transcript,
            code=body.code,
            visit_id=body.visit_id,
            visit_type=body.visit_type,
            answered=body.answered,
            override=body.override)

    end = time.time()
    logger.info(f"[ PROCESSING COMPLETE ] ---- {round(end - start, 2)} secs ----")

    return answers, {"transcriptPath": transcript_path}


async def process_link(body: LinkTask):
    validate_field('id', body)
    # validate_field('code', body)
    validate_field('visit_id', body)
    validate_field('visit_type', body)
    validate_field('snippets', body)

    logger.info(f"[ LINKING ] -------- {datetime.now()} ---------")
    start = time.time()

    result = await linking_pipeline(
        ctx=body.ctx,
        id=body.code or body.id,
        visit_id=body.visit_id,
        episode_id=body.episode_id,
        visit_type=body.visit_type,
        skip_qa=True,
        snippets=body.snippets,
        rationale=body.rationale)

    end = time.time()
    logger.info(f"[ LINKING COMPLETE ] -------- {round(end - start, 2)} secs ---------")

    return result


async def process_qa(body: QATask):
    validate_field('visit_id', body)

    logger.info(f"[ QA ] -------- {datetime.now()} ---------")
    start = time.time()

    result = await qa_pipeline(
        ctx=body.ctx,
        visit_id=body.visit_id,
        visit_type=body.visit_type,
        live=body.live,
        answers=body.answers)

    end = time.time()
    logger.info(f"[ QA COMPLETE ] -------- {round(end - start, 2)} secs ---------")

    return result


async def process_care_plan(body: ProcessCarePlan):
    validate_field('ctx', body)
    validate_field('goals', body)
    validate_field('episode_id', body)
    validate_field('responses', body)

    logger.info(f"[ CARE PLAN ] -------- {datetime.now()} ---------")
    start = time.time()

    result = await care_plan_pipeline(
        ctx=body.ctx,
        goals=body.goals,
        episode_id=body.episode_id,
        responses=body.responses,
        conditions=body.conditions)

    end = time.time()
    logger.info(f"[ CARE PLAN COMPLETE ] -------- {round(end - start, 2)} secs ---------")

    return result


async def process_sandbox(body: ProcessSandbox):
    start = time.time()
    logger.info(f"[ SANDBOX ] -------- {datetime.now()} ---------")

    result = await sandbox_pipeline(
        ctx=body.ctx,
        id=body.id,
        field=body.field,
        snippets=body.snippets,
        visit_type=body.visit_type)

    end = time.time()
    logger.info(f"[ SANDBOX COMPLETE ] -------- {round(end - start, 2)} secs ---------")
    return result


async def process_generate(body: ProcessGenerate):
    start = time.time()
    logger.info(f"[ GENERATE ] -------- {datetime.now()} ---------")

    result = await generate_pipeline(
        ctx=body.ctx,
        id=body.id,
        message=body.message,
        data=body.data,
        context=body.context
    )

    end = time.time()
    logger.info(f"[ GENERATE COMPLETE ] -------- {round(end - start, 2)} secs ---------")
    return result


async def process_compliance(body: ProcessCompliance):
    validate_field('id', body)

    logger.info(f"[ COMPLIANCE ] -------- {datetime.now()} ---------")
    start = time.time()

    answers = await compliance_pipeline(
        ctx=body.ctx,
        run_id=body.id,
        document_refs=body.documents,
        data=body.data,
        service_line=body.service_line,
        ques=body.questions,
        source=body.source,
        bucket=body.bucket)

    end = time.time()
    logger.info(f"[ COMPLIANCE COMPLETE ] -------- {round(end - start, 2)} secs ---------")

    return answers


async def process_correction(body: ProcessCorrectionRecording):
    validate_field('visit_id', body)
    validate_field('agency_id', body)
    validate_field('visit_type', body)
    validate_field('episode_id', body)
    validate_field('patient_id', body)
    validate_field('correction_id', body)
    validate_field('recording', body)

    logger.info(f"[ PROCESSING CORRECTION RECORDING ] ---- {datetime.now()} ----")
    logger.info(f"---- {json.dumps(dict(body))} ----")
    start = time.time()

    base_path = f'{body.agency_id}/{body.patient_id}/{body.episode_id}/{body.visit_id}'
    transcript, words = await transcribe(recording_url=body.recording)

    transcript_path = f'{base_path}/correction_{body.correction_id}_transcript.json'
    save_to_s3(transcript_path, json.dumps({"transcript": transcript, 'words': words}))
    logger.debug(f'---- Saved correction transcript to :: {transcript_path} ----')

    answers = []
    if len(transcript) > 0:
        answers = await corrections_pipeline(
            ctx=body.ctx,
            transcript=transcript,
            code=body.code,
            visit_id=body.visit_id,
            visit_type=body.visit_type,
            correction_id=body.correction_id)

    end = time.time()
    logger.info(f"[ PROCESSING COMPLETE ] ---- {round(end - start, 2)} secs ----")

    return answers, {"transcriptPath": transcript_path, "correctionId": body.correction_id}

