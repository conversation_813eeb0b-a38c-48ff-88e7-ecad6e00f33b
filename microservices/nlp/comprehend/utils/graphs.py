from collections import defaultdict
from itertools import chain
from typing import List

from packages.langfuse.client import StatefulSpanClient


def get_complete_sections(questions: List[dict], answers: List[dict]):
    # Get all sections
    code_sects = {}
    sects = list(set(list(chain.from_iterable([question.get('sections', []) for question in questions]))))
    for sect in sects:
        sect_questions = [question for question in questions if sect in question.get('sections', [])]
        if len(sect_questions) == 0:
            continue
        code_sects[sect] = [q.get('code') for q in sect_questions]

    # Get complete sections
    complete = []
    answer_codes = set(answer.get('code') for answer in answers)
    for sect in code_sects.items():
        sect_id = sect[0]
        sect_codes = sect[1]
        answered = []
        for sect_code in sect_codes:
            if sect_code.replace('QA_', '') in answer_codes:
                answered.append(sect_code)
        if len(answered) == len(sect_codes):
            complete.append(sect_id)

    return code_sects, complete


def get_qa_addressable(snap: StatefulSpanClient, graphs: dict, questions: List[dict], answers: List[dict]):
    answer_codes = set(answer.get('code') for answer in answers)

    span = snap.span(
        name="Get QA Addressable",
        input={
            "graphs": graphs,
            "answers": answers,
            "questions": questions
        }
    )

    sects, complete = get_complete_sections(questions, answers)
    span.event(
        name="Complete Sections",
        input={"sections": sects, "complete": complete}
    )

    # Find sub graphs from answers with all answer codes available
    sub_graphs = {}
    for answer_code in answer_codes:
        qa_code = f'QA_{answer_code}'

        # Skip if the answer already has a qa value
        answer = next((a for a in answers if a.get("code") == answer_code), None)
        if answer is not None:
            if answer.get('qa', None) is not None:
                qa_val = answer.get('qa', None)
                if qa_val == 'INVALID':
                    continue

        graph = graphs.get(qa_code)
        if graph is not None:
            sub_graphs[qa_code] = graph

    span.event(
        name="Sub Graphs",
        input={"sub_graphs": sub_graphs}
    )

    # Loop through the sub graphs and get the answers of the nodes
    graph_questions = []
    for sub_graph in sub_graphs.items():
        sub_node = None
        sub_code = sub_graph[0]
        sub_deps = sub_graph[1]

        sub_answers = []
        for node in sub_deps:
            answer = next((a for a in answers if a.get("code") == node), None)
            if answer is not None:
                question = next((q for q in questions if q.get("code") == node), None)
                if question is not None:
                    answer.update({"source": question.get("source")})
                sub_answers.append(answer)

        if len(sub_deps) > 0 and len(sub_answers) == 0:
            continue

        sub_question = next((q for q in questions if q.get("code") == sub_code), None)
        if sub_question is not None:
            sub_node = sub_question
            sub_node.update({
                "relevant_data": sub_answers
            })

        if sub_node is None:
            continue

        graph_questions.append(sub_node)

    span.end(output={"graph_questions": graph_questions})

    return graph_questions


def get_dep_graph(questions: List[dict], clean_ref: bool = True):
    dep_graphs = {}
    # Build the dependency graph
    for question in questions:
        q_code = question.get("code")
        q_source = question.get("source")
        q_ref = f"{q_code}~{q_source}"

        dep_graphs[q_ref] = []

        for dep in question.get("dependencies", []):
            code = dep.get("code")
            source = dep.get("source")
            ref = f"{code}~{source}"

            if ref not in dep_graphs:
                dep_graphs[ref] = []

            if q_code not in dep_graphs[ref]:
                if q_ref not in dep_graphs[ref]:
                    dep_graphs[ref].append(q_ref)

    clean_graph = {}
    if clean_ref:
        # remove the tilde and take the first part from the items
        for graph in dep_graphs.items():
            clean_graph[graph[0].split('~')[0]] = [x.split('~')[0] for x in graph[1]]
    else:
        clean_graph = dep_graphs

    return clean_graph


def reverse_graph(graph):
    reversed_graph = {}

    def add_parent(child, parent):
        if child not in reversed_graph:
            reversed_graph[child] = []
        if parent not in reversed_graph[child]:
            reversed_graph[child].append(parent)

    # Iterate through the graph and reverse it
    for parent, children in graph.items():
        if parent not in reversed_graph:
            reversed_graph[parent] = []

        for child in children:
            add_parent(child, parent)

            # Propagate the parent up the chain
            current = parent
            if current in reversed_graph:
                for grandparent in reversed_graph[current]:
                    if grandparent != current:
                        add_parent(child, grandparent)

    return reversed_graph


def get_graph_exec_order(questions, dep_graph):
    # Calculate in-degrees for each node
    in_degree = defaultdict(int)
    for node in dep_graph:
        for child in dep_graph[node]:
            in_degree[child] += 1

    # Initialize queue with nodes that have no dependencies
    queue = [node for node in dep_graph]
    # queue = [node for node in dep_graph if in_degree[node] == 0]

    result = []
    processed = set()
    while queue:
        # Process all nodes in the current level
        level = []
        next_queue = []  # Prepare the next level
        for node in queue:
            if node not in processed:
                level.append(node)
                processed.add(node)

                # Reduce in-degree of children and add to next queue if ready
                for child in dep_graph[node]:
                    in_degree[child] -= 1
                    if in_degree[child] == 0 and child not in processed:
                        next_queue.append(child)

        # Add the current level to the result
        if level:
            ques = []
            for item in level:
                parts = item.split('~')
                que = next((x for x in questions if x.get("code", "") == parts[0]
                            and x.get("source", "") == parts[1]), None)
                if que is None:
                    continue
                ques.append(que)
            result.append(ques)

        # Update the queue for the next iteration
        queue = next_queue

    return result


def get_reverse_graph(questions: List[dict]):
    dep_graph = get_dep_graph(questions)
    return reverse_graph(dep_graph)


def get_execution_order(questions: List[dict]):
    dep_graph = get_dep_graph(questions, False)
    return get_graph_exec_order(questions, dep_graph)


def execution_order(questions: List[dict]):
    dep_graph = get_dep_graph(questions)
    return {
        "order": get_execution_order(questions),
        "graphs": dep_graph,
        "reverse": get_reverse_graph(questions)
    }
