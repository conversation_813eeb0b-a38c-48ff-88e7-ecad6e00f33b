OPENAI_MODEL = "gpt-4o"
GEMINI_MODEL = "gemini-2.5-flash"
GEMINI_PRO_MODEL = "gemini-2.5-pro"
GEMINI_LITE_MODEL = "gemini-2.5-flash-lite"
RE_RANK_MODEL = GEMINI_LITE_MODEL

MODEL = GEMINI_MODEL  # default

FIELD_TYPE_MAP = {
    'DC': 'DISCHARGE',
    'DAH': 'DEATH_AT_HOME',
    'FUC': 'CHECKLIST',
    # 'FUC': 'FOLLOW_UP',
    'TRN': 'TRANSFER',
    'ROC': 'RESUMPTION_OF_CARE',
    'ROU': 'CHECKLIST',
    'SOC': 'SOC_CHECKLIST',
    'CARE_PLAN': 'CARE_PLAN'
}

BACKWARD_COMPATIBILITY_MAP = {
    "SocChecklist": "SOC",
    "Checklist": "FUC",
    "Referral": "REFER<PERSON><PERSON>",
}
