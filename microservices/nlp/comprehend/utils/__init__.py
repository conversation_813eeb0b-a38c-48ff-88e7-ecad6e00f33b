import ast
import asyncio
import base64
import functools
import json
import random
import re
import sys
import time
from datetime import timedelta, datetime
from json import JSONDecodeError
from typing import Text, List, Union, Dict, Generator
from urllib.parse import unquote

import dspy
import requests
from dspy import make_signature
from google.genai import Client, types
from loguru import logger

from config import settings
from packages.langfuse.client import StatefulTraceClient, StatefulSpanClient
from ..lib.splitter import split_content


def embedding_text(i: dict) -> str:
    examples = []
    for eg in i.get("examples", []):
        if ((eg.get("answer") is None) or (eg.get('context') is None) or
                (isinstance(eg.get('context'), list) and len(eg.get('context')) == 0)):
            logger.warning('--- Empty example :: ', i.get('code'))
        else:
            examples.append(f"transcript: {eg['context'][0]}; answer: {eg['answer']}")

    examples_formatted = '\n'.join(examples)

    return f"""Question: {i.get("question", "")};\n Code: {i.get("code")};\n Field Name: {i.get("name")};\n Criteria: {i.get("criteria")};\n Examples: {examples_formatted};"""


def get_date(target_time: str):
    import calendar
    from datetime import datetime

    if target_time == "" or target_time is None:
        time = datetime.now()
    else:
        time = datetime.strptime(target_time, "%Y-%m-%d %H:%M:%S")

    units = {
        "year": time.year,
        "month": calendar.month_name[time.month],
        "weekday": calendar.day_name[time.weekday()]
    }

    return time.strftime("%m/%d/%Y"), 'MM/DD/YYYY', units


def get_prompt_date(visit_date: str = None):
    date, format, units = get_date(visit_date)
    return f"Today is {date} in the format {format}; It's a {units['weekday']}, {units['month']}, {units['year']}."


def get_net_prompt_with_fallback(reference: str, langfuse, variables: dict = None, fallback: str = ""):
    lf_prompt = get_net_prompt(reference, langfuse, variables)
    if lf_prompt[0] is not None:
        return lf_prompt
    else:
        return fallback, None, None

def get_net_prompt(reference: str, langfuse, variables: dict = None):
    ref_map = {
        'summary': 'SUMMARY_QA',
        'referral': 'REFERRAL_QA',
        'referral-gemini': 'REFERRAL_GEMINI',
        'referral-gemini-qa': 'REFERRAL_GEMINI_QA',
        'compliance-gemini': 'COMPLIANCE_GEMINI',
        'compliance-gemini-qa': 'COMPLIANCE_GEMINI_QA',
        'transcript_pipeline': 'TRANSCRIPT',
        'transcript': 'TRANSCRIPT_QA',
        'care_plan': 'CARE_PLAN_FILTER',
        'correction': 'CORRECTION_QA',
        'correction_source_questions': 'CORRECTION_SOURCE_QUESTIONS',
        'source_questions': 'SOURCE_QUESTIONS_QA',
        'base_qa': 'BASE_QUALITY_ASSURANCE_QA',
        'live_qa': 'LIVE_QUALITY_ASSURANCE_QA'
    }

    try:
        prompt_client = langfuse.get_prompt(ref_map.get(reference, reference), label="latest",
                                            cache_ttl_seconds=60 if settings.PATRIUM_ENV != 'prod' else 360)
        configs = prompt_client.config
        fields = None
        if configs is not None:
            fields = {}
            for config in configs:
                key = config.get('key')
                field_type = config.get('type')

                # Clean Up
                ref_config = dict(config, type=None, key=None)

                fields[key] = dspy.InputField(**ref_config) if field_type == 'input' else dspy.OutputField(**ref_config)

        prompt = prompt_client.compile(**(variables or {}))
        return prompt, fields, prompt_client
    except Exception as e:
        logger.error(f"Unable to get prompt for {reference} :: {e}")
        return None, None, None


def get_signature(reference: str = 'transcript', langfuse=None):
    prompt, fields, prompt_client = get_net_prompt(reference, langfuse)
    if reference == 'transcript':
        return make_signature(
            signature=fields or vars(TranscriptQA)['model_fields'],
            instructions=prompt or TranscriptQA.__doc__,
            signature_name="TranscriptQA"), prompt_client
        # return TranscriptQA, prompt_client
    elif reference == 'summary':
        return make_signature(
            signature=fields or vars(SummaryQA)['model_fields'],
            instructions=prompt or SummaryQA.__doc__,
            signature_name="SummaryQA"), prompt_client
        # return SummaryQA, prompt_client
    elif reference == 'source_questions':
        return make_signature(
            signature=fields or vars(SourceQuestions)['model_fields'],
            instructions=prompt or SourceQuestions.__doc__,
            signature_name="SourceQuestions"), prompt_client
    elif reference == 'care_plan':
        return make_signature(
            signature=fields or vars(CarePlanFilter)['model_fields'],
            instructions=prompt or CarePlanFilter.__doc__,
            signature_name="CarePlanFilter"), prompt_client
        # return SourceQuestions, prompt_client
    elif reference == 'correction':
        return make_signature(
            signature=fields or vars(CorrectionQA)['model_fields'],
            instructions=prompt or CorrectionQA.__doc__,
            signature_name="CorrectionQA"), prompt_client
        # return CorrectionQA, prompt_client
    elif reference == 'referral':
        return make_signature(
            signature=fields or vars(ReferralQA)['model_fields'],
            instructions=prompt or ReferralQA.__doc__,
            signature_name="ReferralQA"), prompt_client
        # return ReferralQA, prompt_client
    elif reference == 'base_qa':
        return make_signature(
            signature=fields or vars(QualityAssurance)['model_fields'],
            instructions=prompt or QualityAssurance.__doc__,
            signature_name="QualityAssurance"), prompt_client
        # return QualityAssurance, prompt_client
    elif reference == 'live_qa':
        return make_signature(
            signature=fields or vars(LiveQualityAssurance)['model_fields'],
            instructions=prompt or LiveQualityAssurance.__doc__,
            signature_name="LiveQualityAssurance"), prompt_client
    elif reference == 'generate':
        return make_signature(
            signature=fields or vars(LiveQualityAssurance)['model_fields'],
            instructions=prompt or LiveQualityAssurance.__doc__,
            signature_name="LiveQualityAssurance"), prompt_client
    elif reference == 'sandbox':
        return make_signature(
            signature=fields or vars(LiveQualityAssurance)['model_fields'],
            instructions=prompt or LiveQualityAssurance.__doc__,
            signature_name="LiveQualityAssurance"), prompt_client
    return None


class QualityAssurance(dspy.Signature):
    __doc__ = """Your task is to assess the quality of answers and their rationales given by a model, given the relevant context used in answering them. 
Provide a rating of 1 if the answer and rationale make sense and are coherent, or 0 if they are unclear, inconsistent 
or lack a clear connection between the question, context, rationale and answer.
If the rating is 0, give a reason explaining why it doesn't make sense so that we can improve the model.
"""

    question = dspy.InputField(desc="${the question the model answered}")
    date = dspy.InputField(desc="${the day the session was recorded}")
    description = dspy.InputField(prefix="Clinician Context:",
                                  desc="${context / instruction the clinician used to ask the patient the question}")
    notes = dspy.InputField(prefix="Rationale Notes:",
                            desc="${additional notes given to the model to guide how it reasons through the question}")
    criteria = dspy.InputField(prefix="Criteria:", desc="${criteria to be used to answer the question}")
    relevant_data = dspy.InputField(prefix="Relevant Data:",
                                    desc="${Additional data points given to the model that may be relevant to answering the question}")
    context = dspy.InputField(prefix="Session:",
                              desc="${snippets of the transcript / session given to the model that may be relevant to answer}")
    rationale = dspy.InputField(prefix="Rationale:",
                                desc="${the rationale given by the model to it's answer}")
    answer = dspy.InputField(desc="${the answer given by the model}")

    assessment = dspy.OutputField(
        desc="${a JSON object with 'rating', 'reason', 'alt_rationale' and 'alt_answer' fields."
             "The reason must give a stepped deduction on the accuracy of the rationale and answer based on context."
             "The rating must be either 0 or 1. 0 is for unclear or inconsistent answers; 1 is for clear and coherent answers."
             "The alt_rationale field must give a more accurate rationale to replace the given one."
             "The alt_answer field must either be an accurate answer based on the given criteria or 'NA' if no valid answer.}")


class LiveQualityAssurance(dspy.Signature):
    __doc__ = """Your task is to assess the quality of answers and their rationales given by a model, given the relevant context used in answering them. 
Provide a rating of 1 if the answer and rationale make sense and are coherent, or 0 if they are unclear, inconsistent 
or lack a clear connection between the question, context, rationale and answer.
If the rating is 0, give a reason explaining why it doesn't make sense so that we can improve the model.
"""

    question = dspy.InputField(desc="${the question the model answered}")

    date = dspy.InputField(desc="${the day the session was recorded}")
    description = dspy.InputField(prefix="Clinician Context:",
                                  desc="${context / instruction the clinician used to ask the patient the question}")

    criteria = dspy.InputField(prefix="Criteria:", desc="${criteria to be used to answer the question}")

    notes = dspy.InputField(prefix="Rationale Notes:",
                            desc="${additional notes given to the model to guide how it reasons through the question}")

    relevant_data = dspy.InputField(prefix="Relevant Data:",
                                    desc="${Additional data points given to the model that may be relevant to answering the question}")

    context = dspy.InputField(prefix="Session:",
                              desc="${snippets of the transcript / session given to the model that may be relevant to answer}")
    rationale = dspy.InputField(prefix="Rationale:",
                                desc="${the rationale given by the model to it's answer}")
    answer = dspy.InputField(desc="${the answer given by the model}")

    assessment = dspy.OutputField(
        desc="${a JSON object with 'rating', 'reason', 'alt_rationale' and 'alt_answer' fields."
             "The reason must give a stepped deduction on the accuracy of the rationale and answer based on context."
             "The rating must be either 0 or 1. 0 is for unclear or inconsistent answers; 1 is for clear and coherent answers."
             "The alt_rationale field must give a more accurate rationale to replace the given one."
             "The alt_answer field must either be an accurate answer based on the given criteria or 'NA' if no valid answer.}")


class TranscriptQA(dspy.Signature):
    __doc__ = """Extract / Infer the answers to the question from a clinician's transcript snippets and additional data points given the answering criteria. 
Pay close attention to the question code provided, as it is crucial for identifying the specific OASIS-E (cms.gov) manual question, medical assessment, or custom question being addressed.
When assessing the snippets, always refer back to the question code and criteria or schema to ensure you're answering the right question accurately. 
The snippets are organized in the same order as their position in the transcript. Focus only on direct confirmation from the clinician regarding the right answer; refer to the patient's answer if there is no confirmation.
Ensure to get explicit references to the question or its code in the snippets in order to get the answer. If none exists, respond with NA.
Make sure the answer is directly representative of the answering criteria format.
For example, if the answering criteria starts with 0, then the answer should start with 0.
Respond with 'NA' in the answer field if no answer is found in the snippets or if the snippets do not directly address the question indicated by the provided code.
Be aware that question codes may include alphanumeric combinations specific to OASIS-E, standard medical assessments, or custom formats. Always use the code as a primary identifier for the question being asked.
If multiple snippets relate to the question code, synthesize the information to provide the most accurate and complete answer according to the answering criteria.

The next sections are structure and example scenarios:
"""

    question = dspy.InputField(desc="${the question to be answered}")
    date = dspy.InputField(desc="${the day the transcript was recorded}")
    description = dspy.InputField(prefix="Clinician Context:",
                                  desc="${context / instruction the clinician used to ask the patient the question}")
    notes = dspy.InputField(prefix="Rationale Notes:",
                            desc="${additional notes that guide how you reason through the problem}")
    criteria = dspy.InputField(prefix="Criteria:", desc="${criteria to be used to answer the question}")
    schema_def = dspy.InputField(prefix="Schema:",
                                 desc="${an optionally availed JSON schema defining how the answer should formatted}")
    previous_value = dspy.InputField(prefix="Current Answer:",
                                     desc="${In the of a question with a schema (JSON Object), this is the current answer given to the question, you may need to update (append / modify the value of a key) based on new information}")
    relevant_data = dspy.InputField(prefix="Relevant Data:",
                                    desc="${Additional data points that may be relevant to answering the question}")
    #  Field here is "context" so we can auto format the output to a set of passages instead of a single string

    context = dspy.InputField(prefix="Transcript:",
                              desc="${snippets of the same transcript that may contain the answer, some crucial words "
                                   "may be misspelled - use context to get the right spelling}")

    answer = dspy.OutputField(
        desc="${a JSON object with a 'rationale' field and an 'answer' field, both being string values."
             "The rationale field should give a step by step deduction of each transcript slowly and carefuly analyzed to identify"
             " & ensures we have the correct answer, The answer field should contain the correct answer based on the criteria and rationale}")


class CorrectionQA(dspy.Signature):
    __doc__ = """Extract / Infer the correct answer to the question given with the answering criteria based on the clinicians feedback.
Some words in the transcript may be misspelled, use the context to correct them.
Respond with 'NA' in the answer field if no update / answer can be arrived at.
The next section is some example scenarios."""

    question = dspy.InputField(desc="${the question to be answered}")
    date = dspy.InputField(desc="${the day the transcript was recorded}")
    criteria = dspy.InputField(prefix="Criteria:", desc="${criteria to be used to answer the question}")
    notes = dspy.InputField(prefix="Rationale Notes:",
                            desc="${additional notes that guide how you reason through the problem}")
    update = dspy.InputField(desc="${the clinician's feedback / note to get the answer}")

    answer = dspy.OutputField(desc="${a JSON object with a rationale field and answer field."
                                   "The rationale field should be a short & clear deduction of the given information to get the right answer,"
                                   "The answer field should contain the correct answer based on the given criteria, update and rationale}")


class ReferralQA(dspy.Signature):
    __doc__ = """Extract / Infer the answers to the question from the document sections given the answering criteria.
 Respond with 'NA' if no answer is found.
 Based on the format given below only generate the rationale and answer sections, not the other fields such as question, criteria, sections.
 """

    question = dspy.InputField(desc="${the question to be answered}")
    criteria = dspy.InputField(prefix="Criteria:", desc="${criteria to be used to answer the question}")
    context = dspy.InputField(prefix="Sections:", desc="${document section that may contain relevant content}")
    schema_def = dspy.InputField(prefix="Schema:",
                                 desc="${an optionally availed JSON schema defining how the answer should formatted}")

    rationale = dspy.OutputField(
        desc="${a step-by-step deduction that identifies the correct response, which will be provided below as an answer}")
    answer = dspy.OutputField(desc="${an answer based on the following criteria}")


class SummaryQA(dspy.Signature):
    """Create a summary in narrative form for each section of the medicare home health visit based on the transcript below per medicare standards, including:
Medical History: Summarize the patient's background, including age, gender, medical history, and current health condition.
Assessment Findings: Highlight the main findings from the health assessment, including vital signs, physical examination results, and any identified health issues.
Care Plan: Outline the proposed care plan, including the goals of care, recommended treatments, therapies, medications, and any other interventions.
Patient and Caregiver Education: Note any instructions or education provided to the patient and/or caregivers regarding the care plan, medication management, or lifestyle adjustments.
Follow-Up: Mention any scheduled follow-up visits or further evaluations planned, including the frequency and purpose of these follow-ups.
Additional Notes: Text field of a list of any other important details or observations made during the visit that are relevant to the patient's care.
Respond with a json object with a single 'summary' field as shown in the example below."""

    context = dspy.InputField(prefix="Transcript:", desc="${transcript of the clinical conversation to summarize}")
    date = dspy.InputField(desc="${the day the transcript was recorded}")

    answer = dspy.OutputField(desc="${a json object with a single 'summary' field as demonstrated above}")


class CorrectionQuestions(dspy.Signature):
    __doc__ = """Given a transcript of a clinician updating / correcting / filling in information in a document, infer the original clinical questions that need to be updated based on the clinician's transcript.
Provide the questions in JSON format as demonstrated.
Respond with an empty array of items if no questions need to be updated."""

    context = dspy.InputField(prefix="Transcript:", desc="${transcript of the clinician updating fields the document}")
    assessment = dspy.InputField(prefix="Pre assessment:",
                                 desc="${pre assessed reasoning of the transcript that might be useful in finding the right question}")

    rationale = dspy.OutputField(
        desc="${a step-by-step deduction of the transcript that identifies the questions that need to be answered / updated}")
    answer = dspy.OutputField(
        desc="${the questions that need to be answered / updated in json format to be parsed and is without any formating}")


class CarePlanFilter(dspy.Signature):
    __doc__ = """Given the following criteria details and respective relevant data points, determine whether the given care plan step should be included 
    in the patients care plan. Respond with a JSON object that details the rationale for the step being included or excluded."""

    question = dspy.InputField(prefix="Care Plan Step:",
                               desc="${The care plan step that needs to be included in the patients care plan}")
    criteria = dspy.InputField(prefix="Inclusion Criteria:",
                               desc="${criteria details that should be considered in determining whether the step should be included}")
    relevant_data = dspy.InputField(prefix="Relevant Data:",
                                    desc="${data points that are relevant to determining inclusion criteria}")
    answer = dspy.OutputField(
        desc="${a JSON object with a 'rationale' field and an 'answer' field, both being string values."
             "The rationale field should give a step by step deduction as to why the step should be included in the patients care plan. "
             "The answer field should be either 'INCLUDE' or 'EXCLUDE' depending on the rationale}")


class SourceQuestions(dspy.Signature):
    __doc__ = """Given a transcript of a clinician speaking to a patient with the intent of collecting information for documentation, infer the clinical questions that are being answered based on the transcript.
The questions can be: 
- Home health care OASIS-E items
- Clinical assessment questions such as physical examination (vitals) or review of systems, symptoms and mental status
- Social history & psychosocial assessment
- Clinical / Medical history
- Personal medical information about the patient
- Payment information e.g medicare medicaid, private insurance etc.
Provide the questions in JSON format as demonstrated: The object must have an "items" field that contains an array.
For the snippets, capture the full respective transcript context to answer the question, especially OASIS 
codes and context setters.
Respond with an empty array of items if no questions need to be updated."""

    context = dspy.InputField(prefix="Transcript:", desc="${transcript of the clinician updating fields the document}")

    answer = dspy.OutputField(
        desc="${the questions that need to be answered / updated in json format to be parsed and is without any formating}")


def merge_questions(questions: List[dict]) -> List[dict]:
    merged = []
    for q in questions:
        exists = next((x for x in merged if x['code'] == q['code']), None)
        # Get the index of the question
        if exists is not None:
            # update the question
            snips = set(exists.get('snippets', []))
            for snippet in q.get('snippets', []):
                snips.add(snippet)
            exists['snippets'] = list(snips)
        else:
            merged.append(q)
    return merged


def merge_answers(answers: List[dict]) -> List[dict]:
    merged = []
    for q in answers:
        exists = next((x for x in merged if x.get('code') == q.get('code')), None)
        # Get the index of the answer
        if exists is not None:
            # update the answer
            exists['previous'] = {
                'rationale': exists.get('rationale'),
                'answer': exists.get('answer')
            }
            exists['answer'] = q.get('answer')
            exists['snippets'] = q.get('snippets') or exists.get('snippets')
            exists['rationale'] = q.get('rationale') or exists.get('snippets')
        else:
            merged.append(q)
    return merged


def get_unhandled_questions(trace: StatefulTraceClient, handled: List[dict], questions: List[dict],
                            include_null_answers=False) -> List[dict]:
    span = trace.span(
        trace_id=trace.id,
        name="Get Unhandled Questions",
        input={
            "handled": handled,
            "questions": questions,
            "include_null_answers": include_null_answers
        }
    )
    handled_codes = [x['code'] for x in handled]
    null_answers = [x for x in handled if x['answer'] is None]

    for null_ans in null_answers:
        que = next((x for x in questions if x['code'] == null_ans['code']), None)
        if que is not None:
            que['snippets'] = null_ans['snippets']

    # merge null answered questions with unhandled
    merge = [x for x in questions if
             x['code'] not in handled_codes or (
                     include_null_answers and x['code'] in [y['code'] for y in null_answers])]

    span.end(output={
        "handled_codes": handled_codes,
        "null_answers": [x['code'] for x in null_answers],
        "unhandled": [x['code'] for x in merge]
    })
    return merge


def re_handle_questions(trace: StatefulTraceClient, re_handle: List[str], sourced_questions: list, all_questions: list):
    # Take snippets in the first run and add then to the questions
    trace.event(
        trace_id=trace.id,
        name="Re-Handle Questions",
        input={
            "re_handle": re_handle,
        }
    )
    re_questions = list(filter(lambda x: x["code"] in re_handle, all_questions))

    questions = []
    for handle in re_questions:
        for question in sourced_questions:
            if question["code"] == handle:
                handle["snippets"] = question["snippets"]
        questions.append(handle)

    return questions


def prep_documents(trace: StatefulTraceClient, content: Text, lower_limit=35, upper_limit=100, sentence_buffer=4):
    span = trace.span(
        trace_id=trace.id,
        name="Prep Documents",
        input={
            "lower_limit": lower_limit,
            "upper_limit": upper_limit,
            "sentence_buffer": sentence_buffer,
            "content": content
        }
    )

    chunks = split_content(content, lower_limit=lower_limit, upper_limit=upper_limit, sentence_buffer=sentence_buffer)
    documents = [x['text'] for x in chunks]

    span.end(output={"documents": documents})
    return documents


def prep_document_list(trace: StatefulTraceClient, content: list[dict], lower_limit=3, upper_limit=7, sentence_buffer=5,
                       target_words=200):
    span = trace.span(
        trace_id=trace.id,
        name="Prep Documents",
        input={
            "lower_limit": lower_limit,
            "upper_limit": upper_limit,
            "sentence_buffer": sentence_buffer,
            "content": content
        }
    )

    sections = [f"[{x['timestamp']}] {x['speaker']}: {x['sentences']}" for x in content]
    documents: list[str] = []

    current_doc = []
    word_count = 0

    for section in sections:
        section_words = len(section.split())

        if word_count + section_words > target_words and len(current_doc) >= lower_limit:
            # Join current sections and add to documents
            documents.append("\n".join(current_doc))
            current_doc = []
            word_count = 0

        current_doc.append(section)
        word_count += section_words

        # If we hit upper limit, force a new document
        if len(current_doc) >= upper_limit:
            documents.append("\n".join(current_doc))
            current_doc = []
            word_count = 0

    # Add any remaining sections
    if current_doc:
        documents.append("\n".join(current_doc))

    span.end(output={"documents": documents})
    return documents


def synchronize_async_helper(to_await):
    async_response = []

    async def run_and_capture_result():
        r = await to_await
        async_response.append(r)

    loop = asyncio.new_event_loop()
    coroutine = run_and_capture_result()
    loop.run_until_complete(coroutine)
    return async_response[0]


def prepare_context(context: list):
    chosen_sections = []

    # Iterate through context docs
    for document_section in context:
        # clean up the text
        chosen_sections.append(document_section.replace("\n", " "))

    return chosen_sections


def merge_dicts(source: dict, target: dict):
    return target.update(source)


def chunk_list(array, chunk_size=10):
    def divide_chunks(l, n):
        # looping till length l
        for i in range(0, len(l), n):
            yield l[i:i + n]

    return list(divide_chunks(array, chunk_size))


def weigh(content, unit="kb"):
    measure = {
        "kb": 1000,
        "mb": 1000000,
        "gb": 1000000000
    }

    size = sys.getsizeof(content) / measure[unit]
    return size, f"{size} {unit}"


def batchify(seq: list, size: int) -> Generator[list, None, None]:
    for pos in range(0, len(seq), size):
        yield seq[pos:pos + size]


def get_transcript_from_file(path: Text):
    with open(path, "r") as f:
        return f.read()


def get_transcript_from_url(url: Text):
    response = requests.get(url)
    return " ".join(response.text.splitlines())


def is_valid_url(x):
    from urllib.parse import urlparse
    try:
        result = urlparse(x)
        return all([result.scheme, result.netloc])
    except:
        return False


def is_json(text):
    try:
        return json.loads(text, strict=False)
    except ValueError as e:
        return False


def post_process_answer(answer: str):
    if type(answer) != str:
        return answer

    lower_answer = answer.lower()
    if ((lower_answer == 'not available')
            or (lower_answer == 'na')
            or (lower_answer == 'null')
            or (lower_answer == 'n/a')):
        return None
    return answer


def post_process_answers(answers: list, override: list = None):
    if override is None:
        override = []

    for _ans in answers:
        if isinstance(_ans['answer'], str):
            lower_answer = _ans['answer'].lower()
            if ((lower_answer == 'not available')
                    or (lower_answer == 'na')
                    or (lower_answer == 'null')
                    or (lower_answer == 'n/a')
                    or ('not mentioned' in lower_answer) or ('not specified' in lower_answer)
                    or ('not answer available' in lower_answer)
                    or ('none of the above' in lower_answer) or (lower_answer == '') or (lower_answer == '')
                    or lower_answer == 'not provided in the transcript.' or ('not provided' in lower_answer)
                    or ('no answer can be extracted' in lower_answer) or ('unable to answer' in lower_answer)
                    or ('unable to determine' in lower_answer) or ('not enough information' in lower_answer)
                    or ('cannot be determine' in lower_answer)):
                _ans['answer'] = None

        # Override the answer if it is in the override list
        if override is not None:
            exists = [x for x in override if x['code'] == _ans['code']]
            if len(exists) > 0:
                _ans['answer'] = exists[0]['answer']

    return answers


def multi_replace(text, chars: list, targets: Union[str, list]):
    for c in chars:
        replacer = targets if isinstance(targets, str) else targets[chars.index(c)]
        text = text.replace(c, replacer)
    return text


def safe_json_load(text: str, logs=False):
    try:
        if type(text) in [list, dict]:
            return text
        return json.loads(text, strict=False)
    except JSONDecodeError as e:
        if logs: logger.error(f"Error decoding JSON: {e}")
        return text


def clean_text(text, logs=False):
    if isinstance(text, str):
        if text.startswith("[") and text.endswith("]"):
            return safe_json_load(text, logs=logs)
        json_start = text.find("{")
        json_end = text.rfind("}")
        if json_start == -1 or json_end == -1:
            return text
        clean = multi_replace(text[json_start:json_end + 1], ['\n', '\t', '\r', '\0'], ' ')
        if logs: logger.debug(f"JSON Response: {clean}")
        return clean
    else:
        return text


def extract_json(response, should_raise=True, logs=False):
    clean_response = clean_text(response, logs=logs)
    try:
        if isinstance(response, str):
            clean = clean_text(response, logs=logs)
            clean = ast.literal_eval(clean)
            return clean
        else:
            return response
    except ValueError as ve:
        if logs: logger.error(f"Value Error extracting JSON :: {clean_response} :: {ve}")
        return safe_json_load(clean_response)
    except SyntaxError as se:
        if logs: logger.error(f"Syntax Error extracting JSON :: {clean_response} :: {se}")
        return safe_json_load(clean_response)
    except Exception as e:
        if logs: logger.error(f"Error extracting JSON :: {response} :: {e}")
        if should_raise:
            raise e
        return response


def get_as_base64(source, ref):
    import base64
    if source == 'url':
        return base64.b64encode(requests.get(ref).content).decode('utf-8')
    if source == 'file':
        with open(ref, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')


def object_type(obj):
    return str(type(obj))


def traverse(obj, func):
    obj_type = object_type(obj)
    if obj_type == "<class 'dict'>":
        for key in obj:
            func(obj, key)
            traverse(obj[key], func)
    elif obj_type == "<class 'list'>":
        for index, val in enumerate(obj):
            func(obj, index)
            traverse(val, func)


def filter_mapping(result, mapping):
    def refs(obj, key):
        if key == 'src':
            val = obj[key]
            if isinstance(val, list):
                selected_ref.extend(val)
            else:
                selected_ref.append(obj)

    selected_ref = []
    traverse(result, refs)

    selected_mapping = []
    for ref in selected_ref:
        # convert PG 1 | L15 to page 1 line 15 dict
        # convert PG 1 | L15-L16 to [{page 1 line 15}, {page 1 line 16}] dict
        refs = ref.split("|")
        page_ref = refs[0].strip().replace("PG", "").strip()
        page_ref = int(page_ref)
        line_ref = refs[1].strip()

        items = []
        if "-" in line_ref or "," in line_ref:
            start = None
            end = None

            if "-" in line_ref:
                start, end = line_ref.split("-")
            elif "," in line_ref:
                start, end = line_ref.split(",")

            if start is not None and end is not None:
                start = int(start.replace("L", "").strip())
                end = int(end.replace("L", "").strip())
                for i in range(start, end + 1):
                    items.append({"page": page_ref, "line": i})
        else:
            r = line_ref.replace("L", "").strip()
            items.append({"page": page_ref, "line": int(r)})

        for line in mapping:
            for item in items:
                if line['page'] == item['page'] and line['line'] == item['line']:
                    selected_mapping.append(line)
                    break

    return selected_mapping


def create_ascii_text(span: StatefulSpanClient, ocr_data: List[Dict], page=None, width: int = 180, add_line_no=True,
                      height: int = int(100 * 1.414), max_empty_n=3) -> (str, list):
    span = span.span(
        name="Create ASCII Text",
        input={
            "ocr_data": ocr_data,
            "page": page,
            "width": width,
            "height": height,
            "max_empty_n": max_empty_n
        }
    )

    canvas = [[' ' for _ in range(width)] for _ in range(height)]
    # add a line no. to beginning of canvas lines
    for i in range(height):
        canvas[i] = canvas[i] if (add_line_no is False) else [f"L{i + 1}"] + canvas[i]
        # canvas[i] = [f"{i + 1:03d}"] + canvas[i]

    logger.debug(f"Creating page text :: {width} x {height} :: Page {page}")

    block_mapping = []
    to_add = 1 if add_line_no else 0
    for block in ocr_data:
        if block['BlockType'] == 'LINE':
            bbox = block['Geometry']['BoundingBox']
            left = int(bbox['Left'] * width) + to_add
            top = int(bbox['Top'] * height)
            block_mapping.append({
                "line": top + to_add,
                "page": page,
                "block": {
                    "Text": block['Text'],
                    "Geometry": block['Geometry']
                }
            })

            # Place the text on the canvas
            text = block['Text']
            for i, char in enumerate(text):
                if left + i < width:
                    canvas[top][left + i] = char

    # Clean up more than n empty lines in the canvas
    cleaned_canvas = []
    empty_count = 0
    last_n_lines = list(range(height - 5, height))
    for ix, row in enumerate(canvas):
        is_empty = all([c == ' ' for c in row[1:]])
        if ix in last_n_lines and is_empty:
            continue
        if not is_empty:
            cleaned_canvas.append(''.join(row).rstrip())
            empty_count = 0
            continue

        empty_count += 1
        # skip the line
        if empty_count > max_empty_n:
            continue

        cleaned_canvas.append(''.join(row).rstrip())

    result = "\n".join(cleaned_canvas).strip()
    span.end(output={
        "ascii_text": result,
        "block_mapping": block_mapping
    })
    return result, block_mapping


def remove_code_backticks(text):
    if type(text) != str:
        return text
    import re
    pattern = r'```[\w]*\n|```'
    cleaned_text = re.sub(pattern, '', text)
    return cleaned_text.strip()


def process_json_text(text: str):
    final_text = ""

    if text is not None:
        try:
            json_text = json.loads(text)
            if isinstance(json_text, list):
                final_text = functools.reduce(
                    lambda x, y: x + f" {re.sub('</?[^>]+(>|$)', '', y['text'])}" if y.get("text") is not None else x,
                    json_text, "")
        except (TypeError, json.decoder.JSONDecodeError) as e:
            print("--- Error text:", text)
            # message = f" --- Error parsing :: {text} --- {e}"
            # raise Exception(message)
            return text

    return final_text.strip()


def get_unique_items(items: list) -> list:
    unique_snippets = set()

    if items:
        for item in items:
            if isinstance(item, list):
                for snip in item:
                    unique_snippets.add(snip)
            elif isinstance(item, str):
                unique_snippets.add(item)

    return list(unique_snippets)


def seconds_to_time_string(seconds):
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)

    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"


def upload_to_gemini(client: Client, path, name, mime_type=None, replace=False) -> types.File:
    name = name.replace("_", "-").replace(" ", "-")
    try:
        exists = client.files.get(name=name)
    except Exception:
        exists = None

    if exists is not None:
        if replace:
            # lest than an hour old
            if exists.update_time.timestamp() > datetime.now().timestamp() - timedelta(hours=1).seconds:
                return exists
            client.files.delete(name=exists.name)
        else:
            logger.debug(f"File {name} already exists, skipping upload")
            return exists

    file = client.files.upload(file=path, config=types.UploadFileConfig(
        name=name,
        mime_type=mime_type
    ))
    logger.debug(f"Uploaded file '{file.display_name or file.name}' as: {file.uri}")
    return file


def delete_gemini_docs(client: Client, doc_names):
    for doc_name in doc_names:
        try:
            client.files.delete(name=doc_name)
        except Exception:
            pass


def generate_object_id(prefix=None):
    timestamp = format(int(time.time()), 'x')
    formatted_prefix = f"{prefix}_" if prefix else ""
    random_chars = ''.join(random.choice('0123456789abcdef') for _ in range(16))
    object_id = f"{formatted_prefix}{timestamp}{random_chars}"
    return object_id.lower()


def clone_object(obj: dict | list):
    return json.loads(json.dumps(obj))


def dict_from_base_64(b):
    return json.loads(unquote(base64.b64decode(b).decode('utf-8')))
