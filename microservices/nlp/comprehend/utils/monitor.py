import logging
import os.path
from sys import stdout

import google.cloud.logging
from google.cloud.logging_v2.handlers import CloudLoggingHandler
from google.oauth2 import service_account
from loguru import logger

from config import settings
from packages.langfuse.client import StatefulTraceClient, Langfuse


def setup_trace(fuse: Langfuse, trace: StatefulTraceClient = None, **kwargs) -> StatefulTraceClient:
    tags = [settings.ENV, settings.PATRIUM_ENV]

    if trace is not None:
        return trace.update(tags=tags, **kwargs)

    return fuse.trace(tags=tags, **kwargs)


def setup_logger(level: int = logging.DEBUG):
    logger.remove()

    if settings.PATRIUM_ENV != 'local':
        credentials = service_account.Credentials.from_service_account_file(
            filename=os.path.join('.', 'config', 'gcp', 'credentials.json')
        )

        client = google.cloud.logging.Client(project="patrium-health-gcp", credentials=credentials)

        handler = CloudLoggingHandler(
            client,
            name=f"{settings.PATRIUM_ENV}-nlp.svc",
            labels={
                "service": f"[{settings.PATRIUM_ENV.upper()}] nlp_svc",
                "source": f"{settings.PATRIUM_ENV}-nlp.svc"
            })
        handler.setLevel(level=level)

        logger.add(handler, backtrace=True)
    else:
        logger.add(stdout, level=level, backtrace=True)


def _setup_logger(level: int = logging.DEBUG):
    # Remove any existing handlers
    logger.remove()

    # Define a custom format for logs
    log_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level> | "
        "{extra}"
    )

    if settings.PATRIUM_ENV != 'local':
        try:
            credentials = service_account.Credentials.from_service_account_file(
                filename=os.path.join('.', 'config', 'gcp', 'credentials.json')
            )

            client = google.cloud.logging.Client(
                project="patrium-health-gcp",
                credentials=credentials
            )

            # Create Cloud Logging handler with detailed labels
            handler = CloudLoggingHandler(
                client,
                name=f"{settings.PATRIUM_ENV}-nlp.svc",
                labels={
                    "service": f"[{settings.PATRIUM_ENV.upper()}] nlp_svc",
                    "source": f"{settings.PATRIUM_ENV}-nlp.svc"
                }
            )
            handler.setLevel(level=level)

            # Add handler with custom format for structured logging
            def cloud_logging_format(record):
                # Include additional context in structured format
                record["extra"].update({
                    "function": record["function"],
                    "file": record["file"].name,
                    "line": record["line"]
                })
                return record["message"]

            logger.add(
                handler,
                level=level,
                format=cloud_logging_format,
                backtrace=True,
                diagnose=True,  # Includes variable values in tracebacks
                enqueue=True,  # Thread-safe logging
                catch=True  # Catch any exceptions during logging
            )

        except Exception as e:
            logger.error(f"Failed to setup Cloud Logging: {e}")
            # Fallback to stdout if cloud logging setup fails
            logger.add(stdout, level=level, format=log_format, backtrace=True)
    else:
        # Local environment logging
        logger.add(
            stdout,
            level=level,
            format=log_format,
            backtrace=True,
            diagnose=True,
            enqueue=True,
            catch=True
        )

    # Add a separate file handler for errors
    logger.add(
        "logs/errors.log",
        level="ERROR",
        format=log_format,
        rotation="500 MB",  # Rotate files when they reach 500MB
        retention="1 week",  # Keep logs for 1 week
        compression="zip",  # Compress rotated logs
        backtrace=True,
        diagnose=True,
        enqueue=True
    )

    return logger
