from typing import List, Union, Literal

import google.generativeai as genai
import requests
from loguru import logger

from config import settings


def embed(prompts: List[str], type: Union[Literal['doc'], Literal['query']] = 'doc', title: str = None) -> List[
    List[float]]:
    try:
        logger.info(f"Embedding inputs: {len(prompts)}")
        resp = requests.post(
            url=settings.RUNPOD_EMBEDDING_URL,
            headers={"Authorization": f"Bearer {settings.RUNPOD_API_KEY}"},
            json={
                'input': {
                    'model': "abhinand/MedEmbed-large-v0.1",
                    'input': prompts
                }
            }
        )
        result = resp.json()
        embeddings = [x.get('embedding') for x in result.get('output').get('data')]
        return embeddings
    except Exception as e:
        logger.error(f"Error embedding prompts: {e}")
        raise e


def __embed(prompts: List[str], type: Union[Literal['doc'], Literal['query']] = 'doc', title: str = None) -> List:
    try:
        logger.info(f"Embedding inputs: {len(prompts)}")
        result = genai.embed_content(
            model="models/text-embedding-004",
            content=prompts,
            title=title if type == 'doc' else None,
            task_type="RETRIEVAL_QUERY" if type == 'query' else "RETRIEVAL_DOCUMENT",
        )
        return result.get('embedding')
        # return list(map(lambda x: x.values, ))
    except Exception as e:
        logger.error(f"Error embedding prompts: {e}")
        raise e


def _embed(prompts: List[str], type: Union[Literal['doc'], Literal['query']] = 'doc', title: str = None) -> List[
    List[float]]:
    try:
        logger.info(f"Embedding inputs: {len(prompts)}")
        resp = requests.post(
            url=settings.BASETEN_EMBEDDING_URL,
            headers={"Authorization": f"Api-Key {settings.BASETEN_API_KEY}"},
            json={
                'input': prompts
            }
        )
        result = resp.json()
        return result
    except Exception as e:
        logger.error(f"Error embedding prompts: {e}")
        raise e
