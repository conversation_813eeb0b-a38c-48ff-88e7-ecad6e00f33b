from __future__ import annotations

import json
from typing import List, Any

from loguru import logger
from weaviate import WeaviateClient
from weaviate.classes.query import MetadataQuery
from weaviate.collections import Collection
from weaviate.collections.classes.filters import _Filters, Filter

from comprehend.models.embedding import embed
from comprehend.utils import dict_from_base_64


class HybridSearchRetriever:
    """`Weaviate hybrid search` retriever."""

    client: WeaviateClient
    """keyword arguments to pass to the Weaviate client."""
    index_name: str
    """The name of the index to use."""
    alpha: float = 0.5
    """The weight of the text key in the hybrid search."""
    k: int = 4
    """Whether to return the score of the results."""
    score: bool = False
    """The number of results to return."""
    attributes: List[str]
    exec_args: dict
    collection_config: dict
    collection: Collection

    def __init__(self, collection_config, **kwargs: Any) -> None:
        self.client = kwargs.get("client")
        self.agency_id = kwargs.get("agency_id")
        self.index_name = kwargs.get("index_name")
        self.k = kwargs.get("k")
        self.attributes = kwargs.get("attributes")
        self.exec_args = kwargs.get("exec_args")
        self.collection_config = collection_config

        if kwargs.get("reset", False):
            self.client.collections.delete(self.index_name)

        if not self.client.collections.exists(self.index_name):
            self.client.collections.create(**self.collection_config)

        self.collection = self.client.collections.get(self.index_name)

        if self.agency_id:
            self.collection = self.collection.with_tenant(self.agency_id)

    def load_docs(self, count=3):
        try:
            result = self.collection.query.fetch_objects(limit=count)
            return len(result.objects) == 0
        except Exception as e:
            logger.error(f"Error: {e}")
            return True

    def add_documents(self, documents: List[dict]) -> None:
        with self.client.batch.dynamic() as batch:
            for i, doc in enumerate(documents):
                batch.add_object(properties=doc.get("object"),
                                 collection=self.index_name,
                                 uuid=doc.get("uuid"),
                                 vector=doc.get("vector"))

    def list(self, filters: _Filters, post_process=None, opts=None, attempts=0) -> List[dict]:
        if opts is None:
            opts = {}

        opts = {
            'return_properties': self.attributes,
            'limit': opts.get('limit') or self.k,
            **opts
        }

        retries = attempts or 0
        try:
            results = self.collection.query.fetch_objects(filters=filters, **opts)

            docs = []
            for res in results.objects:
                # metadata = res.metadata
                properties = res.properties
                docs.append({"uuid": str(res.uuid), **properties})

            docs = [dict(**dict_from_base_64(x.get('data')),
                         uuid=x.get('uuid'),
                         group_type=x.get('group_type')) for x in docs]
            logger.debug(f"Retrieved {len(docs)} documents for query")

            if post_process is not None:
                return post_process(docs)

            return docs
        except Exception as e:
            logger.error(f"Error: {e}. Retrying... {retries} :: {json.dumps(opts)}")
            if retries > 5:
                raise e
            return self.list(filters, post_process, opts, attempts=retries + 1)

    def invoke(self, query: str, opts: dict = None, attempts=0) -> List[dict]:
        if opts is None:
            opts = {}

        opts = {'return_properties': self.attributes, **opts}

        if type(query) == str:
            query = query.strip()

        if query is None or query == '':
            return []

        retries = attempts or 0
        try:
            vector = embed([query], 'query')[0]
            results = (
                self.collection.query.hybrid(
                    query,
                    vector=vector,
                    alpha=opts.get('alpha', self.alpha),
                    limit=opts.get('limit', self.k),
                    return_metadata=MetadataQuery(score=True, explain_score=True) if self.score else None,
                    **self.exec_args,
                    **dict((k, v) for k, v in opts.items() if k not in ['limit', 'alpha'])
                )
            )

            docs = []
            for res in results.objects:
                metadata = res.metadata
                properties = res.properties
                docs.append({"uuid": res.uuid, **properties})

            docs = [dict_from_base_64(x.get('data')) for x in docs]
            logger.debug(f"Retrieved {len(docs)} documents for query: {query}")

            return docs
        except Exception as e:
            logger.error(f"Error: {e}. Retrying... {retries} :: {query} :: {json.dumps(opts)}")
            if retries > 5:
                raise e
            return self.invoke(query, opts, attempts=retries + 1)

    def clean_up(self) -> None:
        logger.info(f"Cleaning up collection: {self.index_name}")
        self.collection.data.delete_many(
            where=Filter.by_property("code").not_equal("_q_")
        )
