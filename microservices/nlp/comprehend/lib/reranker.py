import json
from typing import List

from loguru import logger

from comprehend.utils import extract_json
from packages.langfuse.client import StatefulSpanClient
from .base_llm import call_llm
from ..pipelines import MAX_RETRIES
from ..utils.constants import RE_RANK_MODEL


def format_list(passages, key_ref):
    formatted_list = ""
    for idx, passage in enumerate(passages):
        formatted_list += f"[{idx + 1}] -> {passage if key_ref is None else passage.get(key_ref)}\n"
    return formatted_list


def reranker(passages: List[dict],
             query: str,
             top_n=10,
             span: StatefulSpanClient = None,
             key_ref="text",
             query_text='transcript snippet',
             passage_text='questions', attempt=0):
    retries = attempt or 0
    system_message = f"You are RankGP<PERSON>, an intelligent assistant that can rank {passage_text} based on how related " \
                     f"they are to a {query_text}"
    context_string = format_list(passages, key_ref)

    prompt = f"""{system_message}\n
I will provide you with {len(passages)} {passage_text}, each indicated by a number `index` field.
Rank the items based on their relevance to this query: \n{query} :

{context_string}

Search Query: {query}.

Rank and filter the {len(passages)} items above based on their relevance to the search query.
Use all the information available to you to rank the {passage_text}.
The output format should be a json object containing a field 'order' with index identifiers as shown.
{json.dumps({"order": list(range(1, len(passages) + 1))})}
Filter the items that are most relevant to the query, keep at least 2 items.
The items should be listed in the order of most relevant first using the index identifiers.
Only output the object with the list of index numbers (filtered and ordered by relevance), do not output any other text.
"""

    try:
        response = call_llm(prompt=prompt, model=RE_RANK_MODEL)
        ranked_order = extract_json(response).get("order")
        ranking = [passages[int(i) - 1] for i in ranked_order][:top_n]

        if span is not None:
            span.event(
                name="Reranking",
                input={
                    "query": query,
                    "passages": passages,
                    "top_n": top_n,
                },
                output={
                    "order": ranked_order,
                    "ranking": ranking
                }
            )

        return ranking
    except Exception as e:
        logger.error(f"Error in re-ranker: {e}")
        while retries < MAX_RETRIES:
            retries += 1
            logger.error(f"Retrying {retries} of {MAX_RETRIES}")
            return reranker(
                passages=passages,
                query=query,
                span=span,
                top_n=top_n,
                key_ref=key_ref,
                query_text=query_text,
                passage_text=passage_text,
                attempt=retries)
        raise e

# if __name__ == '__main__':
#     import time
#     passages_ = [
#         {"text": "did the quick brown fox jumps over the lazy elephant?"},
#         {"text": "did the quick brown fox jumps over the lazy rabbit?"},
#         {"text": "did the quick brown fox jumps over the lazy dog?"},
#         {"text": "did the quick brown fox jumps over the lazy cat?"},
#         {"text": "did the quick brown fox jumps over the lazy lion?"}
#     ]
#
#     query = "The quick brown fox jumps over the lazy dog."
#
#     start = time.time()
#     reranked_passages = reranker(passages_, query)
#     print(f"Time taken: {time.time() - start}")
#     print(reranked_passages)
