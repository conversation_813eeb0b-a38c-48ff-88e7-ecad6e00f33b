from google.genai import types
from loguru import logger
from openai import OpenAI

from comprehend.pipelines import gemini_client
from comprehend.utils.constants import GEMINI_MODEL, OPENAI_MODEL
from config import settings

oai = OpenAI(
    api_key=settings.FIREWORKS_API_KEY,
    base_url=settings.FIREWORKS_API_BASE
)


def _oai_call_llm(prompt, model=OPENAI_MODEL):
    try:
        response = oai.chat.completions.create(
            model=model,
            messages=[{
                "role": "user",
                "content": prompt
            }],
            stream=False,
            temperature=0.1,
            response_format={"type": "json_object"}
        )
        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"Error in LLM call [fallback]: {e}")
        raise e


def call_llm(prompt, model=GEMINI_MODEL, fallback_model=None):
    try:
        response = gemini_client.models.generate_content(
            model=model,
            config=types.GenerateContentConfig(
                temperature=0.3,
                top_p=0.95,
                thinking_config=types.ThinkingConfig(thinking_budget=0)
            ),
            contents=[{
                "role": "user",
                "parts": [types.Part.from_text(text=prompt)]
            }]
        )
        return response.text
    except Exception as e:
        logger.error(f"Error in LLM call [main]: {e}")
        if str(e).find("429 Resource has been exhausted") != -1:
            return _oai_call_llm(prompt, fallback_model)
        raise e
