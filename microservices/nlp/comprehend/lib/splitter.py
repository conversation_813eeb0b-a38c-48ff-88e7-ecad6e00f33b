from typing import TypedDict, List

import spacy
from loguru import logger

nlp_sm = spacy.load("en_core_web_sm")


def is_question(sent):
    doc = nlp_sm(sent)
    token = doc[0]  # gets the first token in a sentence
    last_token = doc[len(doc) - 1]  # gets the last token in a sentence

    # checks if the last token is a question mark
    if last_token.text == "?":
        return True
    if token.pos_ == "VERB" and token.dep_ == "ROOT":  # checks if the first token is a verb and root or not
        return True
    for token in doc:  # loops through the sentence and checks for WH tokens
        if token.tag_ == "WDT" or token.tag_ == "WP" or token.tag_ == "WP$" or token.tag_ == "WRB":
            return True
    return False


def cap_sentences(doc, limit: int):
    for sent in doc.sents:
        if len(sent) < limit:
            yield sent
            continue

        offset = 0
        while offset < len(sent):
            yield sent[offset:offset + limit]
            offset += limit


class SpanSections(TypedDict):
    page: int
    line_start: int
    line_end: int
    lines: List[str]
    summary: str


class Sections(TypedDict):
    title: str
    summary: str
    spans: List[SpanSections]


def resolve_sections(pages, sects) -> List[Sections]:
    """
    Extracts sections from an array of pages with line numbers.
    Args:
        pages:  List of pages with line numbers.
        sects:  List of sections with title, span, and summary.

    Returns: List of sections with title, span, content, and summary.
    """
    try:
        sections = []
        for ix, section in enumerate(sects):
            span_content = []
            for ixi, span in enumerate(section["span"]):
                page_no = span["page"]
                line_items = span["lines"].split(",")

                for line_item in line_items:
                    lines_nos = line_item.split("-")

                    start = int(lines_nos[0].replace("L", "")) - 3
                    end = start + 1
                    if "-" in line_item:
                        end = int(lines_nos[1].replace("L", "")) - 3

                    page_content = pages[page_no - 1].split("\n")
                    page_lines = page_content[start:end]

                    span_content.append({
                        "page": page_no,
                        "line_start": start,
                        "line_end": end,
                        "lines": page_lines,
                        "summary": span.get("content", "")
                    })

            sections.append({
                "title": section.get("title", ""),
                "summary": section.get("summary", ""),
                "spans": span_content
            })
        return sections
    except Exception as e:
        logger.error(f"Error: {e}")
        raise e


def split_content(content: str, lower_limit: int = 50, upper_limit: int = 200, sentence_buffer: int = 3):
    doc = nlp_sm(content)

    sents = []
    for sent in cap_sentences(doc, upper_limit):
        token_count = len(sent)
        text = sent.text.strip().replace("\n", " ")
        # print(token_count, text)

        is_que = is_question(text)

        curr_sent = {
            'text': text,
            'token_count': token_count,
            'is_question': is_que,
            'parts': [{'text': text, 'token_count': token_count}]
        }

        # join sentences based on the token count, if it is a question append the next sentence to avoid missing answers
        if len(sents) > 0:
            last_sent = sents[len(sents) - 1]
            last_sent_token_count = last_sent['token_count']
            if (last_sent_token_count <= lower_limit) or last_sent['is_question']:
                last_sent['text'] += f" {text}"
                last_sent['token_count'] += token_count
                last_sent['is_question'] = is_que
                last_sent['parts'].append({"text": text, "token_count": token_count})
            else:
                sents.append(curr_sent)
        else:
            sents.append(curr_sent)

    # buffer the sentences with n extra sentences at the beginning and end
    buffered_sents = []
    for i, sent in enumerate(sents):
        del sent['is_question']

        if i != 0:
            prev_sent = sents[i - 1]
            # get the last n sentences

            prev_parts = prev_sent['parts'][-sentence_buffer * 2:-sentence_buffer]
            part_text = ' '.join([x['text'] for x in prev_parts])
            sent['text'] = f"{part_text} {sent['text']}"

            # this helps in debugging the aggregation
            sent['token_count'] += sum([x['token_count'] for x in prev_parts])
            sent['parts'] = prev_parts + sent['parts']

        if i != (len(sents) - 1):
            next_sent = sents[i + 1]
            # get the first n sentences
            next_parts = next_sent['parts'][:sentence_buffer]
            part_text = ' '.join([x['text'] for x in next_parts])
            sent['text'] = f"{sent['text']} {part_text}"

            # this helps in debugging the aggregation
            sent['token_count'] += sum([x['token_count'] for x in next_parts])
            sent['parts'] = sent['parts'] + next_parts

        buffered_sents.append(sent)

    return buffered_sents
    # return buffered_sents, sents


# if __name__ == '__main__':
#     import os
#     import json
#
#     with open(os.path.join('../pipelines/tmp/240406/_pages.json'), 'r') as f:
#         pages = json.load(f)
#
#     with open(os.path.join('../pipelines/tmp/240406/_sections.json'), 'r') as f:
#         sections = json.load(f)
#
#     outcome = resolve_sections(pages, sections)
#
#     with open(os.path.join('../pipelines/tmp/240406/_sections_filled.json'), 'w') as f:
#         json.dump(outcome, f)
#
#     # with open(os.path.join('../transcripts/hv/britney.txt'), 'r') as f:
#     #     text = f.read()
#
#     # chunks = split_transcript(text, lower_limit=40, upper_limit=50, sentence_buffer=2)
#     # chunks, sents = split_transcript(text, lower_limit=40, upper_limit=150, sentence_buffer=4)
#
#     # with open(os.path.join('../transcripts/hv/brit_sents.json'), 'w') as f:
#     #     json.dump(sents, f)
#     #
#     # with open(os.path.join('../transcripts/hv/brit_spacey_sents.txt'), 'w') as f:
#     #     f.write("\n".join([f"[{x['token_count']}]  - {x['text'].rstrip()}" for x in sents]))
#     #
#     # with open(os.path.join('../transcripts/hv/brit_buffer.json'), 'w') as f:
#     #     json.dump(chunks, f)
#     #
#     # with open(os.path.join('../transcripts/hv/brit_spacey_sents_buffered.txt'), 'w') as f:
#     #     f.write("\n".join([f"[{x['token_count']}]  - {x['text'].rstrip()}" for x in chunks]))
