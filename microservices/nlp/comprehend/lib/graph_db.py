import typing

import google.generativeai as genai
from graphiti_core import <PERSON><PERSON><PERSON><PERSON>
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient
from graphiti_core.embedder import OpenAIEmbedderConfig, OpenAIEmbedder
from graphiti_core.llm_client import LLMConfig
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.prompts import Message
from loguru import logger
from openai import AsyncOpenAI, BaseModel

from comprehend.pipelines import TEMPERATURE, MAX_TOKENS
from comprehend.utils import extract_json
from comprehend.utils.constants import OPENAI_MODEL
from config import settings

genai.configure(api_key=settings.GEMINI_API_KEY)

class BuiltLLMClient(OpenAIGenericClient):

    def __init__(
            self, config: LLMConfig | None = None, cache: bool = False, client: typing.Any = None
    ):
        config = LLMConfig(
            model=config.model,
            temperature=config.temperature,
            max_tokens=config.max_tokens,
            api_key=config.api_key,
            base_url=config.base_url,
        )
        super().__init__(config, cache, client)

    async def __generate_response(
            self,
            messages: list[Message],
            response_model: type[BaseModel] | None = None,
            max_tokens: int | None = None,
    ) -> dict[str, typing.Any]:
        try:
            content = {"system": "", "messages": []}
            for m in messages:
                m.content = self._clean_input(m.content)
                if m.role == 'user':
                    content['messages'].append({'role': 'user', 'parts': [m.content]})
                elif m.role == 'system':
                    content['system'] += m.content

            model = genai.GenerativeModel(
                model_name=self.config.model,
                generation_config={"temperature": self.config.temperature},
                system_instruction=content['system'])

            result = await model.generate_content_async(contents=content['messages'])
            print('llm - result', result.text)
            return extract_json(result.text)
        except Exception as e:
            logger.error(f'Error in generating LLM response: {e}')
            raise


class BuiltEmbedder(OpenAIEmbedder):
    def __init__(self, config: OpenAIEmbedderConfig | None = None):
        config = OpenAIEmbedderConfig(
            base_url=config.base_url,
            api_key=config.api_key,
            embedding_model=config.embedding_model or 'text-embedding-004'
        )
        super().__init__(config)

    # async def create(self, input_data: any) -> list[float]:
    #     response = await self.client.embed_content_async(
    #         model=self.config.embedding_model,
    #         content=input_data,
    #         output_dimensionality=self.config.embedding_dim
    #     )
    #     return response.get('embedding')



class BuiltRerankerClient(OpenAIRerankerClient):

    def __init__(self, config: LLMConfig | None = None):
        config = LLMConfig(
            model=config.model,
            temperature=config.temperature,
            max_tokens=config.max_tokens,
            api_key=config.api_key,
            base_url=config.base_url,
        )
        super().__init__(config)
        self.client = AsyncOpenAI(
            base_url=config.base_url,
            api_key=config.api_key
        )


graph = Graphiti(
    uri=settings.GRAPH_HOST,
    user=settings.GRAPH_USER,
    password=settings.GRAPH_PASSWORD,
    llm_client=BuiltLLMClient(
        config=LLMConfig(
            model=OPENAI_MODEL,
            temperature=TEMPERATURE,
            max_tokens=MAX_TOKENS,
            api_key=settings.FIREWORKS_API_KEY,
            base_url=settings.FIREWORKS_API_BASE
        )
    ),
    embedder=BuiltEmbedder(
        config=OpenAIEmbedderConfig(
            base_url=settings.OPENAI_API_BASE,
            api_key=settings.OPENAI_API_KEY,
            embedding_model='text-embedding-3-small'
        )
    ),
    cross_encoder=BuiltRerankerClient(
        config=LLMConfig(
            model=OPENAI_MODEL,
            temperature=TEMPERATURE,
            max_tokens=MAX_TOKENS,
            api_key=settings.FIREWORKS_API_KEY,
            base_url=settings.FIREWORKS_API_BASE
        )
    )
)
